#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的AssetProfitLossHistory amount字段测试脚本
演示实际应用中的最佳实践
"""

import pymysql
from decimal import Decimal, InvalidOperation
from datetime import date
import os

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
        user='uat_coinex_backend_db_user',
        password='lVLG96Yvstxb0nez',
        database=f'coinex_backend_{os.getenv("ENV_NUM", "1")}',
        charset='utf8mb4',
        autocommit=False
    )

def validate_amount_field(amount_str):
    """
    验证amount字段的值是否符合DECIMAL(26,12)的要求
    
    Args:
        amount_str: 要验证的数值字符串
        
    Returns:
        tuple: (is_valid, processed_value, error_message)
    """
    
    # 检查NULL或空值
    if amount_str is None:
        return False, None, "amount字段不能为NULL"
    
    if amount_str == "":
        return False, None, "amount字段不能为空字符串"
    
    # 尝试转换为Decimal
    try:
        decimal_value = Decimal(str(amount_str))
    except (InvalidOperation, ValueError):
        return False, None, f"无效的数值格式: {amount_str}"
    
    # 检查是否为特殊值
    if not decimal_value.is_finite():
        return False, None, f"不支持无穷大或NaN值: {amount_str}"
    
    # 检查精度 - DECIMAL(26,12)
    max_value = Decimal("99999999999999.999999999999")
    min_value = Decimal("-99999999999999.999999999999")
    
    if decimal_value > max_value:
        return False, None, f"数值超出最大范围: {amount_str} > {max_value}"
    
    if decimal_value < min_value:
        return False, None, f"数值超出最小范围: {amount_str} < {min_value}"
    
    # 检查小数位数
    sign, digits, exponent = decimal_value.as_tuple()
    if exponent < -12:  # 小数位数超过12位
        return False, None, f"小数位数超过12位: {amount_str}"
    
    # 检查总位数 (去除符号位)
    if len(digits) > 26:
        return False, None, f"总位数超过26位: {amount_str}"
    
    return True, decimal_value, None

def safe_insert_asset_profit_loss_history(asset, user_id, report_date, profit, price, amount, record_type="system"):
    """
    安全地插入AssetProfitLossHistory记录，包含完整的验证
    
    Args:
        asset: 资产名称
        user_id: 用户ID
        report_date: 报告日期
        profit: 利润
        price: 价格
        amount: 数量 (将被验证)
        record_type: 记录类型
        
    Returns:
        tuple: (success, record_id_or_error_message)
    """
    
    # 验证amount字段
    is_valid, validated_amount, error_msg = validate_amount_field(amount)
    if not is_valid:
        return False, f"amount字段验证失败: {error_msg}"
    
    # 同样验证其他DECIMAL字段
    profit_valid, validated_profit, profit_error = validate_amount_field(profit)
    if not profit_valid:
        return False, f"profit字段验证失败: {profit_error}"
    
    price_valid, validated_price, price_error = validate_amount_field(price)
    if not price_valid:
        return False, f"price字段验证失败: {price_error}"
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 插入SQL语句
        insert_sql = """
        INSERT INTO asset_profit_loss_history 
        (asset, user_id, report_date, profit, price, amount, type, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_sql, (
            asset,
            user_id,
            report_date,
            str(validated_profit),
            str(validated_price),
            str(validated_amount),
            record_type
        ))
        
        # 提交事务
        conn.commit()
        
        # 获取插入的记录ID
        record_id = cursor.lastrowid
        
        return True, record_id
        
    except Exception as e:
        # 回滚事务
        conn.rollback()
        return False, f"数据库插入失败: {str(e)}"
    
    finally:
        cursor.close()
        conn.close()

def demo_safe_insertion():
    """演示安全插入的使用"""
    
    print("演示AssetProfitLossHistory安全插入功能")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "正常数据",
            "data": {
                "asset": "BTC",
                "user_id": 777777,
                "report_date": date.today(),
                "profit": "1000.12345678",
                "price": "50000.123456789012",
                "amount": "0.123456789012"
            }
        },
        {
            "name": "边界值数据",
            "data": {
                "asset": "ETH",
                "user_id": 777777,
                "report_date": date.today(),
                "profit": "99999999999999.999999999999",
                "price": "5000.123456789012",
                "amount": "99999999999999.999999999999"
            }
        },
        {
            "name": "超出精度的数据",
            "data": {
                "asset": "USDT",
                "user_id": 777777,
                "report_date": date.today(),
                "profit": "100.12345678",
                "price": "1.0",
                "amount": "999999999999999.123456789012"  # 超出整数位数
            }
        },
        {
            "name": "无效数据",
            "data": {
                "asset": "INVALID",
                "user_id": 777777,
                "report_date": date.today(),
                "profit": "100.12345678",
                "price": "1.0",
                "amount": "abc123"  # 无效格式
            }
        },
        {
            "name": "NULL数据",
            "data": {
                "asset": "NULL_TEST",
                "user_id": 777777,
                "report_date": date.today(),
                "profit": "100.12345678",
                "price": "1.0",
                "amount": None  # NULL值
            }
        }
    ]
    
    successful_inserts = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print(f"amount值: {test_case['data']['amount']}")
        
        success, result = safe_insert_asset_profit_loss_history(**test_case['data'])
        
        if success:
            print(f"✅ 插入成功，记录ID: {result}")
            successful_inserts.append(result)
        else:
            print(f"❌ 插入失败: {result}")
        
        print("-" * 40)
    
    # 清理测试数据
    if successful_inserts:
        print(f"\n清理 {len(successful_inserts)} 条测试数据...")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            for record_id in successful_inserts:
                cursor.execute("DELETE FROM asset_profit_loss_history WHERE id = %s", (record_id,))
            conn.commit()
            print("✅ 测试数据清理完成")
        except Exception as e:
            conn.rollback()
            print(f"⚠️  清理失败: {str(e)}")
        finally:
            cursor.close()
            conn.close()

if __name__ == "__main__":
    demo_safe_insertion()
