# -*- coding: utf-8 -*-
from datetime import date
import os
import sys

import requests
from tqdm import tqdm


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    import time
    from app.models import User, ZendeskUserTags, db
    from app.schedules.zendesk_user_tags import TAG_PREFIX
    from app.utils import today
    from app import config

    def update_db_tags():
        rows = ZendeskUserTags.query.filter(
            ZendeskUserTags.tags != ''
        ).all()
        # 更新zendesk的标签
        i = 0
        for row in rows:
            if not row.tags:
                continue
            i += 1
            # row.tags = row.tags.replace(TAG_PREFIX, '')
            new_tags = row.tags.split(';')
            row.tags = ';'.join([f'{TAG_PREFIX}{x}' for x in new_tags])
            if i % 5000 == 0:
                db.session.commit()
        db.session.commit()

    def update_zendesk_tags():
        LIMIT = 100
        ZENDESK_CONFIG = config['ZENDESK_CONFIG']
        print('update_zendesk_user_tags')
        today_ = today()
        result = ZendeskUserTags.query.filter(ZendeskUserTags.tags != '').all()
        tmp_update_list = [dict(id=i.zendesk_id, tags=i.tags) for i in result]
        print(f'{len(tmp_update_list)} users to update')
        progress_bar = tqdm(total=len(tmp_update_list))  # 进度条
        # 这里直接覆盖更新，后续运营手动处理【手动添加的标签】
        for i in range(0, len(tmp_update_list), LIMIT):
            update_data = dict(users=tmp_update_list[i: i + LIMIT])
            progress_bar.update(len(update_data))
            requests.put(ZENDESK_CONFIG['update_url'],
                         json=update_data,
                         auth=(ZENDESK_CONFIG['user'], ZENDESK_CONFIG['token'])).json()
            time.sleep(10)

    print('update_model_user_tags')
    update_db_tags()
    print('update_zendesk_tags')
    update_zendesk_tags()  # 预计 15min 执行完毕
    print('done.')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
