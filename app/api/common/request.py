# -*- coding: utf-8 -*-

from enum import Enum
from itertools import chain as chain_iter
from datetime import tzinfo, timedelta, timezone
from decimal import Decimal
from json import loads as json_loads
from typing import Callable, Optional, Union, Any

from flask import request, g, current_app
from flask_api.exceptions import AuthenticationFailed
from flask_babel import gettext
from user_agents.parsers import parse as parse_user_agent, UserAgent


from app.exceptions import SubAccountNotAllowed, AuthenticationTimeout, WebauthnVerificationFailed, \
    NotSupportedRegionInOnlyWithdrawal, NotSupportedByCountryNotKYC
from app.exceptions.legacy import AccessIdDoesNotExist
from app.models.system import LocalAreasBlock
from ...business.user import require_user_not_only_withdrawal
from ...caches.admin import (
    AdminUserOperationTokenCache, AdminUserWebAuthnOperationTokenCache,
    AdminWebAuthnOperationType,
)

from ...caches.system import SystemMaintainManagerCache, CountryStateCache
from ...caches.user import UserVisitPermissionCache

from ...exceptions import (
    InvalidPlatform, InvalidMobileVerificationCode,
    InvalidTotpVerificationCode, TwoFactorAuthenticationFailed,
    EmailCodeVerificationFailed, ServerInMaintainMode,
    EmailAlreadyExists, EmailDoesNotExist
)
from ...common import Language, TwoFAType, CAPTCHA_VALIDATION_LIMIT, MobileCodeType, get_country
from .validators import MobileCodeValidator, TotpCodeValidator, WebauthnCredentialValidator
from ...caches import EmailCodeTokenCache, UserOperationTokenCache
from ...models import User
from ...utils import current_timestamp, GeoIP
from ...business import UserPreferences, CountrySettings, BusinessSettings
from ...business.clients import monitor_client


class RequestPlatform(Enum):

    WEB = 'web'
    IOS = 'iOS'
    IOS_APP_STORE = 'iOS_appstore'
    ANDROID = 'Android'
    ANDROID_GOOGLE_PLAY = 'Android_GooglePlay'
    # 原先的APP也被下架了，重新添加两个字段区分上架
    IOS_LITE = 'iOSLite'
    IOS_LITE_APP_STORE = 'iOSLite_appstore'

    def is_web(self):
        return self is self.WEB

    def is_ios(self):
        return self in [self.IOS, self.IOS_APP_STORE, self.IOS_LITE, self.IOS_LITE_APP_STORE]

    def is_android(self):
        return self in [self.ANDROID, self.ANDROID_GOOGLE_PLAY]

    def is_mobile(self):
        return self.is_ios() or self.is_android()

    @classmethod
    def app_list(cls):
        return [cls.IOS, cls.IOS_APP_STORE, cls.IOS_LITE, cls.IOS_LITE_APP_STORE, cls.ANDROID, cls.ANDROID_GOOGLE_PLAY]

    @classmethod
    def from_request(cls) -> 'RequestPlatform':
        platform = request.headers.get('PLATFORM', '')
        if platform:
            try:
                platform = RequestPlatform(platform)
            except ValueError:
                raise InvalidPlatform(platform)
        else:
            platform = cls.WEB
        return platform


class RequestPermissionCheck(Enum):
    IP = 'IP'
    USER_NOT_ONLY_WITHDRAWAL = 'USER_NOT_ONLY_WITHDRAWAL'
    KYC = 'KYC'


def get_request_info() -> dict:
    return dict(
        path=request.path,
        method=request.method,
        ip=get_request_ip(),
        platform=get_request_platform().name,
        build=get_request_build(),
        language=get_request_language().name,
        args=dict(request.args)
    )


def get_request_ip() -> str:
    headers = request.headers
    if 'Cf-Connecting-Ip' in headers:     # 来自CloudFlare
        return headers['Cf-Connecting-IP']
    if 'CloudFront-Viewer-Address' in headers:     # 来自AWS CloudFront
        return headers['CloudFront-Viewer-Address'].split(':')[0]
    if 'X-Real-IP' in headers:            # 来自nginx设置
        return headers['X-Real-IP']
    if 'X-Forwarded-For' in headers:      # 来自api_gateway等设置
        return headers['X-Forwarded-For'].split(',')[0]
    return request.remote_addr


def get_request_platform() -> RequestPlatform:
    return RequestPlatform.from_request()


def get_request_build() -> int:
    return int(request.headers.get('BUILD', 0))


def get_request_version() -> str:
    return request.headers.get('version', '')


def get_request_host_url():
    return request.host_url


def get_request_language(use_default_val: bool = True) -> Union[Language, None]:
    for lang in chain_iter(request.accept_languages.values(),
                           [request.cookies.get('lang')]):
        if not lang:
            continue
        try:
            lang = Language(lang)
        except ValueError:
            continue
        break
    else:
        if use_default_val:
            lang = Language.DEFAULT
        else:
            lang = None
    return lang


def get_request_timezone() -> Optional[tzinfo]:
    if (tz := request.headers.get('timezone')) is None:
        return None
    return timezone(timedelta(minutes=int(Decimal(tz) * 60)))


def get_request_user_agent() -> str:
    return request.headers.get('User-Agent', '')


def get_request_user_agent_parsed() -> UserAgent:
    return parse_user_agent(get_request_user_agent())


def get_request_user(allow_none=True, allow_sub_account=True) -> Optional[User]:
    from .decorators import require_login

    try:
        return require_login(lambda: g.user, allow_sub_account=allow_sub_account)()
    except (AuthenticationFailed, SubAccountNotAllowed):
        if not allow_none:
            raise
    return None


def get_api_request_user(allow_none=True, allow_sub_account=True) -> Optional[User]:
    from .decorators import require_api_v2_auth

    try:
        return require_api_v2_auth(lambda: g.user, allow_sub_account=allow_sub_account)()
    except (AccessIdDoesNotExist, SubAccountNotAllowed):
        if not allow_none:
            raise
    return None


def get_request_auth_user(allow_none=True) -> Optional[User]:
    from .decorators import require_login

    try:
        return require_login(lambda: g.auth_user)()
    except AuthenticationFailed:
        if not allow_none:
            raise
    return None


def get_admin_request_user(allow_none=True) -> Optional[User]:
    from .decorators import require_admin_login
    try:
        return require_admin_login(lambda: g.user)()
    except AuthenticationFailed:
        if not allow_none:
            raise
    return None


def try_verify_request_mobile_code(user: User, code_type: MobileCodeType) -> Union[bool, Callable]:
    """尝试验证验证码， 如果没有验证码传参，则不校验"""
    if not user.mobile:
        return False
    # 支持在header或body中传递 Sms-Captcha: {"validate_code": "xxxxxx"} 或 Sms-Captcha: xxxxxx
    code = None
    headers = request.headers
    if (value := headers.get('SMS_CAPTCHA') or headers.get('Sms-Captcha')):
        if value.isdigit():
            code = value
        else:
            try:
                value = json_loads(value)
            except ValueError:
                raise InvalidMobileVerificationCode
            code = value.get('validate_code')
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('sms_captcha')):
        # 这里不能直接判断 request.json，flask 2.1 之后，如果没有 body 会报错
        if isinstance(value, (int, str)):
            code = value
        elif isinstance(value, dict):
            code = value.get('validate_code')
    else:
        return False

    if not code or not isinstance(code, (int, str)):
        raise InvalidMobileVerificationCode
    if isinstance(code, int):
        code = str(code)
    elif not code.isdigit():
        raise InvalidMobileVerificationCode
    callback = MobileCodeValidator(
            user.mobile_country_code,
            user.mobile_num,
            code_type
        ).validate(code, delete=False)
    UserPreferences(user.id).two_fa_type = TwoFAType.MOBILE
    g.success_2fa_type = TwoFAType.MOBILE
    return callback


def verify_request_mobile_code(user: User, code_type: MobileCodeType) -> Callable:
    cb = try_verify_request_mobile_code(user, code_type)
    if not cb:
        raise InvalidMobileVerificationCode
    return cb


def try_verify_request_totp_code(user: User) -> Union[bool, Callable]:
    from app.business.security import SecuritySettingType, update_security_statistics
    if not user.totp_auth_key:
        return False

    code = None
    headers = request.headers
    if (value := headers.get('TOTP_CAPTCHA') or headers.get('Totp-Captcha')):
        if value.isdigit():
            code = value
        else:
            try:
                value = json_loads(value)
            except ValueError:
                raise InvalidTotpVerificationCode
            code = value.get('validate_code')
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('totp_captcha')):
        if isinstance(value, (int, str)):
            code = value
        elif isinstance(value, dict):
            code = value.get('validate_code')
    else:
        return False

    if not code or not isinstance(code, (int, str)):
        raise InvalidTotpVerificationCode
    if isinstance(code, int):
        code = str(code)
    elif not code.isdigit():
        raise InvalidTotpVerificationCode
    callback = TotpCodeValidator(user).validate(code, delete=False)
    UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
    g.success_2fa_type = TwoFAType.TOTP
    update_security_statistics([user.id], SecuritySettingType.TOTP)
    return callback


def verify_request_totp_code(user: User) -> Callable:
    cb = try_verify_request_totp_code(user)
    if not cb:
        raise InvalidTotpVerificationCode
    return cb


def try_verify_request_webauthn_credential(user: User) -> Union[bool, Callable]:
    from app.business.security import SecuritySettingType, update_security_statistics
    if not user.web_authn_list:
        return False
    headers = request.headers
    if value := headers.get('Webauthn-Captcha'):
        try:
            value = json_loads(value)
        except ValueError:
            raise WebauthnVerificationFailed
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('webauthn_captcha')):
        if not isinstance(value, dict):
            raise WebauthnVerificationFailed
    else:
        return False
    WebauthnCredentialValidator(user).validate(value)
    UserPreferences(user.id).two_fa_type = TwoFAType.WEBAUTHN
    g.success_2fa_type = TwoFAType.WEBAUTHN
    update_security_statistics([user.id], SecuritySettingType.WEBAUTHN)
    def cb():
        return
    return cb


def verify_request_operation_token(user: User, tfa_type: TwoFAType = None) -> Callable:
    headers = request.headers
    token = (headers.get('OPERATION_TOKEN') or headers.get('OPERATE_TOKEN')
             or headers.get('Operate-Token'))
    if token:
        try:
            int(token, 16)
        except ValueError:
            try:
                token = json_loads(token)
            except ValueError:
                raise TwoFactorAuthenticationFailed
    elif not (json := request.get_json(silent=True)):
        raise TwoFactorAuthenticationFailed
    elif not (token := json.get('operate_token')):
        raise TwoFactorAuthenticationFailed

    _require_frequency_limit('verify_request_operation_token', user.id)
    cache = UserOperationTokenCache(token)
    if tfa_type is None:
        if cache.get_user() != user.id:
            raise TwoFactorAuthenticationFailed
    else:
        user_id, _tfa_type = cache.get_user_with_2fa_type()
        if user_id != user.id or _tfa_type != tfa_type:
            raise TwoFactorAuthenticationFailed
    def cb():
        cache.delete()
    return cb


def verify_admin_request_operation_token() -> Callable:
    headers = request.headers
    _token = (headers.get('OPERATION_TOKEN') or headers.get('OPERATE_TOKEN')
             or headers.get('Operate-Token'))
    _type = (headers.get('OPERATION_TYPE') or headers.get('OPERATE_TYPE')
             or headers.get('Operate-Type'))
    if not _token or not _type:
        if not (json_data := request.get_json(silent=True)):
            raise AuthenticationTimeout

        _token = json_data.get('operation_token')
        _type = json_data.get('operation_type')

    if not _token or not _type:
        raise AuthenticationTimeout
    _cache_cls = AdminUserOperationTokenCache
    try:
        _type_enum = AdminWebAuthnOperationType[_type]
    except ValueError:
        raise AuthenticationTimeout
    _cache = AdminUserOperationTokenCache(_token, _type_enum)
    user_id = _cache.get_user()
    if user_id is None:
        raise AuthenticationTimeout
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise AuthenticationTimeout
    g.operation_user = user
    g.operation_type = _type_enum

    def cb():
        _cache.delete()
    return cb


def verify_admin_webauthn_operation_token(auth_user: User) -> Callable:
    headers = request.headers
    _token = (headers.get('WEBAUTHN_TOKEN') or headers.get('WebAuthn-Token'))
    _type = (headers.get('OPERATION_TYPE') or headers.get('OPERATE_TYPE')
             or headers.get('Operate-Type'))
    if not _token or not _type:
        if not (json_data := request.get_json(silent=True)):
            raise AuthenticationTimeout

        _token = json_data.get('webauthn_token')
        _type = json_data.get('operation_type')

    if not _token or not _type:
        raise AuthenticationTimeout
    _cache_cls = AdminUserWebAuthnOperationTokenCache
    try:
        _type_enum = AdminWebAuthnOperationType[_type]
    except ValueError:
        raise AuthenticationTimeout
    _cache = _cache_cls(_token, _type_enum)
    user_id = _cache.get_user()
    if user_id is None:
        raise AuthenticationTimeout
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise AuthenticationTimeout
    if auth_user.id != user.id:
        raise AuthenticationTimeout
    def cb():
        _cache.delete()
    return cb


def verify_request_email_token(user: User) -> Callable:
    if not user.main_user_email:
        raise EmailCodeVerificationFailed

    headers = request.headers
    token = headers.get('EMAIL_CODE_TOKEN') or headers.get('Email-Code-Token')
    if token:
        try:
            int(token, 16)
        except ValueError:
            try:
                token = json_loads(token)
            except ValueError:
                raise EmailCodeVerificationFailed
    elif not (json := request.get_json(silent=True)):
        raise EmailCodeVerificationFailed
    elif not (token := json.get('email_code_token')):
        raise EmailCodeVerificationFailed

    _require_frequency_limit('verify_request_email_token', user.id)
    cache = EmailCodeTokenCache(token)
    if cache.get_user() != user.id:
        raise EmailCodeVerificationFailed

    def cb():
        cache.delete()
    return cb


def _require_frequency_limit(key: str, arg: Union[str, Any]):
    from app.api.common.decorators import limit_frequency

    deco = limit_frequency(*CAPTCHA_VALIDATION_LIMIT, key, str(arg))
    deco(lambda: True)()


def require_email_exists(email: str) -> User:
    """used for any email validation"""
    if not email:
        raise EmailDoesNotExist
    _require_frequency_limit('verify_request_email', get_request_ip())
    monitor_client.increase('web', 'check_email_exists')
    if not (user := User.query.filter(User.email == email).first()):
        raise EmailDoesNotExist
    return user


def require_email_not_exists(email: str):
    """used for any email validation"""
    if not email:
        return
    _require_frequency_limit('verify_request_email', get_request_ip())
    monitor_client.increase('web', 'check_email_exists')
    if User.query.filter(User.email == email).first():
        raise EmailAlreadyExists


def require_ip_not_only_withdrawal():
    _ip_info = GeoIP(get_request_ip())
    ip_country_code = _ip_info.country_code
    if ip_country_code:
        country_settings = None
        # noinspection PyBroadException
        try:
            country_settings = CountrySettings(ip_country_code)
        except Exception:
            current_app.logger.warning(f"failed to get country settings for {ip_country_code}")
        if country_settings and country_settings.only_withdrawal:
            region_name = get_region_name(ip_country_code)
            raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

        block_record = LocalAreasBlock.query.filter(
            LocalAreasBlock.country_code == ip_country_code,
            LocalAreasBlock.state_code == _ip_info.state_code,
            LocalAreasBlock.type == LocalAreasBlock.Type.WITHDRAWAL_ONLY,
            LocalAreasBlock.status == LocalAreasBlock.Status.VALID
        ).first()
        if block_record:
            region_name = get_region_name(ip_country_code, _ip_info.state_code)
            raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

def get_region_name(country_code: str, state_code: str = None):
    country_info = get_country(country_code)
    if country_code and state_code:
        # 局部地区不提供服务，则展示地区名（如果是局部地区，展示地区英文名即可）
        state_map = CountryStateCache().get_state_map_by_iso2(country_info.iso_2) if country_info else {}
        region_name = state_map.get(state_code) or ""
    else:
        # 国家不提供服务，则展示国家名
        region_name = gettext(country_info.cn_name) if country_info else ""
    return region_name

def require_user_request_permission(user: User | None,
                                    permissions: list[RequestPermissionCheck] | None = None):
    """
    针对Admin Tag系统的【员工标签】增加以下权限：
    在名单里的用户，不受”用户维度“仅提现的限制；即如果用户在【员工标签】，即使对用户设置了仅提现，用户也能正常使用；
    在名单里的用户，不受Admin国家配置里仅提现的限制；即如果用户在【员工标签】，那用户使用开启了仅提现的国家的IP返问，也能正常使用；
    如果某个国家开启了“强制KYC”，【员工标签】的用户即使使用该国家IP访问平台，也不受影响，不弹窗提示，依旧能正常使用；
    如果某个国家关闭了“KYC国家允许登录”，【员工标签】的用户即使是该国家的KYC，也能正常登录，不受这个限制的影响；
    """
    if not permissions:
        _permissions = [RequestPermissionCheck.IP, RequestPermissionCheck.USER_NOT_ONLY_WITHDRAWAL, RequestPermissionCheck.KYC]
    else:
        _permissions = permissions

    if user:
        _main_user = user.main_user if user.is_sub_account else user
        passed_kyc = _main_user.kyc_status == User.KYCStatus.PASSED
        _permission_user_id = _main_user.id
        user_permission = UserVisitPermissionCache().get_user_permission(_permission_user_id)
    else:
        user_permission = None
        _permission_user_id = None
        passed_kyc = None

    if user_permission == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
        # 白名单账号全部不检查
        return

    _ip_info = GeoIP(get_request_ip())
    country_settings = None
    ip_country_code = _ip_info.country_code
    if ip_country_code:
        # noinspection PyBroadException
        try:
            country_settings = CountrySettings(ip_country_code)
        except Exception:
            current_app.logger.warning(f"failed to get country settings for {ip_country_code}")

    for _permission in _permissions:
        match _permission:
            case RequestPermissionCheck.IP:
                if country_settings and country_settings.only_withdrawal:
                    region_name = get_region_name(ip_country_code)

                    raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

                block_record = LocalAreasBlock.query.filter(
                    LocalAreasBlock.country_code == ip_country_code,
                    LocalAreasBlock.state_code == _ip_info.state_code,
                    LocalAreasBlock.type == LocalAreasBlock.Type.WITHDRAWAL_ONLY,
                    LocalAreasBlock.status == LocalAreasBlock.Status.VALID
                ).first()
                if block_record:
                    region_name = get_region_name(ip_country_code, _ip_info.state_code)
                    raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

            case RequestPermissionCheck.USER_NOT_ONLY_WITHDRAWAL:
                if _permission_user_id and user_permission == UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE:
                    require_user_not_only_withdrawal(_permission_user_id)

            case RequestPermissionCheck.KYC:
                if not _permission_user_id:
                    continue
                if  passed_kyc:
                    continue
                user_pref = UserPreferences(_permission_user_id)
                if user_pref.timezone_offset in BusinessSettings.not_require_kyc_by_timezone_offsets:
                    # 按照用户的时区判断不强制KYC限制
                    continue
                if country_settings and country_settings.kyc_required and _permission_user_id > country_settings.kyc_required_after_user_id:
                    raise NotSupportedByCountryNotKYC


def check_maintain_mode(raise_exception=True):
    cache = SystemMaintainManagerCache()
    data = cache.saving_data
    if data and data['on']:
        start_time = data['start_time']
        end_time = data['end_time']
        current_ts = current_timestamp(to_int=True)
        if start_time <= current_ts <= end_time or (end_time == 0 and current_ts >= start_time):
            if not raise_exception:
                return True
            raise ServerInMaintainMode(data=dict(
                start_time=start_time,
                end_time=end_time,
                url=data['url'],
                protect_duration=data['protect_duration']
            ))
    return False


write_required_module = [
    # 管理和维护模块
    "frontend.Maintain",        # 维护操作
    "frontend.Operation",       # 运营操作
    "v2.Maintain",              # V2维护
    "admin.",                   # 管理后台 (通配符)

    # 内部和旧版API
    "internal.",                # 内部API (通配符)
    "v1.",                      # V1 API 旧版 (通配符)

    # 托管回调接口 (包含写操作,必须走主库)
    "v2.Custody",               # V2托管
]

wirte_required_endpoints = [
    "frontend.User_login_qr_code_resource",                             # 登录二维码
    "frontend.Message_messages_resource",                               # 消息列表
    "frontend.Message_message_resource",                                # 单条消息
    "frontend.Message_mobile_code_resource",                            # 短信验证码
    "frontend.Wallet_assets_resource",                                  # 资产信息
    "frontend.User_user_resource",                                      # 用户信息
    "frontend.User_im_user_info_resource",                              # IM用户信息
    "frontend.Activities_seventh_anniversary_reward_receive_resource",  # 七周年奖励领取
    "frontend.Account_share_pop_resource",                              # 分享弹窗
    "frontend.User_non_sensitive_user_info_resource",                   # 非敏感用户信息
    "frontend.User_withdraw_password_reset_resource",                   # 提现密码重置
    "frontend.Broker_broker_report_detail_resource",                    # 经纪人报告详情

    # 阶段一例外情况
    "frontend.Tutorial_beginner_tutorial_question_resource",            # 合约新手教程答题 (UserPreferences写入)
    "frontend.Tutorial_beginner_tutorial_resource",                     # 合约新手教程视频 (UserPreferences写入)
    "frontend.Academy_details_resource",                                # Academy文章详情 (阅读计数+1)
    "frontend.Insight_details_resource",                                # Insight详情 (阅读计数+1)
    "frontend.ShortLinkApi_push_redirect_resource",                     # 短链接重定向 (点击计数更新)

    # 阶段二例外情况
    "frontend.Referral_referral_info_resource",                         # 推荐信息 (需要主库)
    "frontend.Referral_referral_basic_resource",                        # 推荐基础信息 (需要主库)

    # 阶段三例外情况
    "frontend.Auth_mobile_code_resource",                               # 手机验证码 (DB写入短信记录)
]


def _is_request_in_write_required_module():
    """检查请求是否在白名单中 (必须走主库的模块)"""
    if not request or not request.endpoint:
        return False

    for prefix in write_required_module:
        if request.endpoint.startswith(prefix):
            return True
    return False


def _is_request_in_wirte_required_endpoints():
    """检查请求是否在例外列表中"""
    if not request or not request.endpoint:
        return False

    for endpoint in wirte_required_endpoints:
        if request.endpoint == endpoint:
            return True
    return False


def auto_read_only():
    # 只支持 GET 请求
    if request.method != "GET":
        return

    # 白名单内的模块使用主库
    if _is_request_in_write_required_module():
        return

    # 白名单例外中的 endpoint 使用主库
    if _is_request_in_wirte_required_endpoints():
        return

    # 其他所有 GET 请求使用从库
    g.read_only = True


def is_old_app_request(android_build: int, ios_build: int) -> bool:
    platform = get_request_platform()
    build = get_request_build()
    if (platform.is_android() and build < android_build) or \
            (platform.is_ios() and build < ios_build):
        return True
    return False


def is_old_android_request(android_build: int) -> bool:
    platform = get_request_platform()
    build = get_request_build()
    if (platform.is_android() and build < android_build):
        return True
    return False


def get_country_by_ip():
    ip = get_request_ip()
    _ip_info = GeoIP(ip)
    if not (ip_country_code := _ip_info.country_code):
        return ""
    country = get_country(ip_country_code)
    return country.iso_3