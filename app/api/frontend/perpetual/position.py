# -*- coding: utf-8 -*-

from flask import g, current_app
from flask_babel import gettext as _
from flask_restx import fields, marshal
from webargs import fields as wa_fields

from app.business import (
    PerpetualServerClient,
    CacheLock, LockKeys, InvalidArgument, amount_to_str, PerpetualHistoryDB,
    set_settle_switch, TakeProfitStopLossPriceType
)
from app.business.clients.biz_monitor import biz_monitor
from app.business.order import VerifyPriceTool
from app.caches import PerpetualMarketCache
from app.common import (
    Language, PrecisionEnum,
    PositionSide, Decimal, position_deal_type_map,
    PerpetualMarketType, TradeType, position_margin_type_map,
    OrderBusinessType, OrderType, PerpetualEvent, SettleSwitch, OrderSideType,
)
from app.config import config
from app.exceptions.perpetual import PerpetualResponseCode
from app.utils import offset_to_page, export_xlsx, quantize_amount, format_percent
from app.business.perpetual.order import fetch_cal_fee
from .order import get_user_close_order_side
from ...common import Resource, Namespace, respond_with_code, ex_fields, \
    require_login, success
from ...common.decorators import trade_permission_validate, copy_trading_sub_user_setter, require_trade_password
from ...common.fields import PositiveDecimalField, PerpetualMarketField
from ...common.request import RequestPlatform

ns = Namespace('Perpetual')


@ns.route('/pending')
@respond_with_code
class PositionPendingResource(Resource):
    marshal_fields = {
        'mainten_margin': fields.String,
        'amount': fields.String,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(missing=None),
        )
    )
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        市场信息-仓位档位介绍-仓位维持保证金率
        """
        market = kwargs['market']
        if market and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument
        client = PerpetualServerClient()
        result = client.position_pending(g.user.id, kwargs['market'])
        return marshal(result, cls.marshal_fields)


def get_roi(x):
    if Decimal(x['amount_max_margin']) <= 0:
        return '0%'
    return format_percent(Decimal(x['profit_real']) / Decimal(x['amount_max_margin']))


@ns.route('/expect')
@respond_with_code
class PositionExpectResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            price=PositiveDecimalField(required=True),
            side=wa_fields.String(
                required=True,
                validate=lambda x: x in [v.name.lower() for v in OrderSideType]
            ),
        )
    )
    def get(cls, **kwargs):
        """获取最大可开数量"""
        market = kwargs["market"]
        price = kwargs["price"]
        side = OrderSideType[kwargs['side'].upper()]
        user_id = g.user.id
        market_info = PerpetualMarketCache().get_market_info(market)
        if not market_info:
            raise InvalidArgument
        p_client = PerpetualServerClient()
        fee_rate = fetch_cal_fee(user_id, market)
        taker_fee_rate = amount_to_str(fee_rate[TradeType.TAKER], PrecisionEnum.COIN_PLACES)
        price_str = amount_to_str(price, PrecisionEnum.PRICE_PLACES)
        data = p_client.position_expect(
            user_id, market, side, price_str, taker_fee_rate
        )
        return {"amount": data["position_expect"]}


@ns.route('/finished')
@respond_with_code
class PositionFinishedResource(Resource):
    marshal_fields = {
        'position_id': fields.Integer,
        'create_time': fields.Integer,
        'update_time': fields.Integer,
        'amount_max_margin': fields.String,
        'market': fields.String,
        'type': fields.Integer,
        'side': fields.Integer,
        'amount_max': fields.String,
        'leverage': fields.String,
        'open_val_max': fields.String,
        'open_price': fields.String,
        'profit_real': fields.String,
        'fee_asset': fields.String,
        'deal_asset_fee': fields.String,
        'first_price': fields.String,
        'latest_price': fields.String,
        'deal_all': fields.String
    }

    export_fields = {
        'market': fields.String,
        'side': ex_fields.EnumMarshalField(PositionSide),
        'profit_real': ex_fields.AmountField,
        'roi': fields.String(attribute=lambda x: get_roi(x)),
        'amount_max': ex_fields.AmountField,
        'first_price': ex_fields.AmountField,
        'latest_price': ex_fields.AmountField,
        'update_time': ex_fields.LocalDateTimeStr,
    }

    export_headers = (
        {"field": "market", Language.ZH_HANS_CN: "合约", Language.EN_US: "Contract"},
        {"field": "side", Language.ZH_HANS_CN: "仓位类型", Language.EN_US: "Position Type"},
        {"field": "profit_real", Language.ZH_HANS_CN: "总盈亏", Language.EN_US: "Cml.PNL"},
        {"field": "roi", Language.ZH_HANS_CN: "收益率", Language.EN_US: "ROI"},
        {"field": "amount_max", Language.ZH_HANS_CN: "历史最大数量", Language.EN_US: "ATH Amount"},
        {"field": "first_price", Language.ZH_HANS_CN: "进入价格", Language.EN_US: "Entry Price"},
        {"field": "latest_price", Language.ZH_HANS_CN: "离开价格", Language.EN_US: "Exit Price"},
        {"field": "update_time", Language.ZH_HANS_CN: "平仓时间", Language.EN_US: "Closing Time"},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        market_type=ex_fields.EnumField([str(i.value) for i in PerpetualMarketType], missing=0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        side=ex_fields.EnumField([str(i.value) for i in PositionSide], missing=0),
        export=wa_fields.Integer(missing=0)
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        历史持仓
        """
        market = kwargs['market']
        page = kwargs['page']
        limit = kwargs['limit']
        side = int(kwargs['side'] or 0)
        export = kwargs['export']
        market_detail_mapper = PerpetualMarketCache().read_aside()
        if market and market not in market_detail_mapper.keys():
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
            )
        if market_type := kwargs['market_type']:  # market_type is str
            market = [k for k, v in market_detail_mapper.items() if v['type'] == int(market_type)]
        if export:
            page, limit = 1, config['EXPORT_ITEM_MAX_COUNT']
        client = PerpetualServerClient()
        result = client.position_finished(
            g.user.id, market, kwargs['start_time'],
            kwargs['end_time'], page=page, limit=limit, side=side)
        if export:
            return export_xlsx(
                filename='perpetual-position-history',
                data_list=marshal(result['records'], cls.export_fields),
                export_headers=cls.export_headers
            )
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)


@ns.route('/funding')
@respond_with_code
class PositionFundingResource(Resource):

    DETAIL_MODEL = ns.model('PositionFunding', {
        'user_id': fields.Integer(example=476),
        'market': fields.String(example='BTCUSD'),
        'asset': fields.String(example='BTC'),
        'type': fields.Integer(example='1   # 1-限价单，2-市价单'),
        'position_id': fields.Integer(example=220),
        'side': fields.Integer(example='2   # 1-卖，2-买'),
        'amount': fields.String(example='30 # 仓位数量'),
        'price': fields.String(example='11459.2'),
        'funding_rate': fields.String(example='-0.001   # 资金费率'),
        'funding': fields.String(example='0.00000261795762356883    # 资金费用'),
        'real_funding_rate': fields.String(example='-0.001  # 实际资金费率'),
        'value': fields.String(example='0.00261798  # 仓位价值'),
    })

    LIST_MODEL = ns.model('PositionFundings', {
        'data': fields.Nested(DETAIL_MODEL, as_list=True)
    })

    export_fields = {
        'time': ex_fields.LocalDateTimeStr,
        'market': fields.String,
        'side': ex_fields.EnumMarshalField(PositionSide),
        'value': ex_fields.AmountField,
        'real_funding_rate': ex_fields.AmountField,
        'funding': ex_fields.AmountField,
    }

    export_headers = (
        {"field": "time", Language.ZH_HANS_CN: "时间",
         Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "合约",
         Language.EN_US: "Contract"},
        {"field": "side", Language.ZH_HANS_CN: "仓位类型",
         Language.EN_US: "Position Type"},
        {"field": "value", Language.ZH_HANS_CN: "仓位价值",
         Language.EN_US: "Position Value"},
        {"field": "real_funding_rate", Language.ZH_HANS_CN: "资金费率",
         Language.EN_US: "Funding Rate"},
        {"field": "funding", Language.ZH_HANS_CN: "金额",
         Language.EN_US: "Amount"},
    )

    marshal_fields = {
        'user_id': fields.Integer,
        'time': fields.Float,
        'market': fields.String,
        'asset': fields.String,
        'type': fields.Integer,
        'position_id': fields.Integer,
        'side': fields.Integer,
        'amount': ex_fields.AmountField,
        'value': ex_fields.AmountField,
        'price': ex_fields.PriceField,
        'liq_price': ex_fields.LiqPriceField,
        'funding_rate': ex_fields.AmountField,
        'real_funding_rate': ex_fields.AmountField,
        'funding': ex_fields.AmountField,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        market_type=ex_fields.EnumField([str(i.value) for i in PerpetualMarketType], missing=0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        side=ex_fields.EnumField([str(i.value) for i in PositionSide], missing=0),
        export=wa_fields.Integer(missing=0)
    ))
    @copy_trading_sub_user_setter(require_running=False)
    @ns.response(200, 'Success', LIST_MODEL)
    def get(cls, **kwargs):
        """
        资金费用
        """
        market = kwargs['market']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        export = kwargs['export']
        side = int(kwargs['side'] or 0)
        if export:
            page = 1
            limit = config['EXPORT_ITEM_MAX_COUNT']

        market_detail_mapper = PerpetualMarketCache().read_aside()
        if market_type := kwargs['market_type']:  # market_type is str
            market = [k for k, v in market_detail_mapper.items() if v['type'] == int(market_type)]

        offset = (page - 1) * limit
        result = PerpetualHistoryDB.get_position_funding_history(
            g.user.id, offset=offset, limit=limit + 1,
            start_time=start_time, end_time=end_time,
            market=market, side=side, include_zero=False
        )
        for r in result:
            market_info = market_detail_mapper.get(r['market'])
            if not market_info:
                value = Decimal()
            else:
                if market_info['type'] == PerpetualMarketType.INVERSE.value:
                    value = Decimal(r['amount']) / Decimal(r['price'])
                else:
                    value = Decimal(r['amount']) * Decimal(r['price'])
            r['value'] = quantize_amount(value, PrecisionEnum.COIN_PLACES)
            r['real_funding_rate'] = cls.calc_real_funding_rate(r)
            r['time'] = float(r['time'])
        if export:
            return export_xlsx(
                filename='perpetual-position-funding-history',
                data_list=marshal(result, cls.export_fields),
                export_headers=cls.export_headers
            )
        res = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(result, cls.marshal_fields)
        }
        return offset_to_page(res)

    @classmethod
    def calc_real_funding_rate(cls, funding_data):
        r = funding_data
        if not (value := r.get('value')):
            market = PerpetualMarketCache().get_market_info(r['market'])
            if not market:
                return Decimal('0')
            if market['type'] == PerpetualMarketType.INVERSE.value:
                value = Decimal(r['amount']) / Decimal(r['price'])
            else:
                value = Decimal(r['amount']) * Decimal(r['price'])
        real_funding_rate = quantize_amount(Decimal(r['funding']) / value, 8)
        if real_funding_rate * Decimal(r['funding_rate']) < 0:  # 正负号相同
            real_funding_rate *= -1
        # 忽略计算误差
        if abs(Decimal(r['funding_rate']) - real_funding_rate) < Decimal('1e-7'):
            real_funding_rate = r['funding_rate']
        return real_funding_rate


@ns.route('/deal/detail')
@respond_with_code
class PositionDealDetailResource(Resource):

    marshal_fields = {
        'type': fields.String(
            attribute=lambda x: _(position_deal_type_map[x['deal_type']])
        ),
        'margin_amount': ex_fields.AmountField,
        'amount': ex_fields.AmountField,
        'leverage': ex_fields.AmountField,
        'price': ex_fields.PriceField,
        'liq_price': ex_fields.LiqPriceField,
        'change': fields.String(attribute='amount'),
        'create_time': fields.Integer(attribute='time'),
        'deal_fee': fields.String,
        'profit_real': ex_fields.DealProfitField(
            attribute=lambda x: (
                x['deal_profit'], x['deal_fee'], x['fee_asset'])
        ),
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['deal_margin'] if x['deal_type'] in (1, 2)
            else -x['deal_margin']
        ),
        'deal_id': fields.Integer(attribute='id'),
        'fee_asset': fields.String
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        仓位明细-加减仓
        """
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_deal_history(
            g.user.id, kwargs['position_id'], offset, limit + 1
        )
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/margin/detail')
@respond_with_code
class PositionMarginResource(Resource):

    marshal_fields = {
        'type': fields.String(
            attribute=lambda x: _(position_margin_type_map[x['type']]
                                  [x['position_type']])
            if x['type'] in (4, 5) else _(position_margin_type_map[x['type']])),
        'margin_amount': ex_fields.AmountField,
        'change': fields.String(attribute=lambda x: '-'),
        'create_time': fields.Integer(attribute='time'),
        'profit_real': fields.String(attribute=lambda x: '0'),
        'leverage': fields.String,
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['margin_change']
        ),
        'position_id': fields.Integer,
        'settle_price': ex_fields.PriceField,
        'liq_price': ex_fields.LiqPriceField,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        仓位明细-追加/减少保证金
        """
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_position_margin_history(
            g.user.id, kwargs['position_id'], offset, limit + 1)
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/funding/detail')
@respond_with_code
class FundingDetailResource(Resource):

    marshal_fields = {
        'funding_rate': ex_fields.AmountField,
        'real_funding_rate': ex_fields.AmountField,
        'funding': ex_fields.AmountField,
        'create_time': fields.Integer(attribute='time'),
        'liq_price': ex_fields.LiqPriceField,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        仓位明细-资金费用
        """
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_position_funding_history(
            g.user.id, position_id=kwargs['position_id'],
            offset=offset, limit=limit + 1, include_zero=False
        )
        for r in records:
            r['real_funding_rate'] = PositionFundingResource.calc_real_funding_rate(r)

        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/summary')
@respond_with_code
class PositionSummaryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        )
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        仓位明细-仓位信息
        """
        # funding
        funding_result = PerpetualHistoryDB.get_position_funding_summary(
            g.user.id, position_id=kwargs['position_id']
        )
        funding_amount = funding_result['total_amount']
        # deal history
        deal_fee = PerpetualHistoryDB.sum_deal_fee(
            g.user.id, kwargs['position_id']
        )
        close_price = PerpetualHistoryDB.calc_close_position_avg_price(
            g.user.id, position_id=kwargs['position_id']
        )
        return dict(
            funding_amount=funding_amount,
            deal_fee=deal_fee or Decimal(),
            close_price=close_price,
        )


@ns.route('/margin/adjust')
class PositionAdjustResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        type=wa_fields.Integer(
            required=True,
            validate=lambda x: x in (1, 2)
        ),
        amount=PositiveDecimalField(required=True),
    ))
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"])
    def post(cls, **kwargs):
        """
        加减保证金
        """
        market = kwargs['market']
        margin_type = kwargs['type']
        amount = kwargs['amount']
        user_id = g.user.id
        with CacheLock(
                LockKeys.perpetual_adjust_margin(user_id, market),
                wait=False
        ):
            if market not in PerpetualMarketCache().get_market_list():
                raise InvalidArgument
            client = PerpetualServerClient(current_app.logger)
            try:
                client.adjust_margin(
                    user_id=user_id,
                    market=market,
                    margin_type=margin_type,
                    amount=amount_to_str(amount, 8)
                )
            except Exception as e:
                if e.code == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH:
                    e.message_template = _('余额不足，调整失败')
                raise e
        return success(message=_("设置成功"))


@ns.route('/limit/config')
@respond_with_code
class PositionLimitConfigResource(Resource):

    @classmethod
    def get(cls):
        return PerpetualServerClient().position_limit_config()


@ns.route('/stop-loss')
class PositionStopLossResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True),
        price=PositiveDecimalField(required=True),
        type=wa_fields.Integer(
            required=True,
            validate=lambda x: x in (TakeProfitStopLossPriceType.DEAL_PRICE, TakeProfitStopLossPriceType.SIGN_PRICE)
        )
    ))
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"])
    @require_trade_password
    def post(cls, **kwargs):
        """
        设置止损
        """
        user_id = g.user.id
        market = kwargs['market']
        stop_type = kwargs['type']
        with CacheLock(
                LockKeys.perpetual_loss_profit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            price = quantize_amount(
                kwargs['price'], int(market_info['money_prec']))

            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)

            records = client.position_pending(user_id, market)
            if len(records) != 1 or records[0]['position_id'] != kwargs['position_id']:
                raise InvalidArgument
            position = records[0]
            status_map = client.get_market_status(market, 86400)
            if stop_type == TakeProfitStopLossPriceType.DEAL_PRICE:
                last_price = status_map['last']
            else:
                last_price = status_map['sign_price']
            last_price = Decimal(last_price)
            if position['side'] == PositionSide.LONG:
                if price > last_price:
                    raise InvalidArgument(message=_("止损价格应低于当前价格"))
            elif position['side'] == PositionSide.SHORT:
                if price < last_price:
                    raise InvalidArgument(message=_("止损价格应高于当前价格"))
            client.position_stop_loss(
                user_id=user_id,
                market=market,
                stop_loss_price=str(price),
                stop_type=stop_type,
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
            )
            biz_monitor.increase_counter(PerpetualEvent.SET_STOP_LOSS, with_source=True)

        return success(message=_("止损设置成功"))

    @staticmethod
    def _clear_stop_loss(**kwargs):
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_loss_profit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            records = client.position_pending(user_id, market)
            if len(records) != 1 or records[0]['position_id'] != kwargs['position_id']:
                raise InvalidArgument
            client.position_stop_loss(
                user_id=user_id,
                market=market,
                stop_loss_price='0',
                stop_type=records[0]['stop_loss_type'],
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8)
            )
        return success(message=_("止损撤销成功"))

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True),
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def delete(cls, **kwargs):
        """
        清除止损
        """
        return cls._clear_stop_loss(**kwargs)

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True),
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def put(cls, **kwargs):
        """
        清除止损(兼容旧版本，未来可移除)
        """
        return cls._clear_stop_loss(**kwargs)


@ns.route('/take-profit')
class PositionTakeProfitResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True),
        price=PositiveDecimalField(required=True),
        type=wa_fields.Integer(
            required=True,
            validate=lambda x: x in (TakeProfitStopLossPriceType.DEAL_PRICE, TakeProfitStopLossPriceType.SIGN_PRICE)
        )
    ))
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"])
    @require_trade_password
    def post(cls, **kwargs):
        """
        设置止盈
        """
        user_id = g.user.id
        market = kwargs['market']
        stop_type = kwargs['type']
        with CacheLock(
                LockKeys.perpetual_loss_profit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            price = quantize_amount(
                kwargs['price'], int(market_info['money_prec']))

            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)

            records = client.position_pending(user_id, market)
            if len(records) != 1 or records[0]['position_id'] != kwargs['position_id']:
                raise InvalidArgument(message=_("该仓位不存在"))
            position = records[0]
            status_map = client.get_market_status(market, 86400)
            if stop_type == TakeProfitStopLossPriceType.DEAL_PRICE:
                last_price = status_map['last']
            else:
                last_price = status_map['sign_price']
            last_price = Decimal(last_price)
            if position['side'] == PositionSide.LONG:
                if price < last_price:
                    raise InvalidArgument(message=_("止盈价格应高于当前价格"))
            elif position['side'] == PositionSide.SHORT:
                if price > last_price:
                    raise InvalidArgument(message=_("止盈价格应低于当前价格"))
            client.position_take_profit(
                user_id=user_id,
                market=market,
                take_profit_price=str(price),
                stop_type=stop_type,
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8)
            )
            biz_monitor.increase_counter(PerpetualEvent.SET_TAKE_PROFIT, with_source=True)
        return success(message=_("止盈设置成功"))

    @staticmethod
    def _clear_take_profit(**kwargs):
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(LockKeys.perpetual_loss_profit(user_id, market),
                       wait=False):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            records = client.position_pending(user_id, market)
            if len(records) != 1 or records[0]['position_id'] != kwargs['position_id']:
                raise InvalidArgument(message=_('该仓位不存在'))
            client.position_take_profit(
                user_id=user_id,
                market=market,
                take_profit_price='0',
                stop_type=records[0]['take_profit_type'],
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8)
            )
        return success(message=_("止盈撤销成功"))

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True)
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def delete(cls, **kwargs):
        """
        清除止盈
        """
        return cls._clear_take_profit(**kwargs)

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        market=wa_fields.String(required=True)
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def put(cls, **kwargs):
        """
        清除止盈(兼容旧版本，未来可移除)
        """
        return cls._clear_take_profit(**kwargs)


@ns.route('/limit')
class LimitClosePositionResource(Resource):

    effect_type_map = dict(
        normal=1,
        ioc=2,
        fok=3
    )

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            price=PositiveDecimalField(required=True),
            effect_type=wa_fields.String(required=True,
                                         validate=lambda x: x in ('normal', 'ioc', 'fok'))
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    @require_trade_password
    def delete(cls, **kwargs):
        """
        限价平仓
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_limit_close(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            price = amount_to_str(
                kwargs['price'], int(market_info['money_prec']))
            side = get_user_close_order_side(user_id, market, kwargs["position_id"])
            tool = VerifyPriceTool(
                market,
                OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                OrderType.LIMIT_ORDER_TYPE,
                side,
                Decimal(kwargs['amount']),
                kwargs['price'],
                Decimal(),
            )
            tool.validate(g.user.main_user_type)
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            client.limit_close(
                user_id=user_id,
                market=market,
                position_id=kwargs['position_id'],
                amount=str(kwargs['amount']),
                price=price,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value,
                effect_type=cls.effect_type_map[kwargs['effect_type']]
            )
        biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
        return success(message=_("下单成功"))


@ns.route('/market')
class MarketClosePositionResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            ),
            amount=PositiveDecimalField,
            type=wa_fields.String(required=True,
                                  validate=lambda x: x in ('all', 'partial'))
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    @require_trade_password
    def delete(cls, **kwargs):
        """
        市价平仓
        """
        user_id = g.user.id
        market = kwargs['market']

        if kwargs['type'] == 'all':
            # 0 代表全部平仓
            amount = Decimal()
        else:
            if not (amount := kwargs.get('amount')):
                raise InvalidArgument
        with CacheLock(
                LockKeys.perpetual_put_market(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if amount % 1 != 0:
                    raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            try:
                client.market_close(
                    user_id=user_id,
                    market=market,
                    position_id=kwargs['position_id'],
                    amount=str(amount),
                    taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                    source=RequestPlatform.from_request().value
                )
                biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
            except Exception as e:
                if (e.code == PerpetualResponseCode.CONTRACT_PRICE_LESS_LIQUIDATION_PRICE or
                        e.code == PerpetualResponseCode.CONTRACT_PRICE_HIGHER_LIQUIDATION_PRICE):
                    e.message_template = _('市场波动剧烈，无法执行市价平仓，请稍后再试')
                    raise e
        return success(message=_("下单成功"))


@ns.route('')
class CloseAllOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            )
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    @require_trade_password
    def delete(cls, **kwargs):
        """
        一键全平(拍卖式下单（目前app端在使用）)
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_limit_close(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            client.market_close_all(
                user_id=user_id,
                market=market,
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value
            )
        biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
        return success(message=_("下单成功"))


@ns.route('/settle/detail')
@respond_with_code
class SettleDetailResource(Resource):
    marshal_fields = {
        'type': fields.String(
            attribute=lambda x: _(position_margin_type_map[x['type']][x['position_type']])),
        'create_time': fields.Integer(attribute='time'),
        'leverage': fields.String,
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['margin_change']
        ),
        'position_id': fields.Integer,
        'settle_price': ex_fields.PriceField
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        position_id=wa_fields.Integer(
            required=True,
            validate=lambda x: x > 0
        ),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """仓位明细-结算记录"""
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_settle_history(
            g.user.id, kwargs['position_id'], offset, limit + 1)
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/settle/setting')
@respond_with_code
class SettleSettingResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        status=wa_fields.Integer(required=True, validate=lambda x: x in [i.value for i in SettleSwitch])
    ))
    @copy_trading_sub_user_setter(require_running=True)
    def put(cls, **kwargs):
        """设置结算开关"""
        set_settle_switch(g.user.id, kwargs['status'])
        return {}

    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """获取结算开关状态"""
        client = PerpetualServerClient()
        return client.get_settle_switch(g.user.id)


@ns.route('/auction-pending')
@respond_with_code
class AuctionPendingResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(allow_offline=False, required=True)
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """获取一键平仓相关信息"""
        client = PerpetualServerClient()
        return client.get_auction_pending(g.user.id, kwargs['market'])
