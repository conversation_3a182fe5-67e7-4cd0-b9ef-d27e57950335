# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_DOWN
from typing import List, Dict, Callable, Tuple

from flask import g
from flask_babel import gettext
from marshmallow import fields as mm_fields

from app.api.common.request import require_user_request_permission
from app.caches.system import MarketMaintainCache
from app.common import PrecisionEnum
from app.models import Market
from app.models.strategy import (
    SpotGridMarket,
    UserStrategy,
    SpotGridStrategy,
    SpotGridStrategyDetail,
    SpotGridStrategyTrace,
    StrategyProfitHistory,
    SpotGridStrategyMatchOrder,
)
from app.models.auto_invest import AutoInvestPlan, AutoInvestPlanStatistic
from app.exceptions import InvalidArgument, InsufficientBalance
from app.business import (
    ServerClient,
    mem_cached,
    cached,
    SiteSettings,
    PriceManager,
    SPOT_ACCOUNT_ID,
)
from app.business.order import Order
from app.business.strategy.base import STRATEGY_RUN_USER_NUM_LIMIT
from app.business.strategy.grid import (
    MIN_GRID_COUNT,
    MAX_GRID_COUNT,
    RECOMMEND_RUN_DAYS_LIST,
    get_market_recommend_params,
    get_market_lowest_and_highest_prices,
    calc_grid_pnl_range,
    validate_spot_grid_count,
    new_spot_grid_strategy,
    modify_spot_grid_strategy,
    terminate_spot_grid_strategy,
    update_spot_grid_strategy_profit,
    build_spot_grid_buy_sell_order_prices,
)
from app.assets.asset import get_asset_config
from app.caches import MarketCache
from app.caches.strategy import SpotGridMarketCache, StrategySubBalanceCache, SpotGridMarketsViewCache
from app.api.common import (
    Resource,
    Namespace,
    respond_with_code,
    require_login,
    lock_request,
    limit_user_frequency,
)
from app.api.common.decorators import trade_permission_validate
from app.api.common.fields import LimitField, PageField, PositiveDecimalField, EnumField, BoolField
from app.utils import group_by, quantize_amount


ns = Namespace("Strategy")


PER_GRID_USD_MAX_RATE = Decimal("0.8")  # 网格每格市值不能大于市场挂单USD的80%


@ns.route("/list")
@respond_with_code
class StrategyListResource(Resource):

    PARAM_STATUS_ENUM_STATUSES_MAP = {
        "CREATED": [UserStrategy.Status.CREATED],
        "RUNNING": [UserStrategy.Status.RUNNING],
        "PAUSED": [UserStrategy.Status.PAUSED],
        "TERMINATED": [UserStrategy.Status.TERMINATED],
    }

    @classmethod
    def enum_status_to_param_status(cls, status: UserStrategy.Status) -> str:
        for k, v in cls.PARAM_STATUS_ENUM_STATUSES_MAP.items():
            if status in v:
                return k

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(UserStrategy.Type),
            status=EnumField(list(PARAM_STATUS_ENUM_STATUSES_MAP)),
        )
    )
    def get(cls, **kwargs):
        """ 策略列表 """
        user = g.user
        q = UserStrategy.query.filter(UserStrategy.user_id == user.id)
        if type_ := kwargs.get("type"):
            q = q.filter(UserStrategy.type == type_)
        if status := kwargs.get("status"):
            enum_statuses = cls.PARAM_STATUS_ENUM_STATUSES_MAP[status]
            q = q.filter(UserStrategy.status.in_(enum_statuses))

        max_num = 100  # 不分页，最多返回100条
        if status:
            rows = q.order_by(UserStrategy.id.desc()).limit(max_num).all()
        else:
            # 无状态筛选，先查出未终止的
            rows = q.filter(
                UserStrategy.status.in_(
                    [
                        UserStrategy.Status.CREATED,
                        UserStrategy.Status.RUNNING,
                        UserStrategy.Status.PAUSED,
                    ]
                )
            ).all()
            remain_num = max_num - len(rows)
            if remain_num > 0:
                terminated_rows = q.filter(
                    UserStrategy.status == UserStrategy.Status.TERMINATED
                ).order_by(UserStrategy.id.desc()).limit(remain_num).all()
                rows.extend(terminated_rows)
        items = cls.format_strategy(rows)
        return dict(
            data=items,
            total=len(items),
        )

    @classmethod
    def format_strategy(cls, rows: List[UserStrategy]) -> List[Dict]:
        type_format_func_map: Dict[UserStrategy.Type, Callable] = {
            UserStrategy.Type.SPOT_GRID: cls.format_spot_grid_strategy,
            UserStrategy.Type.AUTO_INVEST: cls.format_auto_invest_strategy,
        }
        format_items = []
        for type_, items in group_by(lambda x: x.type, rows).items():
            if func_ := type_format_func_map.get(type_):
                format_items.extend(func_(items))

        org_ids_order = [i.id for i in rows]
        format_items.sort(key=lambda x: org_ids_order.index(x["id"]))
        return format_items

    @classmethod
    def format_spot_grid_strategy(cls, rows: List[UserStrategy]) -> List[Dict]:
        sty_ids = [r.id for r in rows]
        sty_list = SpotGridStrategy.query.filter(
            SpotGridStrategy.strategy_id.in_(sty_ids)
        ).all()
        sty_map = {i.strategy_id: i for i in sty_list}
        detail_list = SpotGridStrategyDetail.query.filter(
            SpotGridStrategyDetail.strategy_id.in_(sty_ids)
        ).all()
        sty_detail_map = {i.strategy_id: i for i in detail_list}

        # 运行中的返回base余额，用于浮动盈亏
        query_balance_user_ids = {i.run_user_id for i in rows if sty_map[i.id].is_already_triggered()}
        query_balance_user_ids = list(sorted(query_balance_user_ids))[:STRATEGY_RUN_USER_NUM_LIMIT]  # 运行中<=20
        user_balance_dict = {int(k): v for k, v in cls._get_spot_balances(user_ids=query_balance_user_ids).items()}

        format_results = []
        for r in rows:
            sty: SpotGridStrategy = sty_map[r.id]
            sty_detail: SpotGridStrategyDetail = sty_detail_map[r.id]
            item = SpotGridDetailResource.format(r, sty, sty_detail)
            base_balance_amount = quote_balance_amount = 0
            if sty.is_already_triggered() and r.run_user_id in user_balance_dict:
                base_balance = user_balance_dict[r.run_user_id].get(sty.base_asset, {})
                base_balance_amount = Decimal(base_balance["frozen"]) + Decimal(base_balance["available"]) if base_balance else 0
                quote_balance = user_balance_dict[r.run_user_id].get(sty.quote_asset, {})
                quote_balance_amount = Decimal(quote_balance["frozen"]) + Decimal(quote_balance["available"]) if quote_balance else 0
                quote_balance_amount = max(quote_balance_amount - sty_detail.grid_profit, 0)  # 当前quote持仓币数需减去网格利润
            item["base_balance_amount"] = quantize_amount(base_balance_amount, 8)
            item["quote_balance_amount"] = quantize_amount(quote_balance_amount, 8)
            format_results.append(item)
        return format_results

    @classmethod
    def _get_spot_balances(cls, user_ids: List[int]) -> Dict:
        cache = StrategySubBalanceCache(g.user.id)
        sub_balance_dict = cache.get_sub_balances()
        if missing_uids := (set(user_ids) - set(sub_balance_dict)):
            missing_res = {}
            client = ServerClient()
            for uid in missing_uids:
                sub_balances = client.get_user_balances(user_id=uid, account_id=SPOT_ACCOUNT_ID)
                sub_balances = {asset: d for asset, d in sub_balances.items() if (d['available'] + d['frozen']) > 0}
                missing_res[uid] = sub_balances
            sub_balance_dict.update(missing_res)
            cache.save_sub_balances(sub_balance_dict)
        return sub_balance_dict

    @classmethod
    @cached(60)
    def _get_market_last_prices(cls):
        tickers = ServerClient().get_all_market_tickers()
        return {m: ticker['last'] for m, ticker in tickers.items()}

    @classmethod
    def format_auto_invest_strategy(cls, rows: List[UserStrategy]) -> List[Dict]:
        plan_rows = AutoInvestPlan.query.filter(
            AutoInvestPlan.strategy_id.in_([r.id for r in rows])
        ).all()
        plan_ids = [plan.id for plan in plan_rows]
        statistic_query = AutoInvestPlanStatistic.query.filter(
            AutoInvestPlanStatistic.plan_id.in_(plan_ids)
        ).all()
        statistic_map = {statistic.plan_id: statistic for statistic in statistic_query}
        plan_list = []
        market_last_price_map = cls._get_market_last_prices()
        for plan in plan_rows:
            plan_data = dict(
                id=plan.strategy_id,
                plan_id=plan.id,
                type=UserStrategy.Type.AUTO_INVEST.name,
                created_at=plan.created_at,
                target_asset=plan.target_asset,
                source_amount=plan.source_amount,
                source_asset=plan.source_asset,
                market=plan.market,
                status=plan.status.name,
                stop_at=plan.stop_at,
            )
            plan_statistic = statistic_map[plan.id]
            plan_data['target_avg_price'] = plan_statistic.target_avg_price
            plan_data['target_end_price'] = plan_statistic.target_end_price
            plan_data['order_count'] = plan_statistic.order_count
            plan_data['total_source_amount'] = plan_statistic.total_source_amount
            plan_data['total_target_amount'] = plan_statistic.total_target_amount
            last_price = Decimal(market_last_price_map.get(plan.market, 0))
            plan_data['end_price'] = plan_data['target_end_price'] if plan_data['target_end_price'] else last_price
            plan_data['last_price'] = last_price
            plan_data['profit_amount'] = plan_data['total_target_amount'] * plan_data['end_price'] - plan_data['total_source_amount']
            plan_data['profit_rate'] = (
                plan_data['profit_amount'] / plan_data['total_source_amount'] if plan_data['total_source_amount'] else 0
            )
            plan_list.append(plan_data)
        return plan_list


@ns.route("/grid/spot/markets")
@respond_with_code
class SpotGridMarketsResource(Resource):
    @classmethod
    def get(cls):
        """ 现货网格-支持的市场列表 """
        return cls._get()

    @classmethod
    @mem_cached(120)
    def _get(cls):
        cache = SpotGridMarketsViewCache()
        markets = cache.read()
        return markets


@ns.route("/grid/spot/config")
@respond_with_code
class SpotGridConfigResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        )
    )
    def get(cls, **kwargs):
        """ 策略市场的配置 """
        market = kwargs["market"]
        return cls._get_market_config(market)

    @classmethod
    @mem_cached(60)
    def _get_market_config(cls, market: str):
        grid_market = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
        ).first()
        if not grid_market:
            raise InvalidArgument

        market_info = MarketCache(market).dict
        lowest_range, highest_range = get_market_lowest_and_highest_prices(market, market_info["quote_asset_precision"])
        max_usd = SpotGridMarketCache.get_market_max_usd(market)
        if h := highest_range[1]:
            market_max_fee = max(market_info["maker_fee_rate"], market_info["taker_fee_rate"])
            max_grid_count = int((h - lowest_range[0]) / (market_max_fee * Decimal("2") * h))
            max_grid_count = min(max(max_grid_count, MIN_GRID_COUNT), MAX_GRID_COUNT)
        else:
            max_grid_count = MIN_GRID_COUNT
        spot_grid_params = {
            "min_grid_count": MIN_GRID_COUNT,
            "max_grid_count": max_grid_count,
            "lowest_price_range": lowest_range,
            "highest_price_range": highest_range,
            "min_order_amount_mul": grid_market.min_order_amount_mul,
            "max_usd": max_usd,
            "max_grid_usd": max_usd * PER_GRID_USD_MAX_RATE,
        }
        return spot_grid_params


@ns.route("/grid/spot/recommend")
@respond_with_code
class SpotGridRecommendResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        )
    )
    def get(cls, **kwargs):
        """ 现货网格-策略参数推荐 """
        market = kwargs["market"]
        return cls._get(market)

    @classmethod
    @mem_cached(60)
    def _get(cls, market: str):
        return get_market_recommend_params(market)


@ns.route("/grid/spot")
@respond_with_code
class SpotGridListResource(Resource):

    NOT_SUPPORTED_MSG = gettext("暂不支持")

    @classmethod
    def calc_buy_sell_order_num(
        cls,
        lowest_price: Decimal,
        highest_price: Decimal,
        last_price: Decimal,
        grid_count: int,
        prec: int = 8,
    ) -> Tuple[int, int]:
        lowest_price = quantize_amount(lowest_price, prec)
        highest_price = quantize_amount(highest_price, prec)
        last_price = quantize_amount(last_price, prec)
        gap = quantize_amount((highest_price - lowest_price) / grid_count, prec)

        s = lowest_price
        all_grid_prices = []
        while s <= highest_price:
            all_grid_prices.append(s)
            s = s + gap
        all_grid_prices = all_grid_prices[: grid_count + 1]

        buy_prices, sell_prices = build_spot_grid_buy_sell_order_prices(
            grid_prices=all_grid_prices,
            grid_price_gap=gap,
            last_price=last_price,
            cmp_price=last_price,
        )
        return len(buy_prices), len(sell_prices)

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(20, 1800)
    @trade_permission_validate(is_spot=True, account_id=0)
    @ns.use_kwargs(
        dict(
            market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
            lowest_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
            highest_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
            grid_count=mm_fields.Integer(required=True),
            quote_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
            base_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True, allow_zero=True),
            trigger_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            take_profit_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            stop_loss_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            recommend_days=mm_fields.Integer(required=False, validate=lambda x: 0 <= x <= max(RECOMMEND_RUN_DAYS_LIST)),
            is_recommend=BoolField(missing=False),
        )
    )
    def post(cls, **kwargs):
        """ 现货网格-新建策略 """
        require_user_request_permission(g.user)
        grid_count = kwargs["grid_count"]
        if not validate_spot_grid_count(grid_count):
            raise InvalidArgument
        if not SiteSettings.spot_grid_strategy_enabled:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)

        market = kwargs["market"]
        market_max_usd = SpotGridMarketCache.get_market_max_usd(market)
        if not market_max_usd:
            raise InvalidArgument  # 未开启该网格市场
        market_info = MarketCache(market).dict
        if market_info["status"] != Market.Status.ONLINE or market_info["trading_disabled"]:
            # 集合竞价中不能下市价单，会导致建仓失败
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
        if ServerClient().get_protect_status()['status']:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
        market_maintains = MarketMaintainCache.get_market_maintains()
        if market in market_maintains:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)

        lowest_price = kwargs["lowest_price"]
        highest_price = kwargs["highest_price"]
        if lowest_price >= highest_price:
            raise InvalidArgument
        lowest_range, highest_range = get_market_lowest_and_highest_prices(market, market_info["quote_asset_precision"])
        if not (lowest_range[0] <= lowest_price <= lowest_range[1]):
            raise InvalidArgument
        if highest_price > highest_range[1]:
            raise InvalidArgument

        zero = Decimal()
        market_max_fee = max(market_info["maker_fee_rate"], market_info["taker_fee_rate"])
        min_pnl, max_pnl = calc_grid_pnl_range(lowest_price, highest_price, grid_count, market_max_fee)
        if min_pnl <= zero or max_pnl <= zero:
            raise InvalidArgument

        base_amount = kwargs["base_amount"]
        quote_amount = kwargs["quote_amount"]
        base_asset = market_info["base_asset"]
        quote_asset = market_info["quote_asset"]
        asset_price_map = PriceManager.assets_to_usd([base_asset, quote_asset])
        base_price = asset_price_map.get(base_asset, zero)
        quote_price = asset_price_map.get(quote_asset, zero)
        total_usd = base_amount * base_price + quote_amount * quote_price
        if total_usd > market_max_usd * Decimal("1.05") * PER_GRID_USD_MAX_RATE * grid_count:
            # 每格市值 不能大于 市场的最大挂单USD的80%
            raise InvalidArgument
        grid_market: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
            SpotGridMarket.status == SpotGridMarket.Status.ONLINE,
        ).first()
        if not grid_market:
            raise InvalidArgument

        base_min_order_amount = get_asset_config(base_asset).min_order_amount
        per_grid_min_usd = base_min_order_amount * grid_market.min_order_amount_mul * base_price / Decimal("1.05")
        if total_usd / grid_count < per_grid_min_usd:
            raise InvalidArgument

        client = ServerClient()
        last_price = client.market_last(market)
        if base_amount > zero:
            # 双币模式
            if last_price >= highest_price or last_price <= lowest_price:
                # 破网只允许单币投资
                raise InvalidArgument
            buy_order_num, sell_order_num = cls.calc_buy_sell_order_num(
                lowest_price=lowest_price,
                highest_price=highest_price,
                last_price=last_price,
                grid_count=grid_count,
                prec=market_info["quote_asset_precision"],
            )
            if buy_order_num == 0 or sell_order_num == 0:
                raise InvalidArgument
            if quote_amount * quote_price / buy_order_num < per_grid_min_usd:
                raise InvalidArgument
            if base_amount * base_price / sell_order_num < per_grid_min_usd:
                raise InvalidArgument

        user = g.user
        quote_balance_dict = client.get_user_balances(
            user_id=user.id,
            asset=quote_asset,
            account_id=SPOT_ACCOUNT_ID,
        )
        quote_available = quote_balance_dict.get(quote_asset, {}).get("available", zero)
        if quote_available < quote_amount:
            raise InsufficientBalance
        if base_amount:
            base_balance_dict = client.get_user_balances(
                user_id=user.id,
                asset=base_asset,
                account_id=SPOT_ACCOUNT_ID,
            )
            base_available = base_balance_dict.get(base_asset, {}).get("available", zero)
            if base_available < base_amount:
                raise InsufficientBalance

        if take_profit_price := kwargs.get("take_profit_price"):
            if not (take_profit_price > last_price and take_profit_price > highest_price):
                # 止盈价要高于最高价格和最新价
                raise InvalidArgument
        if stop_loss_price := kwargs.get("stop_loss_price"):
            if not (stop_loss_price < last_price and stop_loss_price < lowest_price):
                # 止损价要低于最低价格和最新价
                raise InvalidArgument

        kwargs["last_price"] = last_price
        basic_strategy, sty = new_spot_grid_strategy(user.id, kwargs)
        StrategySubBalanceCache(g.user.id).delete()
        detail = {
            "id": basic_strategy.id,
            "status": SpotGridDetailResource.to_front_status(sty).name,
            "created_at": int(basic_strategy.created_at.timestamp()),
            "market": sty.market,
            "mode": sty.mode.name,
            "grid_count": sty.grid_count,
            "lowest_price": sty.lowest_price,
            "highest_price": sty.highest_price,
            "base_amount": sty.base_amount,
            "quote_amount": sty.quote_amount,
            "trigger_price": sty.trigger_price,
            "entry_price": sty.entry_price,
            "take_profit_price": sty.take_profit_price,
            "stop_loss_price": sty.stop_loss_price,
            "min_pnl": min_pnl,
            "max_pnl": max_pnl,
        }
        return detail


@ns.route("/grid/spot/<int:strategy_id>")
@respond_with_code
class SpotGridDetailResource(Resource):

    @classmethod
    def get_basic_strategy(cls, user_id: int, strategy_id: int) -> UserStrategy:
        strategy: UserStrategy = UserStrategy.query.filter(
            UserStrategy.id == strategy_id,
            UserStrategy.user_id == user_id,
            UserStrategy.type == UserStrategy.Type.SPOT_GRID,
        ).first()
        if not strategy:
            raise InvalidArgument

        return strategy

    @classmethod
    def get_strategy(cls, user_id: int, strategy_id: int) -> SpotGridStrategy:
        strategy: SpotGridStrategy = SpotGridStrategy.query.filter(
            SpotGridStrategy.strategy_id == strategy_id,
            SpotGridStrategy.user_id == user_id,
        ).first()
        if not strategy:
            raise InvalidArgument

        return strategy

    @classmethod
    def to_front_status(cls, sty: SpotGridStrategy) -> UserStrategy.Status:
        status_map = {
            SpotGridStrategy.Status.CREATED: UserStrategy.Status.CREATED,
            SpotGridStrategy.Status.TRANSFERRED_IN: UserStrategy.Status.CREATED,
            SpotGridStrategy.Status.RUNNING: UserStrategy.Status.RUNNING,
            SpotGridStrategy.Status.PAUSED: UserStrategy.Status.RUNNING,
            SpotGridStrategy.Status.TERMINATED: UserStrategy.Status.TERMINATED,
            SpotGridStrategy.Status.TRANSFERRED_OUT: UserStrategy.Status.TERMINATED,
            SpotGridStrategy.Status.FINISHED: UserStrategy.Status.TERMINATED,
            SpotGridStrategy.Status.FAILED: UserStrategy.Status.TERMINATED,
        }
        return status_map.get(sty.status, UserStrategy.Status.CREATED)

    @classmethod
    def format(cls, basic_sty: UserStrategy, sty: SpotGridStrategy, sty_detail: SpotGridStrategyDetail) -> Dict:
        front_status = cls.to_front_status(sty)
        started_at = terminated_at = 0
        if basic_sty.terminated_at:
            terminated_at = int(basic_sty.terminated_at.timestamp())
        elif front_status == UserStrategy.Status.TERMINATED:
            # 终止操作需要时间，terminated_at可能还没设置
            terminated_at = int(sty.updated_at.timestamp())
        if basic_sty.started_at:
            started_at = int(basic_sty.started_at.timestamp())
        elif front_status == UserStrategy.Status.TERMINATED:
            # 没运行过: 待运行 -> 已终止
            started_at = terminated_at
        return {
            "id": basic_sty.id,
            "type": basic_sty.type.name,
            "status": front_status.name,
            "created_at": int(basic_sty.created_at.timestamp()),
            "started_at": started_at,
            "terminated_at": terminated_at,
            "market": sty.market,
            "lowest_price": sty.lowest_price,
            "highest_price": sty.highest_price,
            "base_amount": sty.base_amount,
            "quote_amount": sty.quote_amount,
            "trigger_price": sty.trigger_price,
            "begin_price": sty_detail.begin_price,
            "total_profit": sty_detail.total_profit,
            "grid_profit": sty_detail.grid_profit,
            "float_profit": sty_detail.float_profit,
            "grid_order_avg_price": sty_detail.grid_order_avg_price,
            "stop_price": sty_detail.stop_price,
            "stop_base_amount": sty_detail.stop_base_amount,
            "stop_quote_amount": sty_detail.stop_quote_amount,
        }

    @classmethod
    def try_update_match_order_and_profit(cls, strategy_id: int, cur_max_order_id: int):
        """ 配对订单更新可能不及时 """
        sty_trace: SpotGridStrategyTrace = SpotGridStrategyTrace.query.filter(
            SpotGridStrategyTrace.strategy_id == strategy_id,
        ).first()
        if cur_max_order_id > sty_trace.max_order_id:
            update_spot_grid_strategy_profit.delay(strategy_id)

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, strategy_id):
        """ 现货网格-策略详情 """
        user = g.user
        basic_sty = cls.get_basic_strategy(user.id, strategy_id)
        sty = cls.get_strategy(user.id, strategy_id)
        sty_detail: SpotGridStrategyDetail = SpotGridStrategyDetail.query.filter(
            SpotGridStrategyDetail.strategy_id == strategy_id,
        ).first()
        market_info = MarketCache(sty.market).dict
        market_max_fee = max(market_info["maker_fee_rate"], market_info["taker_fee_rate"])
        min_pnl, max_pnl = calc_grid_pnl_range(sty.lowest_price, sty.highest_price, sty.grid_count, market_max_fee)

        zero = Decimal()
        quote_asset = market_info["quote_asset"]
        base_asset = market_info["base_asset"]
        client = ServerClient()
        if sty.is_modifiable():
            # 未终止，返回持仓币种
            balance_dict = client.get_user_balances(user_id=basic_sty.run_user_id, account_id=SPOT_ACCOUNT_ID)
            quote_balance = balance_dict.get(quote_asset, {})
            quote_balance_amount = quote_balance["frozen"] + quote_balance["available"] if quote_balance else zero
            quote_balance_amount = max(quote_balance_amount - sty_detail.grid_profit, zero)  # 当前quote持仓币数需减去网格利润
            base_balance = balance_dict.get(base_asset, {})
            base_balance_amount = base_balance["frozen"] + base_balance["available"] if base_balance else zero
            balances = {quote_asset: quote_balance_amount, base_asset: base_balance_amount}
        else:
            balances = {quote_asset: zero, base_asset: zero}

        if sty.status == SpotGridStrategy.Status.RUNNING:
            orders = client.user_pending_orders(
                user_id=basic_sty.run_user_id,
                market=sty.market,
                account_id=SPOT_ACCOUNT_ID,
                page=1,
                limit=1,
            )
            cur_max_order_id = max([o["id"] for o in orders]) if orders else 0
            if cur_max_order_id:
                cls.try_update_match_order_and_profit(strategy_id, max([int(o["id"]) for o in orders]))

        detail = cls.format(basic_sty, sty, sty_detail)
        detail.update(
            {
                "mode": sty.mode.name,
                "grid_count": sty.grid_count,
                "entry_price": sty.entry_price,
                "take_profit_price": sty.take_profit_price,
                "stop_loss_price": sty.stop_loss_price,
                "grid_order_amount": sty_detail.grid_order_amount,
                "min_pnl": min_pnl,
                "max_pnl": max_pnl,
                "match_count": sty_detail.match_count,
                "latest_match_count": sty_detail.latest_match_count,
                "balances": balances,
            }
        )
        return detail

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            take_profit_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            stop_loss_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            trigger_price=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
        )
    )
    def put(cls, strategy_id, **kwargs):
        """ 现货网格-策略修改 """
        user = g.user
        grid_sty = cls.get_strategy(user.id, strategy_id)  # has check
        if not grid_sty.is_modifiable():
            raise InvalidArgument

        if grid_sty.is_already_triggered():
            trigger_price = grid_sty.trigger_price  # 运行中无法修改触发价
        else:
            trigger_price = kwargs.get("trigger_price", grid_sty.trigger_price)
        tg_changed = trigger_price != grid_sty.trigger_price

        take_profit_price = kwargs.get("take_profit_price", grid_sty.take_profit_price)
        stop_loss_price = kwargs.get("stop_loss_price", grid_sty.stop_loss_price)
        tp_changed = take_profit_price != grid_sty.take_profit_price
        sl_changed = stop_loss_price != grid_sty.stop_loss_price
        if not tp_changed and not sl_changed and not tg_changed:
            return

        last_price = ServerClient().market_last(grid_sty.market)
        if take_profit_price or stop_loss_price:
            if take_profit_price:
                if not (take_profit_price > last_price and take_profit_price > grid_sty.highest_price):
                    # 止盈价要高于最高价格和最新价
                    raise InvalidArgument
            if stop_loss_price:
                if not (stop_loss_price < last_price and stop_loss_price < grid_sty.lowest_price):
                    # 止损价要低于最低价格和最新价
                    raise InvalidArgument

        modify_spot_grid_strategy(grid_sty.strategy_id, take_profit_price, stop_loss_price, trigger_price, last_price, operator_id=user.id)

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            hold_asset=EnumField(SpotGridStrategy.TerminateHoldAsset, required=True),
        )
    )
    def delete(cls, strategy_id, **kwargs):
        """ 现货网格-策略终止 """
        user = g.user
        grid_sty = cls.get_strategy(user.id, strategy_id)
        if not grid_sty.is_terminable():
            raise InvalidArgument
        terminate_spot_grid_strategy(grid_sty.strategy_id, kwargs["hold_asset"], operator_id=user.id)


@ns.route("/grid/spot/<int:strategy_id>/pending-orders")
@respond_with_code
class SpotGridPendingOrderResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, strategy_id):
        """ 现货网格-策略当前委托 """
        user = g.user
        sty = SpotGridDetailResource.get_strategy(user.id, strategy_id)
        if sty.status != SpotGridStrategy.Status.RUNNING:
            return {
                "buys": [],
                "sells": [],
            }

        basic_strategy = SpotGridDetailResource.get_basic_strategy(user.id, strategy_id)
        run_user_id = basic_strategy.run_user_id
        client = ServerClient()
        orders = client.user_pending_orders(
            user_id=run_user_id,
            market=sty.market,
            account_id=SPOT_ACCOUNT_ID,
            page=1,
            limit=MAX_GRID_COUNT + 1,  # 不会超过最大网格数
        )
        if orders:
            SpotGridDetailResource.try_update_match_order_and_profit(strategy_id, max([int(o["id"]) for o in orders]))

        buys, sells = [], []
        for o in orders:
            item = {
                "order_id": o["id"],
                "price": o["price"],
                "amount": o["amount"],
            }
            if o["side"] == Order.OrderSideType.BUY:
                buys.append(item)
            else:
                sells.append(item)
        buys.sort(key=lambda x: Decimal(x["price"]), reverse=True)
        sells.sort(key=lambda x: Decimal(x["price"]), reverse=True)
        return {
            "buys": buys,
            "sells": sells,
        }


@ns.route("/grid/spot/<int:strategy_id>/finished-orders")
@respond_with_code
class SpotGridFinishedOrderResource(Resource):
    @classmethod
    def format_order_info(cls, dump_data: str, market_info: dict) -> dict:
        order = json.loads(dump_data)
        order["avg_price"] = Order.get_avg_price((Decimal(order['deal_stock']), Decimal(order['deal_money']), order['market']))
        order["create_time"] = int(order["create_time"])
        order["finish_time"] = int(order["finish_time"])
        order["side"] = Order.OrderSideType(int(order['side'])).name
        order["deal_money"] = quantize_amount(order["deal_money"], 8)
        order["deal_amount"] = quantize_amount(order.pop("deal_stock", '0'), 8)
        money_fee = quantize_amount(Decimal(order.pop("money_fee", 0)), 8)
        stock_fee = quantize_amount(Decimal(order.pop("stock_fee", 0)), 8)
        if money_fee:
            order["fee_asset"] = market_info["quote_asset"]
            order["fee_amount"] = money_fee
        else:
            order["fee_asset"] = market_info["base_asset"]
            order["fee_amount"] = stock_fee
        return order

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, strategy_id, **kwargs):
        """ 现货网格-策略历史委托 """
        user = g.user
        sty = SpotGridDetailResource.get_strategy(user.id, strategy_id)
        market_info = MarketCache(sty.market).dict

        q = SpotGridStrategyMatchOrder.query.filter(
            SpotGridStrategyMatchOrder.strategy_id == strategy_id,
        ).order_by(
            SpotGridStrategyMatchOrder.order_at.desc()
        )
        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        items = []
        for r in rows:
            item_orders = []
            if r.sell_order_id and r.sell_order_info:
                item_orders.append(cls.format_order_info(r.sell_order_info, market_info))
            if r.buy_order_id and r.buy_order_info:
                item_orders.append(cls.format_order_info(r.buy_order_info, market_info))
            items.append(
                {
                    "seq": r.seq,
                    "match_time": r.match_at,
                    "profit_amount": r.profit_amount,
                    "orders": item_orders,
                }
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/grid/spot/profit/history")
@respond_with_code
class SpotGridStrategyProfitResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            strategy_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
        )
    )
    def get(cls, **kwargs):
        """ 现货网格-策略收益曲线 """
        user = g.user
        strategy_id = kwargs["strategy_id"]
        _ = SpotGridDetailResource.get_strategy(user.id, strategy_id)

        rows = StrategyProfitHistory.query.filter(
            StrategyProfitHistory.strategy_id == strategy_id,
            StrategyProfitHistory.user_id == user.id,
        ).with_entities(
            StrategyProfitHistory.date,
            StrategyProfitHistory.profit_amount,
            StrategyProfitHistory.float_profit_amount,
        ).order_by(
            StrategyProfitHistory.date.desc(),
        ).limit(365).all()
        rows = rows[::-1]

        return [[i.date, i.profit_amount + i.float_profit_amount] for i in rows]
