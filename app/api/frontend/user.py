# -*- coding: utf-8 -*-
import json
import re
from datetime import date, datetime, timedelta
from decimal import Decimal
from enum import Enum
from math import ceil
from traceback import format_exc
from typing import Dict, Optional

from flask import request, g, current_app
from flask_api.exceptions import AuthenticationFailed
from flask_babel import gettext, force_locale
from flask_restx import fields as rx_fields
from sqlalchemy import func, or_
from sqlalchemy.orm import aliased
from user_agents.parsers import parse as parse_user_agent
from webargs import fields
from werkzeug.security import generate_password_hash

from app.api.common.decorators import lock_request
from app.business.clients import monitor_client
from app.business.oauth.base import get_oauth_client

from app.caches.auth import EmailCodeCache, ThirdPartyAccountAuthTokenCache, ThirdPartyAccountOperationTokenCache
from app.caches.flow_control import ResetTradePasswordFailureCache, ValidateTradePasswordFrequencyCache
from app.common.constants import GuideType, CAN_BIND_REFERRER_DAYS
from app.exceptions.basic import InvalidMobileVerificationCode, InvalidTotpVerificationCode, WebauthnVerificationFailed, \
    NotSupportedRegionInOnlyWithdrawal
from app.exceptions.user import AccountHasBeenBinded, InvalidTradePassword, \
    TradePasswordAlreadyExists, UserSignOffFailed, WithdrawPasswordAlreadyExists, \
    InvalidWithdrawPassword, AccountDoesNotExist, \
    ThirdPartyAccountExists, UnbindThirdPartyAccountNotAllowed, CountryNotSupportKycPro, UserEmailRepeat
from app.models.user import (
    KycVerificationPro, ThirdPartyAccount, UserExtra, UserPreference, UserSetting, MaskUser,
    ActivityPartyUser, KycCountryConfig, LivenessCheckHistory, LivenessCheckProfile,
    TwoFactorHistory, UserEmergencyContact, ImUser, UserBusinessCustomLayout, UserBusinessActiveLayout
)
from app.business.user import (
    check_user_sign_off, do_user_sign_off,
    UnfreezeAccountHelper, update_register_user_location,
    UserRepository,
)

from app.utils.date_ import current_timestamp, datetime_to_time
from ..common import (Resource, Namespace, limit_ip_frequency,
                      respond_with_code, require_login, require_2fa,
                      require_geetest, require_email_code,
                      get_request_ip, get_request_user_agent,
                      require_security_reset_token,
                      get_request_platform, get_request_host_url,
                      EmailCodeValidator, MobileCodeValidator,
                      limit_user_frequency, get_request_language, require_email_exists,
                      require_email_not_exists, TotpCodeValidator, require_user_permission, get_request_user,
                      )
from ..common.decorators import verify_user_permission, copy_trading_sub_user_setter
from ..common.fields import PageField, LimitField, EnumField, DeviceIdField, EmailField
from ..common.request import RequestPlatform, get_request_info, \
    is_old_app_request, get_request_version, get_region_name, require_user_request_permission, RequestPermissionCheck
from ..common.auth import sign_qrcode_url
from ..common.validators import WebauthnCredentialValidator
from ...business import (
    UserSettings, UserPreferences,
    send_registration_notice_email,
    send_login_notice_email,
    send_login_failure_notice_task,
    update_user_location,
    CacheLock, LockKeys, ServerClient,
    PerpetualServerClient, ReferralBusiness,
    SubAccountManager, ViaBTCPoolClient, LivenessCheckBusiness,
)
from ...business import (
    cached, CountrySettings, ALL_RECORD_ACCOUNT_ID, BusinessSettings,
)
from ...business import cancel_withdrawal
from ...business.clients.biz_monitor import biz_monitor
from ...business.credit import is_credit_user
from ...business.email import send_reset_security_notice_email, send_edit_security_notice_email, \
    send_sign_off_notice_email, send_set_login_password_success_notice_email, send_trade_password_update_email
from ...business.file import handle_request_file_upload
from ...business.ip import load_ip_reputation_info
from ...business.kyc import KycBusiness, KYCInstitutionBusiness, KycProBusiness, SumsubClient
from ...business.mission_center.plan import MissionPlanBiz
from ...business.red_packet.task import signup_user_send_red_packet_task
from ...business.risk_control.withdrawal import has_p2p_buy
from ...business.security import SecurityQuestionBusiness, QuestionType, \
    SecurityResetMethod, SecuritySettingType, check_trade_password, check_withdraw_password_by_api, \
        delete_trade_password_sub_account_failure_cache, reset_security_info, SecurityFileBusiness, \
        check_withdraw_password_limit, update_security_statistics
from ...business.sub_account import delete_sub_account_manager_relation, has_sub_account
from ...business.user import update_login_relation_history, new_login_relation_history, update_user_two_fa_type
from ...business.wallet import get_every_status_withdrawal_limit, \
    get_user_daily_withdrawal_limit
from ...business.copy_trading.trader import get_copy_trader_sub_user, add_trader_sub_api_frequency_limit
from ...caches import (
    UserLoginTokenCache, UserOperationTokenCache, UserLoginTokenAccessCache,
    LoginFailureCache, UserLoginOperationTokenCache,
    UserLoginVerifyCache, UserLoginQRCodeOperateTokenCache,
    GeetestCache, TotpKeyCache, ApiAuthCache,
    EmailCodeTokenCache, UserLoginQRCodeCache,
    TelegramBindingTokenCache,
    SubAccountSwitchCache, UserPreferenceFrontendCache,
)
from ...caches.security import SecurityOperationCache, SecurityResetFrequencyCache, UnfreezeAccountFrequencyCache
from ...caches.user import UserVisitPermissionCache, HadPoppedEditNameUserCache, UserConfigKeyCache
from ...common import (
    LOGIN_TOKEN_SIZE, OPERATION_TOKEN_SIZE, LOGIN_IP_LIMIT,
    LOGIN_FAILURE_LIMIT, LOGIN_STATE_DEFAULT_TTL,
    Language, Currency, TwoFAType,
    EmailCodeType, MessageTitle, MobileCodeType, SubAccountPermission,
    MessageContent, MessageWebLink, AccountEvent, ProductEvent
)
from ...config import config
from ...exceptions import (
    DuplicateSecurityResetApplication, LoginQRCodeExpired,
    InvalidSecurityResetToken, NotSupportedByCountryInOnlyWithdrawal, AuthenticationTimeout,
    InvalidArgument, InvalidUsernameOrPassword, EmailAlreadyExists,
    LoginForbidden, FrequencyExceeded,
    TwoFactorAuthenticationRequired,
    TwoFactorAuthenticationFailed,
    EmailDoesNotExist, OperationNotAllowed, InvalidLink,
    TotpAuthKeyAlreadyExists, TotpAuthKeyExpired,
    ApiAuthCountExceeded,
    RecordNotFound, MobileAlreadyBound,
    MobileAlreadyExists, ReferralCodeDoesNotExist,
    InvalidPassword,
    EmailNotAllowed, EmailCodeVerificationFailed, OfflineFeature, ApiExtendInvalid, OperationDenied,
    FrozenLoginForbidden, NoKycQualifications
)
from ...models import (
    User, SubAccount, LoginHistory, UserLoginState,
    EmailToken, ApiAuth, OperationLog, Referral,
    ReferralHistory, Withdrawal, db, Deposit, UserBindingAccount, Message, UserGuideHistory,
    CountrySetting, MarginLoanOrder, SubAccountManagerRelation, KYCFlow
)
from ...models.exchange import AssetExchangeOrder
from ...models.security import SecurityResetAnswerHistory, \
    SecurityResetApplication, SecurityResetFile, SecurityToolHistory
from ...models.wallet import WithdrawalCancel
from ...utils import (
    amount_to_str, ConfigMode, AWSBucketPublic, ConfigField, BaseHTTPClient,
)
from ...utils import (
    new_hex_token, Geetest3, GeoIP, now,
    new_totp_auth_key, validate_ip_address,
    list_mobile_country_codes, url_join,
    mobile_country_code_to_countries, normalise_mobile,
    hide_email, hide_mobile, hide_text_default, validate_password,
    validate_anti_phishing_code, is_disposable_email,
    batch_iter,
)
from ...utils.file import DEFAULT_THUMBNAIL_SCALE
from ...utils.text import validate_trade_password, validate_withdraw_password, max_length_validator

ns = Namespace('User')


ACCOUNT_NAME_ONLINE_DATE = date(2024, 12, 11)


def _get_user_has_popup_edit_name(user: User):
    if user.created_at.date() < ACCOUNT_NAME_ONLINE_DATE and not HadPoppedEditNameUserCache().has_user(user.id):
        return True
    return False


def _user_to_dict(user: User, auth_user: User, **kwargs) -> dict:
    user_id = user.id
    pref = UserPreferences(user_id)
    main_pref = UserPreferences(user.main_user_id) if user.is_sub_account else pref
    auth_pref = UserPreferences(auth_user.id) if user_id != auth_user.id else pref
    r = UserPreference.query.filter(
        UserPreference.user_id == user_id,
        UserPreference.key == UserPreferences.anti_phishing_code.name
    ).with_entities(UserPreference.updated_at).first()
    user_extra = user.extra
    return dict(
        user_id=user_id,
        name=user.nickname,
        account_name=user_extra.display_account_name,
        avatar=user_extra.avatar_url,
        is_popup_edit_name=_get_user_has_popup_edit_name(user),  # 账户上线一段时间之后修改为False,
        has_set_account_name=user_extra.check_has_set_account_name(),
        lang=main_pref.language.value,
        anti_phishing_code=auth_user.main_anti_phishing_code,
        anti_phishing_code_update_time=r.updated_at if r and auth_user.main_anti_phishing_code else None,
        email=hide_email(auth_user.email),
        origin_email=auth_user.email,
        has_email_verified=True,
        country_code=(str(auth_user.mobile_country_code)
                      if auth_user.mobile_country_code else None),
        location_code=auth_user.location_code,
        mobile=(hide_text_default(mobile_num)
                if (mobile_num := auth_user.mobile_num)
                else None),
        mobile_updated_at=auth_user.mobile_updated_at,
        origin_mobile=auth_user.mobile_num,
        has_set_login_password=bool(user.login_password_hash),
        login_password_level=user.login_password_level,
        login_password_update_time=user.login_password_updated_at,
        guide_type=pref.guide_type,
        ###
        # APP兼容, 交易验证相关字段，待移除
        trade_password_frequency="never",
        trade_password_update_time=0,
        ###
        has_totp_auth=bool(auth_user.totp_auth_key),
        has_webauthn=bool(auth_user.web_authn_list),
        totp_auth_update_time=auth_user.totp_auth_updated_at,
        has_announcement_notice=pref.allows_announcement_emails,
        has_activity_notice=pref.allows_activity_emails,
        has_blog_notice=pref.allows_blog_emails,
        has_deposit_withdrawal_notice=pref.allows_deposit_withdrawal_emails,
        opening_account_profit_loss=pref.opening_account_profit_loss,
        order_confirm=pref.order_confirmation,
        display_asset=main_pref.currency.name,
        protect_type=auth_pref.two_fa_type.value,
        auto_put_margin_loan_order=pref.auto_put_margin_loan_order,
        auto_flat_margin_loan_order=pref.auto_flat_margin_loan_order,
        auto_flat_margin_loan_order_time=30,  # 30 minutes
        opening_p2p_function=pref.opening_p2p_function,
        opening_p2p_merchant_function=pref.opening_p2p_merchant_function,
        mask_id=MaskUser.get_or_create(user.id).mask_id,
        **kwargs
    )


def _new_login_state(user_id: int, ttl: int = LOGIN_STATE_DEFAULT_TTL, need_access_token: bool = False) -> str:
    token = new_hex_token(LOGIN_TOKEN_SIZE)
    device = UserLoginState.get_device(
        get_request_user_agent(),
        get_request_version()
    )
    db.session_add_and_commit(UserLoginState(
        user_id=user_id,
        ip=(ip := get_request_ip()),
        location=GeoIP(ip).location,
        device=device,
        token=token,
        expires_at=datetime.utcnow() + timedelta(seconds=ttl)
    ))
    UserLoginTokenCache(user_id).add_token(token, ttl)
    if need_access_token:
        UserLoginTokenAccessCache(token).access()
    return token


def _new_login_history(user_id: int, account: str, device_id: str,
                       *, successful: bool = False, fail_reason: str = '') -> LoginHistory:
    user_agent = LoginHistory.get_login_user_agent(
        get_request_user_agent(),
        get_request_version(),
    )
    ip = get_request_ip()
    load_ip_reputation_info.delay(ip)
    return db.session_add_and_commit(LoginHistory(
        user_id=user_id,
        account=account,
        ip=ip,
        location=GeoIP(ip).location,
        user_agent=user_agent,
        platform=get_request_platform().value,
        device_id=device_id,
        successful=successful,
        fail_reason=fail_reason,
    ))


def _validate_hex_token(token: str, length: int = None):
    if not token:
        return False
    if length and len(token) != length:
        return False
    # noinspection PyBroadException
    try:
        int(token, 16)
    except Exception:
        return False
    return True


@ns.route('/')
@respond_with_code
class UserResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        auth_user: User = g.auth_user
        user: User = g.user
        pref = UserPreferences(user.id)
        is_open_contract = 'open' if pref.opening_perpetual_trading else 'close'
        is_open_margin = 'open' if pref.opening_margin_function else 'close'
        is_open_sub_account = 'open' if pref.opening_sub_account_function else 'close'
        login_history = LoginHistory.query \
            .filter(LoginHistory.user_id == user.id,
                    LoginHistory.successful.is_(True)) \
            .order_by(LoginHistory.id.desc()) \
            .first()
        if user.user_type != User.UserType.SUB_ACCOUNT:
            is_deposit = Deposit.query.filter(
                Deposit.user_id == user.id,
                Deposit.status.in_(
                    [Deposit.Status.CONFIRMING, Deposit.Status.TO_HOT,
                     Deposit.Status.FINISHED]
                )
            ).first()
            sub_account_permissions = []
        else:
            is_deposit = False
            sub_acc: SubAccount = SubAccount.query.filter(
                SubAccount.user_id == user.id,
                SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
                SubAccount.is_visible.is_(True),
            ).first()
            sub_account_permissions = [i.name for i in sub_acc.enum_permissions]

        withdrawal_limit_dict = get_every_status_withdrawal_limit(user.id)
        lang = get_request_language()

        user_extra = auth_user.extra
        has_reset_withdraw_password, reset_error_reason = cls.get_withdraw_password_info(auth_user, user_extra)
        can_bind_referrer = user.referrer is None and user.created_at >= now() - timedelta(days=CAN_BIND_REFERRER_DAYS)
        has_emergency_contact = cls._is_setup_emergency_contact(user.id)
        main_user_pref = UserPreferences(user.main_user_id)
        cls.check_user_has_block_words(user, user_extra.account_name)
        return _user_to_dict(
            user,
            auth_user,
            withdrawal_limit_dict=withdrawal_limit_dict,
            withdrawal_limit=get_user_daily_withdrawal_limit(user.id)['limit_usd'],
            withdrawal_fee_asset=pref.withdrawal_fee_asset,
            sign_in_log=(LoginHistoryResource.row_to_dict(login_history)
                         if login_history is not None else {}),
            account_type=user.user_type.value,
            domain=user.main_user.sub_domain,
            margin=is_open_margin,
            contract=is_open_contract,
            is_open_sub_account=is_open_sub_account,
            is_credit_user=(not user.is_sub_account and is_credit_user(user)),
            has_2fa=auth_user.has_2fa,
            has_kyc=auth_user.kyc_status == User.KYCStatus.PASSED,
            is_deposit=bool(is_deposit),
            sub_account_permissions=sub_account_permissions,
            opening_withdraw_password=pref.opening_withdraw_password,
            opening_trade_password=main_user_pref.opening_trade_password,
            opening_web_login_ip_locking=pref.opening_web_login_ip_locking,
            opening_web_security_login_duration=pref.opening_web_security_login_duration,
            opening_payment=pref.opening_payment,
            has_withdraw_password=user_extra.has_withdraw_password,
            has_trade_password=user_extra.has_trade_password,
            has_sub_account=has_sub_account(user.id),
            has_emergency_contact=has_emergency_contact,
            # 是否正在重置密码
            has_reset_withdraw_password=has_reset_withdraw_password,
            reset_error_reason=reset_error_reason,   # 已废弃，等 app 强制升级后删除
            can_bind_referrer=can_bind_referrer,
            created_at=user.created_at,
            **cls._get_kyc_info(auth_user, lang),
        )

    @classmethod
    def check_user_has_block_words(cls, user: User, account_name: str):
        from app.schedules.users import reset_user_nickname_task, reset_user_account_name_task
        user_id = user.id
        whitelist = BusinessSettings.user_name_whitelist
        if user_id in whitelist or user.is_sub_account:
            return
        nickname = user.name
        if nickname and UserRepository.check_user_name_has_block_word(nickname):
            reset_user_nickname_task.delay(user_id)
        if account_name and UserRepository.check_account_name_has_block_word(account_name):
            reset_user_account_name_task.delay(user_id)

    @classmethod
    def get_withdraw_password_info(cls, auth_user: User, user_extra):
        has_reset_withdraw_password = False
        reset_error_reason = ''
        if user_extra.has_withdraw_password:
            model = SecurityResetApplication
            rows = model.query.filter(
                model.user_id == auth_user.id,
            ).order_by(model.id.desc()).with_entities(
                model.status, model.reason, model.reset_type
            ).all()
            for row in rows:
                reason = model.Reason.INVALID_KYC_INFORMATION.name
                if row.status == model.StatusType.REJECTED and row.reason and reason in row.reason:
                    reset_error_reason = reason

            withdraw_password_rows = [row for row in rows if row.reset_type == model.ResetType.WITHDRAW_PASSWORD]
            if withdraw_password_rows and withdraw_password_rows[0].status == model.StatusType.CREATED:
                has_reset_withdraw_password = True
        return has_reset_withdraw_password, reset_error_reason

    @classmethod
    def _is_setup_emergency_contact(cls, user_id: int) -> bool:
        model = UserEmergencyContact
        row = model.query.with_entities(
            model.id
        ).filter(
            model.user_id == user_id,
            model.status == model.Status.VALID
        ).first()
        return row is not None

    @classmethod
    def _get_kyc_info(cls, user, lang):
        kyc_config = KycCountryConfig.query.filter(
                    KycCountryConfig.country == user.kyc_country,
                ).first()
        kyc_pro_status = User.KycProStatus.NONE.name
        support_kyc_pro = False
        if user.kyc_status is User.KYCStatus.NONE:
            return dict(
                kyc_name=None,
                kyc_status=user.kyc_status,
                kyc_pro_status=kyc_pro_status,
                kyc_type=None,
                support_kyc_pro=support_kyc_pro,
                country_support_kyc_pro=kyc_config.pro_supported if kyc_config else True,
            )

        if user.kyc_pro_status:
            kyc_pro_status = user.kyc_pro_status.name

        kyc_type = user.kyc_type
        if kyc_type == User.KYCType.INSTITUTION:
            kyc_name = KYCInstitutionBusiness(user).get_kyc_status().get('beneficiary_name')
        else:
            kyc_info = KycBusiness.get_user_kyc_info(user, lang)
            kyc_name = kyc_info.get('kyc_name')
            if user.kyc_status == User.KYCStatus.PASSED:
                kyc_config = KycCountryConfig.query.filter(
                    KycCountryConfig.country == user.kyc_country,
                ).first()
                support_kyc_pro = kyc_config.pro_supported if kyc_config else True
        kyc = dict(
            kyc_name=kyc_name,
            kyc_status=user.kyc_status.value,
            kyc_pro_status=kyc_pro_status,
            kyc_type=kyc_type,
            support_kyc_pro=support_kyc_pro,
            country_support_kyc_pro=kyc_config.pro_supported if kyc_config else True,
        )
        return kyc

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        name=fields.String,
        account_name=fields.String,
        avatar=fields.String,
        lang=EnumField(Language, enum_by_value=True),
        display_asset=fields.String,
        order_confirm=fields.Boolean
    ))
    def put(cls, **kwargs):
        user: User = g.user
        pref = UserPreferences(user.id)

        if name := kwargs.get('name'):
            UserRepository.update_user_name(user, name)

        if account_name := kwargs.get("account_name"):
            UserRepository.update_user_account_name(user, account_name)

        if avatar := kwargs.get("avatar"):
            UserRepository.update_user_avatar(user, avatar)

        if lang := kwargs.get('lang'):
            pref.language = lang
            current_app.logger.error("user: %d set lang: %s in modify user resource", user.id, lang)
            platform = get_request_platform()
            if platform.is_web():
                pref.web_language = lang
            else:
                pref.app_language = lang
        if currency := kwargs.get('display_asset'):
            pref.currency = getattr(Currency, currency)
        if (order_confirmation := kwargs.get('order_confirm')) is not None:
            pref.order_confirmation = order_confirmation
        return {}


@ns.route("/basic")
@respond_with_code
class UserBasicResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        """"同UserResource，但只返回尽量少的字段，提高响应性能，加速首屏渲染"""
        auth_user: User = g.auth_user
        user: User = g.user
        pref = UserPreferences(user.id)
        is_open_contract = 'open' if pref.opening_perpetual_trading else 'close'
        is_open_margin = 'open' if pref.opening_margin_function else 'close'
        kyc_info = UserResource._get_kyc_info(auth_user, get_request_language())
        user_extra = user.extra

        if user.user_type != User.UserType.SUB_ACCOUNT:
            sub_account_permissions = []
        else:
            sub_acc: SubAccount = SubAccount.query.filter(
                SubAccount.user_id == user.id,
                SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
                SubAccount.is_visible.is_(True),
            ).first()
            sub_account_permissions = [i.name for i in sub_acc.enum_permissions]

        return dict(
            user_id=user.id,
            name=user.nickname,
            account_name=user_extra.display_account_name,
            avatar=user_extra.avatar_url,
            email=hide_email(auth_user.email),
            mobile=(hide_text_default(mobile_num)
                    if (mobile_num := auth_user.mobile_num)
                    else None),
            account_type=user.user_type.value,
            contract=is_open_contract,
            margin=is_open_margin,
            opening_account_profit_loss=pref.opening_account_profit_loss,
            opening_p2p_function=pref.opening_p2p_function,
            opening_p2p_merchant_function=pref.opening_p2p_merchant_function,
            has_2fa=auth_user.has_2fa,
            has_kyc=auth_user.kyc_status == User.KYCStatus.PASSED,
            is_credit_user=(not user.is_sub_account and is_credit_user(user)),
            sub_account_permissions=sub_account_permissions,
            **kyc_info
        )

@ns.route("/im-user-info")
@respond_with_code
class ImUserInfoResource(Resource):

    @classmethod
    def get(cls):
        """获取IM用户信息，支持匿名和实名调用"""
        try:
            app_type = ImUser.UserType(request.headers.get('x-app-type', ''))
        except ValueError:
            raise InvalidArgument
        token = request.headers.get('AUTHORIZATION', '')

        is_anonymous = app_type in ImUser.ANONYMOUS_TYPES
        if is_anonymous:
            # 匿名登录逻辑，不验证，采用 AUTHORIZATION 作为身份标识
            if not token or len(token) > LOGIN_TOKEN_SIZE:
                raise AuthenticationFailed
            user_id = token
            user_detail_info = {}
        else:
            # 实名登录逻辑，验证token
            if not token or len(token) != LOGIN_TOKEN_SIZE:
                raise AuthenticationFailed
            token_cache = UserLoginTokenCache.from_token(token)
            if token_cache is None:
                raise AuthenticationFailed
            user_id = token_cache.user_id

            user = User.query.get(user_id)
            if not user:
                raise InvalidArgument
            user_extra = UserExtra.query.filter_by(user_id=user_id).first()
            user_detail_info = {
                "user_id": user.id,
                "nickname": user.nickname,
                "account_name": user_extra.display_account_name if user_extra else None,
                "avatar": user_extra.avatar_url if user_extra else None,
                "email": hide_email(user.email) if user.email else None,
                "created_at": user.created_at
            }

        im_user = ImUser.get_or_create(user_id=user_id, user_type=app_type)
        return {
            "im_user_id": im_user.im_user_id,
            "is_anonymous": is_anonymous,
            "app_type": app_type.value,
            "user_info": user_detail_info
        }


@ns.route('/sign/up/email')
@respond_with_code
class SignUpResource(Resource):
    RE_CHANNEL = re.compile(r'^[\w\d-]+$')

    @staticmethod
    def check_ip_country_code(_country_code: str, _platform: RequestPlatform):
        _setting = CountrySettings(_country_code)
        region_name = get_region_name(_country_code)
        if not _setting.allow_visit:
            raise NotSupportedByCountryInOnlyWithdrawal
        if not _setting.allow_register:
            raise NotSupportedRegionInOnlyWithdrawal(region=region_name)
        if _platform.is_web() and not _setting.allow_register_from_web:
            raise NotSupportedRegionInOnlyWithdrawal(region=region_name)
        if (not _platform.is_web()) and not _setting.allow_register_from_app:
            raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

    @classmethod
    def validate_ip(cls):
        ip = get_request_ip()
        _ip_info = GeoIP(ip)
        ip_country_code = _ip_info.country_code

        request_platform = get_request_platform()

        if ip_country_code:
            cls.check_ip_country_code(ip_country_code, request_platform)
        return _ip_info
    
    @classmethod
    def validate_device(cls, device_id):
        # 临时处理
        if device_id == "f24a4cb9312d135d85a946b83371866d0000b8b809bf33b9":
            raise EmailNotAllowed

    @classmethod
    def before_sign_up(cls, data, *, validate_email_code=True) -> Dict:
        cls.validate_device(data['device_id'])
        _ip_info = cls.validate_ip()
        email = data['email'].strip()
        if is_disposable_email(email):
            raise EmailNotAllowed
        password = data.get('login_password')
        if password is not None and not validate_password(password):
            raise InvalidPassword
        if referral_code := data.get('refer_code'):
            referral: Optional[Referral] = Referral.query \
                .filter(Referral.code == referral_code) \
                .first()
            if referral is None:
                raise ReferralCodeDoesNotExist
        else:
            referral = None
        if validate_email_code:
            email_code = data.get('validate_code')
            email_code_token = data.get('email_code_token')
            if email_code:
                EmailCodeValidator(email, EmailCodeType.REGISTRATION).validate(email_code)
            elif email_code_token:  # todo: validate code_type
                _, _email = EmailCodeTokenCache(data['email_code_token']) \
                    .get_user_and_email()
                if _email != email:
                    raise EmailCodeVerificationFailed
            else:
                raise EmailCodeVerificationFailed

        require_email_not_exists(email)
        location = _ip_info.location

        channel = data.get('channel')
        if channel:
            if len(channel) > 32 or not cls.RE_CHANNEL.fullmatch(channel):
                raise InvalidArgument
        return dict(
            email=email,
            referral=referral,
            ip=_ip_info.ip,
            location=location,
            channel=channel,
            device_id=data['device_id'],
        )

    @classmethod
    def after_sign_up(cls, user: User, data: Dict, is_third_party: bool = False) -> Dict:
        monitor_client.increase('web', 'sign_up')
        pref = UserPreferences(user.id)
        pref.language = Language(g.user_lang)
        current_app.logger.error("user: %d set lang: %s in after_sign_up", user.id, g.user_lang)
        if (web_lang := g.get('user_lang_web')) is not None:
            pref.web_language = Language(web_lang)
        if (app_lang := g.get('user_lang_app')) is not None:
            pref.app_language = Language(app_lang)
        if (tz_offset := g.get('user_tz_offset')) is not None:
            pref.timezone_offset = tz_offset
        pref.guide_type = GuideType.NO_TRADE_USER
        user.set_default_account_name()
        if is_third_party:
            op_log_type = OperationLog.Operation.THIRD_PARTY_SIGN_UP
        else:
            op_log_type = OperationLog.Operation.SIGN_UP
        OperationLog.add(user.id, op_log_type, user.email, get_request_platform())
        referral = data['referral']
        if referral is not None:
            is_ambassador = ReferralBusiness.is_valid_ambassador(referral.user_id)
            db.session_add_and_commit(ReferralHistory(
                referral_id=referral.id,
                referrer_id=referral.user_id,
                referree_id=user.id,
                referral_type=ReferralHistory.ReferralType.AMBASSADOR if is_ambassador else ReferralHistory.ReferralType.NORMAL
            ))

        token = _new_login_state(user.id)
        device_id = data['device_id']
        login_history = _new_login_history(user.id, user.email, device_id, successful=True)
        new_login_relation_history(login_history, True)
        update_register_user_location(user.id, g.get("user_tz_offset"))
        send_registration_notice_email.delay(user.id)
        signup_user_send_red_packet_task.delay(user.id)
        biz_monitor.increase_counter(AccountEvent.REGISTER_COUNT, with_source=True)
        return dict(token=token)

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        validate_code=fields.String,
        email_code_token=fields.String,
        login_password=fields.String(required=True),
        refer_code=fields.String(),
        channel=fields.String,
        device_id=DeviceIdField(required=True),
        activity_party=EnumField(ActivityPartyUser.ActivityParty, enum_by_value=True),
    ))
    def post(cls, **kwargs):
        info = cls.before_sign_up(kwargs, validate_email_code=True)

        password = kwargs['login_password']
        email = info['email']
        channel = info.get('channel')
        if channel == MissionPlanBiz.POOL_CHANNEL:
            # https://app.clickup.com/t/86eqpaup4
            try:
                channel = MissionPlanBiz.POOL_CHANNEL if ViaBTCPoolClient().is_pool_email(email) else None
            except Exception as e:
                current_app.logger.warning(f"Failed to check pool email: {e}")
                channel = None
        user = User(
            email=email,
            email_updated_at=now(),
            login_password_hash=generate_password_hash(password),
            login_password_level=User.cal_password_level(password),
            login_password_updated_at=now(),
            registration_ip=info['ip'],
            registration_location=info['location'],
            name='',
            channel=channel
        )
        user = db.session_add_and_flush(user)
        if activity_party := kwargs.get('activity_party'):
            cls.create_party_user(user.id, activity_party)
        db.session.commit()
        data = cls.after_sign_up(user, info)
        return _user_to_dict(user, user, token=data['token'])

    @classmethod
    def create_party_user(cls, user_id, activity_party):
        obj = ActivityPartyUser(
            user_id=user_id,
            activity_party=activity_party,
            mask_id=MaskUser.get_or_create(user_id).mask_id
        )
        db.session.add(obj)


@ns.route('/sign/in')
@respond_with_code
class SignInResource(Resource):
    MainUser = aliased(User)

    @classmethod
    def _email_validate_whitelist(cls):
        # AppStore 审核用账号，不验证邮箱
        return BusinessSettings.email_validate_whitelist

    @classmethod
    def validate_login_permission(cls, _user: User):
        _user_id = _user.id
        settings = UserSettings(_user_id)
        if not settings.login_enabled:
            if UnfreezeAccountHelper.has_user(_user_id):
                raise FrozenLoginForbidden
            raise LoginForbidden
        visit_permission = UserVisitPermissionCache().get_user_permission(_user_id)
        if visit_permission == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
            return
        if visit_permission == UserVisitPermissionCache.FORBIDDEN_VALUE:
            raise LoginForbidden

        cls.validate_ip()

        kyc_country = _user.kyc_country
        if kyc_country:
            _setting = CountrySettings(kyc_country)
            if not _setting.kyc_allow_login:
                raise NotSupportedByCountryInOnlyWithdrawal

    @classmethod
    def do_sign_in(cls, account, password) -> User:
        failure_limit, failure_ttl = LOGIN_FAILURE_LIMIT
        failure_cache = LoginFailureCache(account, ttl=failure_ttl)
        if failure_cache.count() >= failure_limit:
            monitor_client.increase('web', 'sign_in_fail')
            retry_in = int(ceil(failure_cache.ttl() / 60))
            raise FrequencyExceeded(
                message=gettext(
                    '登录已锁定，请%(minutes)s分钟后再尝试。', minutes=max(int(ceil(retry_in)), 1)))

        user = None
        def add_failure(*_args):
            monitor_client.increase('web', 'sign_in_fail')
            failure_cache.add_value()
            if failure_cache.count() >= failure_limit:
                if isinstance(user, User):
                    send_login_failure_notice_task.delay(user.id)
            raise InvalidUsernameOrPassword(*_args)

        user = User.from_account(account)
        if user is None:
            add_failure(failure_limit - failure_cache.count() - 1)
        if isinstance(user, User):
            if user.is_sub_account:
                raise InvalidArgument
            if not user.check_login_password(password):
                add_failure(failure_limit - failure_cache.count() - 1)
        else:
            for u in user:
                if u.check_login_password(password) and not u.is_sub_account:
                    user = u
                    break
            else:
                add_failure(failure_limit - failure_cache.count() - 1)
        failure_cache.delete()
        return user

    @classmethod
    def after_sign_in(cls, user: User, data: Dict) -> Dict:
        monitor_client.increase('web', 'sign_in')
        user_id = user.id
        if login_history_id := data.get('login_history_id'):
            LoginHistory.query.filter(LoginHistory.id == data['login_history_id']) \
                .update(dict(successful=True, fail_reason=''), synchronize_session=False)
            db.session.commit()
        else:
            login_history = _new_login_history(user_id, data['account'], data['device_id'], successful=True)
            login_history_id = login_history.id

        if user.has_2fa:
            db.session_add_and_commit(TwoFactorHistory(
                user_id=user.id,
                business_type=TwoFactorHistory.BusinessType.LOGIN_HISTORY,
                business_id=login_history_id,
                two_fa_type=g.get('success_2fa_type', TwoFAType.NONE),
                successful=True
            ))
        update_login_relation_history.delay(login_history_id, False)
        pref = UserPreferences(user_id, ConfigMode.REAL_TIME)
        token = _new_login_state(user_id, need_access_token=pref.opening_web_security_login_duration)
        req_lang = get_request_language(use_default_val=False)
        if not req_lang:
            req = get_request_info()
            msg = ' '.join([f'{k}: {v}' for k, v in req.items()])
            current_app.logger.warning(f"after_sign_in lack of language: {msg} {format_exc()}",
                                     extra={'method': req['method'], 'path': req['path']})
        else:
            if (lang := g.get('user_lang')) is not None:
                pref.language = Language(lang)
            if (web_lang := g.get('user_lang_web')) is not None:
                pref.web_language = Language(web_lang)
            if (app_lang := g.get('user_lang_app')) is not None:
                pref.app_language = Language(app_lang)

        if (tz_offset := g.get('user_tz_offset')) is not None:
            pref.timezone_offset = tz_offset
        request_host_url = get_request_host_url()
        if request_host_url.endswith('/'):
            request_host_url = request_host_url[:-1]
        if request_host_url != pref.host_url:
            pref.host_url = request_host_url
        send_login_notice_email.delay(login_history_id)
        update_user_location(user_id, g.get('user_tz_offset'))
        biz_monitor.increase_counter(AccountEvent.LOGIN_COUNT, with_source=True)
        return dict(token=token)

    @classmethod
    def validate_ip(cls):
        ip = get_request_ip()
        _ip_info = GeoIP(ip)
        ip_country_code = _ip_info.country_code
        if ip_country_code:
            _setting = CountrySettings(ip_country_code)
            if not _setting.allow_login:
                region_name = get_region_name(ip_country_code)
                raise NotSupportedRegionInOnlyWithdrawal(region=region_name)

    @classmethod
    @require_geetest
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        account=fields.String(required=True),
        login_password=fields.String(required=True),
        device_id=DeviceIdField(required=True)
    ))
    def post(cls, **kwargs):
        account: str = kwargs['account'].strip()
        password: str = kwargs['login_password']
        device_id: str = kwargs['device_id']

        user = cls.do_sign_in(account, password)

        user_id = user.id

        cls.validate_login_permission(user)

        if is_old_app_request(3340, 62):  # app兼容老设备仅绑定通讯密钥用户
            _require_2fa = True if (user.totp_auth_key or user.mobile) else False
        else:
            _require_2fa = user.has_2fa

        _require_email_code = False
        if user.email and user.email not in cls._email_validate_whitelist() \
                and LoginHistory.is_new_device(user.id, device_id)\
                and not _require_2fa:
            _require_email_code = True

        if _require_2fa or _require_email_code:
            operation_token = new_hex_token(OPERATION_TOKEN_SIZE)
            UserLoginOperationTokenCache(operation_token).set_user(user_id)
            login_history = _new_login_history(
                user_id, account, device_id,
                fail_reason=LoginHistory.FailReason.DOUBLE_CHECK.name)
            data = dict(
                require_email_code=_require_email_code,
                require_2fa=_require_2fa,
                login_history_id=login_history.id
            )
            UserLoginVerifyCache(operation_token).set_data(data)
            raise TwoFactorAuthenticationRequired(dict(
                email=(hide_email(email)
                       if (email := user.email)
                       else None),
                origin_email=user.email,
                country_code=(str(country_code)
                              if (country_code := user.mobile_country_code)
                              else None),
                mobile=(hide_text_default(mobile_num)
                        if (mobile_num := user.mobile_num)
                        else None),
                origin_mobile=user.mobile_num,
                has_totp_auth=bool(user.totp_auth_key),
                has_webauthn=bool(user.web_authn_list),
                protect_type=UserPreferences(user_id).two_fa_type.value,
                operate_token=operation_token,
                **data
            ))

        data = cls.after_sign_in(user, kwargs)
        return _user_to_dict(user, user, token=data['token'])


@ns.route('/sign/in/verify')
@respond_with_code
class SignInWith2FAResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            operate_token=fields.String(required=True),
            validate_code=fields.String
        )
    )
    def post(cls, **args):
        operation_token = args['operate_token']
        if len(operation_token) != OPERATION_TOKEN_SIZE:
            raise TwoFactorAuthenticationFailed

        login_op_token_cache = UserLoginOperationTokenCache(operation_token)
        user_id = login_op_token_cache.get_user()
        if user_id is None:
            raise AuthenticationTimeout

        user = User.query.filter_by(id=user_id).first()
        if user is None:
            raise TwoFactorAuthenticationFailed
        if user.is_sub_account:
            raise InvalidArgument

        failure_limit, failure_ttl = LOGIN_FAILURE_LIMIT
        failure_cache = LoginFailureCache(
            user.main_user_email, ttl=failure_ttl)
        if failure_cache.count() >= failure_limit:
            raise FrequencyExceeded

        data = UserLoginVerifyCache(operation_token).get_data()
        if not data:
            raise AuthenticationTimeout

        if data['require_2fa']:
            # hack: set g.user, use require_2fa without login.
            g.auth_user = g.user = user
            try:
                require_2fa(MobileCodeType.LOGIN_WITH_OPERATION_TOKEN)(lambda: None)()
            except Exception as e:
                failure_cache.add_value()
                if isinstance(e, InvalidMobileVerificationCode):
                    fail_reason = LoginHistory.FailReason.TWO_FA_MOBILE
                elif isinstance(e, InvalidTotpVerificationCode):
                    fail_reason = LoginHistory.FailReason.TWO_FA_TOTP
                elif isinstance(e, WebauthnVerificationFailed):
                    fail_reason = LoginHistory.FailReason.TWO_FA_WEBAUTHN
                else:
                    fail_reason = LoginHistory.FailReason.TWO_FA
                LoginHistory.query.filter(
                    LoginHistory.id == data['login_history_id'],
                    LoginHistory.successful.is_(True),
                ).update(dict(fail_reason=fail_reason.name),
                         synchronize_session=False)
                db.session.commit()
                raise

        if data['require_email_code']:
            code = args.get('validate_code')
            try:
                EmailCodeValidator(user.email, EmailCodeType.SIGN_IN).validate(code)
            except Exception:
                LoginHistory.query.filter(
                    LoginHistory.id == data['login_history_id'],
                    LoginHistory.successful.is_(True),
                ).update(dict(fail_reason=LoginHistory.FailReason.EMAIL_CODE.name), synchronize_session=False)
                db.session.commit()
                raise EmailCodeVerificationFailed

        login_op_token_cache.delete()
        data = SignInResource.after_sign_in(user, dict(login_history_id=data['login_history_id']))
        return _user_to_dict(user, user, token=data['token'])


@ns.route('/sign/qrcode')
@respond_with_code
class LoginQRCodeResource(Resource):
    class Status:
        CREATED = 'created'
        SCANNED = 'scanned'
        CONFIMED = 'confirmed'
        EXPIRED = 'expired'

    @classmethod
    @ns.use_kwargs(dict(
        qrcode_key=fields.String(required=True),
        device_id=DeviceIdField(required=True),
    ))
    def get(cls, **kwargs):
        """轮询二维码"""
        device_id = kwargs['device_id']
        qrcode_key = kwargs['qrcode_key']
        if not _validate_hex_token(qrcode_key, 24):
            raise InvalidArgument

        cache = UserLoginQRCodeCache(qrcode_key)
        data = cache.get_data()
        if not data:
            return {'status': cls.Status.EXPIRED}

        status = data['status']
        if status == cls.Status.CREATED or status == cls.Status.SCANNED:
            return {'status': status}
        if status == cls.Status.CONFIMED:
            with CacheLock(LockKeys.qrcode_login(qrcode_key)):
                if not cache.exists():  # check again
                    raise LoginQRCodeExpired
                # 确认设备和ip一致
                if device_id != data['device_id']:
                    raise InvalidArgument
                if get_request_ip() != data['ip']:
                    raise LoginQRCodeExpired
                user_id = data['user_id']
                user = User.query.get(user_id)
                if not user.has_2fa:
                    raise TwoFactorAuthenticationRequired

                cache.delete()
            data = SignInResource.after_sign_in(user, dict(
                account=user.email or user.mobile,
                device_id=device_id
            ))

            user_info = _user_to_dict(user, user, token=data['token'])
            return {
                'status': cls.Status.CONFIMED,
                'user': user_info
            }

    @classmethod
    @limit_ip_frequency(15, 300)
    @ns.use_kwargs(dict(
        device_id=DeviceIdField(required=True)
    ))
    def post(cls, **kwargs):
        """生成登陆二维码"""
        device_id = kwargs.get('device_id')
        ip = get_request_ip()
        ua = get_request_user_agent()
        token = new_hex_token(24)
        cache = UserLoginQRCodeCache(token)
        cache.set_data(dict(
            ip=ip,
            device_id=device_id,
            user_agent=ua,
            status=cls.Status.CREATED
        ))

        url = url_join(get_request_host_url(), f"/qr/{token}")
        sig = sign_qrcode_url(url)

        return dict(
            qrcode=f"{url}?s={sig}"
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        qrcode_key=fields.String(required=True)
    ))
    def patch(cls, **kwargs):
        """App扫码"""
        qrcode_key = kwargs['qrcode_key']
        if not _validate_hex_token(qrcode_key, 24):
            raise InvalidArgument

        with CacheLock(LockKeys.qrcode_login(qrcode_key)):
            cache = UserLoginQRCodeCache(qrcode_key)
            data = cache.get_data()
            if not data:
                raise LoginQRCodeExpired
            if data['status'] != cls.Status.CREATED:
                raise LoginQRCodeExpired
            if not g.user.has_2fa:
                raise TwoFactorAuthenticationRequired

            parsed = parse_user_agent(data['user_agent'])
            os = parsed.os.family.split()[0]
            browser = parsed.browser.family.split()[0]
            device = f'{os} {browser}'

            is_new_device = LoginHistory.is_new_device(g.user.id, data['device_id'])
            data.update(dict(
                user_id=g.user.id,
                status=cls.Status.SCANNED
            ))
            cache.set_data(data)
            # 下发临时token，用于后续流程，确保是该扫码设备发起
            op_token = new_hex_token(32)
            UserLoginQRCodeOperateTokenCache(op_token).set_data(qrcode_key)

        return dict(
            operation_token=op_token,
            ip=data['ip'],
            device=device,
            location=GeoIP(data['ip']).location,
            is_new_device=is_new_device
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        operation_token=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """确认登陆"""
        op_token = kwargs['operation_token']
        if not _validate_hex_token(op_token, 32):
            raise InvalidArgument

        op_cache = UserLoginQRCodeOperateTokenCache(op_token)
        qrcode_key = op_cache.read()
        if not qrcode_key:
            raise LoginQRCodeExpired
        with CacheLock(LockKeys.qrcode_login(qrcode_key)):
            if not op_cache.exists():  # check again
                raise LoginQRCodeExpired
            cache = UserLoginQRCodeCache(qrcode_key)
            data = cache.get_data()
            if not data:
                raise LoginQRCodeExpired
            if data['user_id'] != g.user.id:
                raise InvalidArgument
            if data['status'] != cls.Status.SCANNED:
                raise InvalidArgument

            op_cache.delete()
            data['status'] = cls.Status.CONFIMED
            cache.set_data(data)
        update_security_statistics([g.user.id], SecuritySettingType.QRCODE_SIGNIN)
        return {}


@ns.route('/sign/out')
@respond_with_code
class SignOutResource(Resource):

    @classmethod
    @require_login
    def post(cls):
        token = g.login_token
        login_state: UserLoginState = UserLoginState.query \
            .filter(UserLoginState.token == token,
                    UserLoginState.expires_at >= datetime.utcnow()) \
            .first()
        if login_state is None:
            return None

        login_state.status = UserLoginState.Status.OFFLINE
        login_state.updated_at = datetime.utcnow()
        db.session.commit()
        UserLoginTokenCache(login_state.user_id).del_token(token)
        return login_state.id


@ns.route('/sign/off')
@respond_with_code
class SignOffResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(10, 3600)
    def get(cls):
        res = check_user_sign_off(g.user)
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(10, 3600)
    def post(cls):
        """
        用户注销
        """
        user = g.user
        email = user.email
        if user.email:
            require_email_code()(lambda: None)()
        if is_old_app_request(3340, 62):  # app兼容老设备仅绑定通讯密钥用户
            _require_2fa = True if (user.totp_auth_key or user.mobile) else False
        else:
            _require_2fa = user.has_2fa
        if _require_2fa:
            require_2fa(MobileCodeType.SIGN_OFF)(lambda: None)()
        if not do_user_sign_off(user):
            raise UserSignOffFailed
        OperationLog.add(user.id, OperationLog.Operation.SIGN_OFF, '', get_request_platform())
        SecurityToolHistory.add(user.id, SecurityToolHistory.OpType.SIGN_OFF,
                                SecurityToolHistory.OpRole.USER, user.email, email)
        send_sign_off_notice_email.delay(user.main_user.id, current_timestamp(to_int=True))

        update_security_statistics([user.id], SecuritySettingType.SIGN_OFF)
        biz_monitor.increase_uniq_counter(
            ProductEvent.SIGN_OFF,
            value=[user.id],
            with_source=True
        )


@ns.route('/sign/country')
@respond_with_code
class CountriesResource(Resource):

    @classmethod
    def get(cls):
        lang = g.lang
        if lang not in [Language.ZH_HANS_CN.value, Language.ZH_HANT_HK.value]:
            lang = Language.EN_US.value
        return cls._get(lang)

    @classmethod
    @cached(300)
    def _get(cls, lang: str):
        result = []
        q = CountrySetting.query.filter(CountrySetting.key == "allow_binding_mobile",
                                        CountrySetting.value == "0").with_entities(
            CountrySetting.code
        )
        disable_codes = {v.code for v in q}
        for c_code in list_mobile_country_codes():
            for c_info in mobile_country_code_to_countries(c_code):
                if c_info.iso_3 in disable_codes:
                    continue
                with force_locale(lang):
                    result.append(dict(
                        can_send_sms=True,
                        first_letter=c_info.iso_2[0],
                        key=c_code,
                        value=gettext(c_info.cn_name)
                    ))
        return result


@ns.route('/sign/up/ip')
@respond_with_code
class SignUPIpResource(Resource):

    @classmethod
    def get(cls):
        is_forbidden = False
        country_code = GeoIP(get_request_ip()).country_code
        if country_code and CountrySettings(country_code).allow_register_from_app is False:
            is_forbidden = True
        return dict(is_forbidden=is_forbidden)


@ns.route('/sign/captcha')
@respond_with_code
class CaptchaResource(Resource):

    @classmethod
    def get(cls):
        """Deprecated"""


@ns.route('/sign/geetest')
@respond_with_code
class GeetestResource(Resource):

    @classmethod
    def get(cls):
        """旧版本App的极验验证，兼容保留"""
        user = 'unknown'
        status, response = Geetest3().generate(user)
        GeetestCache(response.challenge).set_data(user, status)
        return response.dict


@ns.route('/sign/log')
@respond_with_code
class LoginHistoryResource(Resource):
    DETAIL_MODEL = ns.model('LoginHistory', {
        'id': rx_fields.Integer(example=1),
        'create_time': rx_fields.Integer(example='时间, 1599788628'),
        'ip': rx_fields.String(example='***********, ip'),
        'location': rx_fields.String(example='新加坡 新加坡'),
        'user_agent_human': rx_fields.String(example='Mac Chrome'),
        'device': rx_fields.String(example='Mac Chrome'),
        'is_success': rx_fields.Boolean(example='是否成功， False'),
    })

    LIST_MODEL = ns.model('LoginHistories',
                          {
                              'data': rx_fields.Nested(DETAIL_MODEL, as_list=True)
                          })

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    @ns.response(200, 'Success', LIST_MODEL)
    def get(cls, **kwargs):
        records = LoginHistory.query \
            .filter(LoginHistory.user_id == g.user.id) \
            .order_by(LoginHistory.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(cls.row_to_dict, records.items)),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    def row_to_dict(cls, row: LoginHistory) -> dict:
        return dict(
            create_time=row.created_at,
            ip=row.ip,
            location=row.location,
            user_agent_human=row.user_agent_human,
            device=row.device,
            is_success=row.successful
        )


@ns.route('/login-states')
@respond_with_code
class LoginStatesResource(Resource):
    DETAIL_MODEL = ns.model('LoginState', {
        'id': rx_fields.Integer(example='id, 1'),
        'date': rx_fields.Integer(example='时间, 1599788628'),
        'ip': rx_fields.String(example='***********, ip'),
        'location': rx_fields.String(example='新加坡 新加坡'),
        'device': rx_fields.String(example='PC / Mac OS X 10.15.6 / Chrome 85.0.4183'),
        'is_current': rx_fields.Boolean(example='是否当前token, False'),
        "expires_at": rx_fields.Integer(example='过期时间 **********'),
    })

    LIST_MODEL = ns.model('LoginStates',
                          {
                              'data': rx_fields.Nested(DETAIL_MODEL, as_list=True)
                          })

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    @ns.response(200, 'Success', LIST_MODEL)
    def get(cls, **kwargs):
        cur_token = g.login_token
        records = UserLoginState.list_user_online_states_with_paginate(
            g.user.id, kwargs['page'], kwargs['limit'])
        items = [dict(
            id=row.id,
            date=row.created_at,
            ip=row.ip,
            location=row.location,
            device=row.device,
            device_type=row.get_device_type(),
            is_current=row.token == cur_token,
            expires_at=row.expires_at
        ) for row in records.items]

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=items,
            total=records.total,
            total_page=records.pages
        )


# noinspection PyUnresolvedReferences
@ns.route('/login-states/<int:id_>')
@respond_with_code
class LoginStateResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def delete(cls, id_):
        row: UserLoginState = UserLoginState.query \
            .filter(UserLoginState.id == id_,
                    UserLoginState.user_id == g.user.id) \
            .first()
        if row is None:
            raise InvalidArgument

        row.status = UserLoginState.Status.OFFLINE
        row.updated_at = datetime.utcnow()
        db.session.commit()
        UserLoginTokenCache(row.user_id).del_token(row.token)
        return {}


@ns.route('/email')
@respond_with_code
class EmailResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.EMAIL_BINDING, allow_sub_account=False)
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        validate_code=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        if user.email:
            raise EmailAlreadyExists

        email = kwargs['email']
        code = kwargs['validate_code']
        EmailCodeValidator(email, EmailCodeType.EMAIL_BINDING).validate(code)
        require_email_not_exists(email)

        user.set_email(email)
        OperationLog.add(user.id, OperationLog.Operation.ADD_EMAIL, email,
                         get_request_platform())
        return {}

    @classmethod
    @require_2fa(MobileCodeType.EMAIL_EDIT, allow_sub_account=False)
    @require_email_code
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        validate_code=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """ 账户设置-修改邮箱 """
        user: User = g.user
        if not user.email:
            raise EmailDoesNotExist

        email = kwargs['email']
        code = kwargs['validate_code']
        EmailCodeValidator(email, EmailCodeType.EMAIL_RESET).validate(code)
        require_email_not_exists(email)

        old_email = user.email
        user.set_email(email)
        for t in EmailCodeType:
            EmailCodeCache(old_email, t).delete()
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        OperationLog.add(user.id, OperationLog.Operation.EDIT_EMAIL,
                         f'old: {old_email}, new: {email}',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_EMAIL,
            SecurityToolHistory.OpRole.USER,
            user.email,
            account_before=old_email
        )
        signup_user_send_red_packet_task.delay(user.id)
        send_edit_security_notice_email.delay("email", user.id)
        return {}


@ns.route('/mobile')
@respond_with_code
class MobileResource(Resource):

    @classmethod
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        country_code=fields.Integer(required=True),
        mobile=fields.String(required=True),
        validate_code=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        country_code = kwargs['country_code']
        number = kwargs['mobile']
        code = kwargs['validate_code']

        MobileCodeValidator(country_code, number, MobileCodeType.MOBILE_BINDING).validate(code)

        user: User = g.user
        if user.mobile:
            raise MobileAlreadyBound
        if (is_old_app_request(3340, 62)  # app兼容老设备仅绑定通讯密钥用户
                and user.web_authn_list
                and not bool(user.mobile or user.totp_auth_key)):
            raise InvalidArgument(message=gettext('由于你绑定了通行密钥，APP端暂不支持验证，请到WEB端设置其他2FA'))

        if user.has_2fa:
            require_2fa(MobileCodeType.MOBILE_BINDING)(lambda: None)()

        country_code, number = normalise_mobile(country_code, number)
        if User.query \
                .filter(User.mobile_country_code == country_code,
                        User.mobile_num == number) \
                .first() is not None:
            raise MobileAlreadyExists

        user.set_mobile(country_code, number)
        UserPreferences(user.id).two_fa_type = TwoFAType.MOBILE
        update_user_location(user.id, g.get('user_tz_offset'))
        OperationLog.add(user.id, OperationLog.Operation.ADD_MOBILE,
                         f'{country_code}{number}',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.BIND_MOBILE,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )

        db.session_add_and_commit(
            Message(
                user_id=user.id,
                title=MessageTitle.ADD_MOBILE.name,
                content=MessageContent.ADD_MOBILE_SUCCESS.name,
                params=json.dumps(
                    dict(
                        mobile=hide_mobile(user.mobile_num),
                    )
                ),
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )

        return {}

    @classmethod
    @require_2fa(MobileCodeType.MOBILE_EDIT, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        country_code=fields.Integer(required=True),
        mobile=fields.String(required=True),
        validate_code=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """ 账户设置-修改手机 """
        country_code = kwargs['country_code']
        number = kwargs['mobile']
        code = kwargs['validate_code']

        MobileCodeValidator(country_code, number, MobileCodeType.MOBILE_RESET).validate(code)

        user: User = g.user

        country_code, number = normalise_mobile(country_code, number)
        if User.query \
                .filter(User.mobile_country_code == country_code,
                        User.mobile_num == number) \
                .first() is not None:
            raise MobileAlreadyExists
        mobile_before = user.mobile
        user.set_mobile(country_code, number)
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        update_user_location(user.id, g.get('user_tz_offset'))
        OperationLog.add(user.id, OperationLog.Operation.EDIT_MOBILE, f'{country_code}{number}',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_MOBILE,
            SecurityToolHistory.OpRole.USER,
            user.email,
            account_before=mobile_before
        )
        send_edit_security_notice_email.delay("mobile", user.id)
        return {}
    
    @classmethod
    @require_2fa(MobileCodeType.MOBILE_UNBIND, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    def patch(cls):
        """ 账户设置-解绑手机 """
        user: User = g.user
        mobile_before = user.mobile
        if not mobile_before:
            raise InvalidArgument
        user.set_mobile(None, None)
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        update_user_two_fa_type(user)
        OperationLog.add(user.id, OperationLog.Operation.RESET_MOBILE, mobile_before,
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.UNBIND_MOBILE,
            SecurityToolHistory.OpRole.USER,
            user.email,
            account_before=mobile_before
        )


@ns.route('/withdraw-password')
@respond_with_code
class WithdrawPasswordResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.ADD_WITHDRAW_PASSWORD, allow_sub_account=False)  # 设置提现密码不需要邮件验证码
    @ns.use_kwargs(dict(
        withdraw_password=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 账户设置-设置提现密码 """

        user: User = g.user
        extra = user.extra
        if extra.has_withdraw_password:
            raise WithdrawPasswordAlreadyExists

        withdraw_password = kwargs['withdraw_password']
        if not validate_withdraw_password(withdraw_password):
            raise InvalidWithdrawPassword

        cls.set_withdraw_password(user, withdraw_password)
        db.session_add_and_commit(
            Message(
                user_id=user.id,
                title=MessageTitle.ADD_WITHDRAW_PASSWORD.name,
                content=MessageContent.ADD_WITHDRAW_PASSWORD_SUCCESS.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )

    @classmethod
    @require_2fa(MobileCodeType.EDIT_WITHDRAW_PASSWORD, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        old_withdraw_password=fields.String(required=True),
        new_withdraw_password=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """ 账户设置-修改提现密码 """
        user: User = g.user
        old_withdraw_password = kwargs['old_withdraw_password']
        new_withdraw_password = kwargs['new_withdraw_password']
        check_withdraw_password_limit(user, old_withdraw_password)

        if not validate_withdraw_password(new_withdraw_password):
            raise InvalidWithdrawPassword
        cls.set_withdraw_password(user, new_withdraw_password, is_update=True)
        send_edit_security_notice_email.delay("withdraw_password", user.id)

    @classmethod
    def set_withdraw_password(cls, user, withdraw_password, is_update=False):
        if is_update:
            operation = OperationLog.Operation.EDIT_WITHDRAW_PASSWORD
            op_type = SecurityToolHistory.OpType.EDIT_WITHDRAW_PASSWORD
            suspend = True
        else:
            operation = OperationLog.Operation.ADD_WITHDRAW_PASSWORD
            op_type = SecurityToolHistory.OpType.ADD_WITHDRAW_PASSWORD
            suspend = False
        user.extra.set_withdraw_password(withdraw_password, suspend=suspend)
        OperationLog.add(user.id, operation, user.email,
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            op_type,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )


@ns.route('/withdraw-password/validate')
@respond_with_code
class WithdrawPasswordValidateResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        withdraw_password=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 验证提现密码 """
        withdraw_password = kwargs['withdraw_password']
        if not validate_withdraw_password(withdraw_password):
            raise InvalidWithdrawPassword
        user = g.user
        pref = UserPreferences(user.id)
        if not pref.opening_withdraw_password or not user.extra.has_withdraw_password:
            raise InvalidArgument
        check_withdraw_password_by_api(user, withdraw_password)


@ns.route('/trade-password')
@respond_with_code
class TradePasswordResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        """ 账户设置-交易密码详情 """

        user: User = g.user
        main_user = user.main_user
        extra = main_user.extra
        freq_cache = ValidateTradePasswordFrequencyCache(user.id)
        pref = UserPreferences(main_user.id)
        return dict(
            require_trade_password=pref.opening_trade_password \
                and extra.has_trade_password and not freq_cache.exists(),
        )

    @classmethod
    @require_2fa(MobileCodeType.TRADING_PASSWORD_ADDITION, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        trade_password=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 账户设置-设置交易密码 """

        user: User = g.user
        extra = user.extra
        if extra.has_trade_password:
            raise TradePasswordAlreadyExists

        trade_password = kwargs['trade_password']
        if not validate_trade_password(trade_password):
            raise InvalidTradePassword

        cls.set_trade_password(user, trade_password)
        UserPreferences(user.id).opening_trade_password = True


    @classmethod
    @require_2fa(MobileCodeType.TRADING_PASSWORD_EDIT, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        new_trade_password=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """ 账户设置-修改交易密码 """
        user: User = g.user
        new_trade_password = kwargs['new_trade_password']
        if not validate_trade_password(new_trade_password):
            raise InvalidTradePassword
        cls.set_trade_password(user, new_trade_password, is_update=True)
        UserPreferences(user.id).opening_trade_password = True
        db.session_add_and_commit(
            Message(
                user_id=user.id,
                title=MessageTitle.EDIT_TRADE_PASSWORD.name,
                content=MessageContent.EDIT_TRADE_PASSWORD_SUCCESS.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )
        ResetTradePasswordFailureCache(user.id).delete()
        delete_trade_password_sub_account_failure_cache.delay(user.main_user_id)
        send_trade_password_update_email.delay(user.id)

    @classmethod
    def set_trade_password(cls, user, trade_password, is_update=False):
        if is_update:
            operation = OperationLog.Operation.EDIT_TRADE_PASSWORD
        else:
            operation = OperationLog.Operation.ADD_TRADE_PASSWORD
        user.extra.set_trade_password(trade_password)
        OperationLog.add(user.id, operation, user.email,
                         get_request_platform())


@ns.route('/trade-password/validate')
@respond_with_code
class TradePasswordValidateResource(Resource):

    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    @ns.use_kwargs(dict(
        trade_password=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 验证交易密码 """
        if not validate_trade_password(kwargs['trade_password']):
            raise InvalidTradePassword
        check_trade_password(g.user, kwargs['trade_password'])


@ns.route('/totp')
@respond_with_code
class TotpResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user: User = g.user
        auth_key = new_totp_auth_key()
        TotpKeyCache(user.id).set_key(auth_key)
        return dict(
            totp_auth_key=auth_key
        )

    @classmethod
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        if user.totp_auth_key:
            raise TotpAuthKeyAlreadyExists

        if (is_old_app_request(3340, 62)  # app兼容老设备仅绑定通讯密钥用户
                and user.web_authn_list
                and not bool(user.mobile or user.totp_auth_key)):
            raise InvalidArgument(message=gettext('由于你绑定了通行密钥，APP端暂不支持验证，请到WEB端设置其他2FA'))

        totp_cache = TotpKeyCache(user.id)
        if not (auth_key := totp_cache.value):
            raise TotpAuthKeyExpired
        TotpCodeValidator(auth_key).validate(kwargs['validate_code'])

        if user.has_2fa:
            # 安卓不需兼容
            if not is_old_app_request(0, 96):
                require_2fa(MobileCodeType.TOTP_BINDING, allow_sub_account=False)(lambda: None)()

        user.set_totp_auth_key(auth_key)
        totp_cache.delete()
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)

        UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
        OperationLog.add(user.id, OperationLog.Operation.ADD_TOTP_AUTH, '',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.BIND_TOTP,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )

        db.session_add_and_commit(
            Message(
                user_id=user.id,
                title=MessageTitle.ADD_TOTP_AUTH.name,
                content=MessageContent.ADD_TOTP_AUTH_SUCCESS.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )

        return {}

    @classmethod
    @require_email_code(allow_sub_account=False)
    @require_2fa(MobileCodeType.TOTP_EDIT, allow_sub_account=False)
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """ 账户设置-修改TOTP """
        user: User = g.user
        if not user.totp_auth_key:
            raise TotpAuthKeyAlreadyExists

        totp_cache = TotpKeyCache(user.id)
        if not (auth_key := totp_cache.value):
            raise TotpAuthKeyExpired
        TotpCodeValidator(auth_key).validate(kwargs['validate_code'])

        user.set_totp_auth_key(auth_key)
        totp_cache.delete()
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
        OperationLog.add(user.id, OperationLog.Operation.EDIT_TOTP_AUTH, '',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_TOTP,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )
        send_edit_security_notice_email.delay("totp", user.id)
        return {}
    
    @classmethod
    @require_email_code(allow_sub_account=False)
    @require_2fa(MobileCodeType.TOTP_UNBIND, allow_sub_account=False)
    def patch(cls):
        """ 账户设置-解绑TOTP """
        user: User = g.user
        if not user.totp_auth_key:
            raise InvalidArgument
        user.set_totp_auth_key(None)
        update_user_two_fa_type(user)
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        OperationLog.add(user.id, OperationLog.Operation.RESET_TOTP_AUTH, '',
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.UNBIND_TOTP,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )


@ns.route('/validate/totp')
@respond_with_code
class TotpValidationResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        TotpCodeValidator(user).validate(kwargs['validate_code'])
        operation_token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(operation_token).set_user(user.id, TwoFAType.TOTP)
        UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
        return dict(
            operate_token=operation_token
        )


def _auth_to_dict(auth: ApiAuth, show_secret_key=True) -> dict:
    r = dict(
        user_auth_id=auth.id,
        create_time=auth.created_at,
        remark=auth.remark,
        access_id=auth.access_id,
        allow_ips=auth.allowed_ips,
        allow_withdraw=auth.withdrawals_enabled,
        allow_trade=auth.trading_enabled,
        source=auth.source.name,
        source_name=auth.source_name,
        is_expires=auth.is_expired,
        expired_time=(int(expired_at.timestamp())
                      if (expired_at := auth.expired_at)
                      else 0)
    )
    if show_secret_key:
        r['secret_key'] = auth.secret_key
    return r


@ns.route('/auth/')
@respond_with_code
class AuthResource(Resource):
    pass


@ns.route('/auth/api')
@respond_with_code
class AuthAPIsResource(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.API])
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=10)
    ))
    def get(cls, **kwargs):
        user = g.user
        auth_user = g.auth_user

        query = ApiAuth.query.filter(
            ApiAuth.user_id == user.id,
            ApiAuth.status == ApiAuth.Status.VALID,
        )
        if user.is_sub_account and user != auth_user and not SubAccountManager.has_relationship(
            auth_user.id, user.id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            # 切换到子账号时：主账号可以看到全部API，托管账号只能看到自己创建的API
            query = query.filter(ApiAuth.creator_id == auth_user.id)

        records = query.order_by(ApiAuth.id.desc()).paginate(kwargs['page'], kwargs['limit'])
        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(lambda x: _auth_to_dict(x, False), records.items)),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    @require_2fa(MobileCodeType.API_AUTH_ADDITION)
    @require_user_permission(sub_account_permissions=[SubAccountPermission.API])
    @ns.use_kwargs(dict(
        remark=fields.String(missing=''),
        allow_ips=fields.String(missing=''),
        allow_withdraw=fields.Boolean(required=True),
        allow_trade=fields.Boolean(required=True),
        is_copy_trader=fields.Boolean(required=False),
    ))
    def post(cls, **kwargs):
        user: User = g.user
        require_user_request_permission(g.user)
        auth_user: User = g.auth_user
        api_counts = ApiAuth.query.filter(
            ApiAuth.user_id == user.id,
            ApiAuth.status == ApiAuth.Status.VALID).with_entities(func.count()).scalar() or 0
        if api_counts >= ApiAuth.API_NUM_LIMIT:
            raise ApiAuthCountExceeded

        withdrawals_enabled = kwargs['allow_withdraw']

        param_is_copy_trader = kwargs.get("is_copy_trader")
        _copy_trader_sub_user = None
        if param_is_copy_trader:
            # 跟单API不允许提现权限
            if withdrawals_enabled:
                raise InvalidArgument
            if user.is_sub_account:
                raise InvalidArgument
            _copy_trader_sub_user = get_copy_trader_sub_user(
                user.id,
                require_running=True,
                check_pos_count=False,
                check_data=None,
            )
            api_source = ApiAuth.Source.COPY_TRADER
        else:
            api_source = ApiAuth.Source.SELF

        allowed_ips = list(filter(None, kwargs['allow_ips'].splitlines()))
        if len(allowed_ips) > ApiAuth.ALLOW_IPS_NUM_LIMIT:
            raise InvalidArgument
        if not all(map(validate_ip_address, allowed_ips)):
            raise InvalidArgument(message=gettext('IP地址错误'))

        if user.is_sub_account and withdrawals_enabled:
            raise OperationNotAllowed

        if api_source == ApiAuth.Source.COPY_TRADER:
            add_trader_sub_api_frequency_limit(_copy_trader_sub_user.id)
        row = ApiAuth.new(
            user.id,
            auth_user.id,
            '\n'.join(allowed_ips),
            kwargs['remark'],
            withdrawals_enabled,
            kwargs['allow_trade'],
            None if allowed_ips else ApiAuth.DEFAULT_TTL,
            source=api_source,
        )
        return _auth_to_dict(row)


# noinspection PyUnresolvedReferences
@ns.route('/auth/api/<int:id_>')
@respond_with_code
class AuthAPIResource(Resource):

    @classmethod
    def check_api_auth_owner(cls, row: ApiAuth):
        user = g.user
        auth_user = g.auth_user
        if user.is_sub_account and user != auth_user and not SubAccountManager.has_relationship(
            auth_user.id, user.id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            # 托管账号只能操作自己创建的API
            if row.creator_id != auth_user.id:
                raise InvalidArgument

    @classmethod
    @require_2fa(MobileCodeType.API_AUTH_QUERY)
    @require_user_permission(sub_account_permissions=[SubAccountPermission.API])
    def get(cls, id_):
        row = cls.get_valid_row(id_)
        return _auth_to_dict(row)

    @classmethod
    @require_2fa(MobileCodeType.API_AUTH_EDIT)
    @require_user_permission(sub_account_permissions=[SubAccountPermission.API])
    @ns.use_kwargs(dict(
        remark=fields.String(missing=''),
        allow_ips=fields.String(missing=''),
        allow_withdraw=fields.Boolean(required=True),
        allow_trade=fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        user: User = g.user
        require_user_request_permission(g.user)
        row = cls.get_valid_row(id_)
        cls.check_api_auth_owner(row)

        withdrawals_enabled = kwargs['allow_withdraw']
        if user.is_sub_account and withdrawals_enabled:
            raise OperationNotAllowed
        if row.source == ApiAuth.Source.COPY_TRADER and withdrawals_enabled:
            raise InvalidArgument

        allowed_ips_lis = list(filter(None, kwargs['allow_ips'].splitlines()))
        if len(allowed_ips_lis) > ApiAuth.ALLOW_IPS_NUM_LIMIT:
            raise InvalidArgument(message=gettext('超出最大上限，IP最多可配置50个'))
        if not all(map(validate_ip_address, allowed_ips_lis)):
            raise InvalidArgument(message=gettext('IP地址错误'))
        row.remark = kwargs['remark']
        row.allowed_ips = (allowed_ips := kwargs['allow_ips'])
        row.withdrawals_enabled = kwargs['allow_withdraw']
        row.trading_enabled = kwargs['allow_trade']
        row.expired_at = (
            None if allowed_ips
            else now() + timedelta(seconds=ApiAuth.DEFAULT_TTL))
        db.session.commit()
        ApiAuthCache(row.access_id).delete()
        return _auth_to_dict(row)

    @classmethod
    @require_2fa(MobileCodeType.API_AUTH_DELETION)
    def delete(cls, id_):
        row = cls.get_valid_row(id_)
        cls.check_api_auth_owner(row)
        row.status = ApiAuth.Status.DELETED
        db.session.commit()
        ApiAuthCache(row.access_id).delete()
        return {}

    @classmethod
    @require_2fa(MobileCodeType.API_AUTH_EXTEND)
    @require_user_permission(sub_account_permissions=[SubAccountPermission.API])
    def patch(cls, id_):
        row = cls.get_valid_row(id_)
        cls.check_api_auth_owner(row)
        now_ = now()
        if not row.expired_at or row.expired_at <= now_:
            raise ApiExtendInvalid
        row.expired_at = now_ + timedelta(days=90)
        db.session.commit()

    @classmethod
    def get_valid_row(cls, id_):
        row: ApiAuth = ApiAuth.query \
            .filter(ApiAuth.id == id_,
                    ApiAuth.user_id == g.user.id,
                    ApiAuth.status == ApiAuth.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound
        return row


@ns.route('/auth/api/extend/<token>')
@respond_with_code
class AuthAPIExtendResource(Resource):

    @classmethod
    def put(cls, token):
        """APIKEY续期"""
        require_user_request_permission(None, [RequestPermissionCheck.IP])
        row = EmailToken.validate(token, EmailToken.EmailType.API_EXPIRATION_EXTEND)
        if row is None:
            raise InvalidLink
        data = row.data_json
        api_auth_id = data['api_auth_id']
        record = ApiAuth.query.get(api_auth_id)
        now_ = now()
        if record is None or record.status == ApiAuth.Status.DELETED:
            raise InvalidLink
        if not record.expired_at or record.expired_at > now_ + timedelta(hours=72):
            raise ApiExtendInvalid
        user = User.query.get(record.user_id)
        require_user_request_permission(user, [RequestPermissionCheck.USER_NOT_ONLY_WITHDRAWAL, RequestPermissionCheck.KYC])
        record.expired_at = record.expired_at + timedelta(days=90)
        db.session.commit()

    @classmethod
    def get(cls, token):
        """获取APIKEY续期相关信息"""
        row = EmailToken.validate(token, EmailToken.EmailType.API_EXPIRATION_EXTEND)
        if row is None:
            raise InvalidLink
        data = row.data_json
        api_auth_id = data['api_auth_id']
        record = ApiAuth.query.get(api_auth_id)
        now_ = now()
        if record is None or record.status == ApiAuth.Status.DELETED:
            raise InvalidLink
        expired_at = record.expired_at
        if not expired_at or expired_at > now_ + timedelta(hours=72):  # 已经绑定IP白名单或已经延期过了
            raise ApiExtendInvalid
        user = record.user
        pref = UserPreferences(user.id)
        name = user.name_displayed
        lang = pref.language.value
        remark = record.remark
        return dict(name=name, lang=lang, remark=remark, expired_at=expired_at)


@ns.route('/sign/reset/2fa')
@respond_with_code
class PasswordReset2FAResource(Resource):
    class _2FAType(Enum):
        MOBILE = 'mobile'
        TOTP = 'totp'
        WEBAUTHN = 'webauthn'

    @classmethod
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        validate_code=fields.String(required=True),
        type=EnumField(_2FAType, required=True),
        credential=fields.Dict(),
    ))
    def post(cls, **kwargs):
        """
        非登录态重置密码2fa验证
        """
        cache = EmailCodeTokenCache(kwargs['email_code_token'])
        email = cache.get_email()
        if not email:
            raise InvalidArgument
        user = User.query.filter(User.email == email).first()
        if not user or user.is_sub_account:
            raise InvalidArgument
        try:
            verify_user_permission(user.id)
        except AuthenticationFailed:
            raise OperationDenied
        _2fa_type = kwargs['type']
        if _2fa_type == cls._2FAType.TOTP:
            # TOTP 验证
            TotpCodeValidator(user).validate(kwargs['validate_code'])
        elif _2fa_type == cls._2FAType.WEBAUTHN:
            WebauthnCredentialValidator(user).validate(kwargs['credential'])
        else:
            # 短信 验证
            country_code, mobile = user.mobile_country_code, user.mobile_num
            code = kwargs['validate_code']
            MobileCodeValidator(country_code, mobile,
                                MobileCodeType.NON_LOGIN_PASSWORD_RESET).validate(code)
        operate_token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(operate_token).set_user(user.id, _2fa_type)
        UserPreferences(user.id).two_fa_type = TwoFAType(_2fa_type.value)
        return {
            "operate_token": operate_token
        }


@ns.route('/sign/reset/by/email')
@respond_with_code
class PasswordResetByEmailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        login_password=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """ (主账号登入态)账户设置-重置密码"""
        password = kwargs['login_password']
        if not validate_password(password):
            raise InvalidPassword
        user = g.user
        cache = EmailCodeTokenCache(kwargs['email_code_token'])
        _, _email = cache.get_user_and_email()
        if _email != user.email:
            raise EmailCodeVerificationFailed
        if user.has_2fa:
            # 这段逻辑考虑短信验证码区分首次设置与二次修改的情况，因此先尝试验证二次修改的短信验证码，失败时再尝试首次设置的短信验证码
            try:
                require_2fa(MobileCodeType.LOGIN_PASSWORD_EDIT)(lambda: None)()
            except InvalidMobileVerificationCode:
                require_2fa(MobileCodeType.LOGIN_PASSWORD_SET)(lambda: None)()
        cache.delete()
        had_password = bool(user.login_password_hash)
        user.set_login_password(password)
        cur_login_token = g.get("login_token")
        exclude_tokens = [cur_login_token] if cur_login_token else []
        tokens = UserLoginTokenCache(user.id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        if not had_password:
            op_log_type = OperationLog.Operation.THIRD_PARTY_FIRST_SET_LOGIN_PASSWORD  # 第三方首次设置密码
        else:
            op_log_type = OperationLog.Operation.RESET_LOGIN_PASSWORD
        OperationLog.add(user.id, op_log_type, user.email, get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_PWD,
            SecurityToolHistory.OpRole.USER,
            user.email,
        )
        if had_password:
            send_edit_security_notice_email.delay("login_password", user.id)
        else:
            send_set_login_password_success_notice_email.delay(user.id)


@ns.route('/security/reset/password')
@respond_with_code
class NonLoginPasswordResetByEmailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        email_code_token=fields.String(required=True),
        login_password=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """ (主账号非登入态)忘记/重置密码"""
        email = kwargs['email'].strip()

        password = kwargs['login_password']
        if not validate_password(password):
            raise InvalidPassword

        cache = EmailCodeTokenCache(kwargs['email_code_token'])
        _email = cache.get_email()
        if _email != email:
            raise EmailCodeVerificationFailed
        user = require_email_exists(_email)
        if user.is_sub_account:
            raise InvalidArgument
        try:
            verify_user_permission(user.id)
        except AuthenticationFailed:
            raise OperationDenied
        if user.has_2fa:
            g.auth_user = g.user = user
            require_2fa(MobileCodeType.NON_LOGIN_PASSWORD_RESET)(lambda: None)()
        cache.delete()
        had_password = bool(user.login_password_hash)
        if not had_password:
            op_log_type = OperationLog.Operation.THIRD_PARTY_FIRST_SET_LOGIN_PASSWORD  # 第三方首次设置密码
        else:
            op_log_type = OperationLog.Operation.RESET_LOGIN_PASSWORD
        user.set_login_password(password)
        tokens = UserLoginTokenCache(user.id).clear_tokens()
        UserLoginState.clear_tokens(tokens)
        OperationLog.add(user.id, op_log_type,
                         email,
                         get_request_platform())
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_PWD,
            SecurityToolHistory.OpRole.USER,
            email,
        )
        if had_password:
            send_edit_security_notice_email.delay("login_password", user.id)
        else:
            send_set_login_password_success_notice_email.delay(user.id)


@ns.route('/info')
@respond_with_code
class NonLoginUserInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        email_code_token=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """ 非登入态获取用户信息 """
        email = kwargs['email'].strip()
        cache = EmailCodeTokenCache(kwargs['email_code_token'])
        _email = cache.get_email()
        if _email != email:
            raise EmailCodeVerificationFailed
        user = require_email_exists(_email)
        if user.is_sub_account:
            raise InvalidArgument
        res = dict(
            country_code=(str(country_code)
                          if (country_code := user.mobile_country_code)
                          else None),
            mobile=(hide_text_default(mobile_num)
                    if (mobile_num := user.mobile_num)
                    else None),
            origin_mobile=user.mobile_num,
            has_totp_auth=bool(user.totp_auth_key),
            has_webauthn=bool(user.web_authn_list),
            protect_type=UserPreferences(user.id).two_fa_type.value,
        )

        if is_old_app_request(0, 69):
            mobile_fields = ('country_code', 'mobile', 'origin_mobile')
            for f in mobile_fields:
                if not res.get(f):
                    res.pop(f, None)
            res['protect_type'] = res['protect_type'] or 'none'
        return res


@ns.route('/sign/auth/reset/by/email')
@respond_with_code
class AuthPasswordResetByEmailResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        login_password=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """ (子账号登入态)账户设置-重置密码 """
        user: User = g.user
        if user.is_sub_account:
            raise InvalidArgument
        email = user.main_user_email
        user_id = user.id

        password = kwargs['login_password']
        if not validate_password(password):
            raise InvalidPassword

        cache = EmailCodeTokenCache(kwargs['email_code_token'])
        _, _email = cache.get_user_and_email()
        if _email != email:
            raise EmailCodeVerificationFailed

        cache.delete()
        user: User = User.query.get(user_id)
        user.set_login_password(password)
        exclude_tokens = [g.login_token]
        tokens = UserLoginTokenCache(user_id).clear_tokens(exclude_tokens=exclude_tokens)
        UserLoginState.clear_tokens(tokens)
        OperationLog.add(user.id, OperationLog.Operation.RESET_LOGIN_PASSWORD, email,
                         get_request_platform())


@ns.route('/safety/user')
@respond_with_code
class SafetyUserResource(Resource):

    @classmethod
    def _user_to_dict(cls, user: User):
        return dict(
            has_mobile=bool(user.mobile),
            login_password_level=user.login_password_level.value,
            login_password_update_time=user.login_password_updated_at,

            # APP兼容字段，待删除
            trade_password_update_time=0,
            trade_password_frequency="never"
        )

    @classmethod
    @require_login
    def get(cls):
        return cls._user_to_dict(g.user)

    @classmethod
    def put(cls):
        raise OfflineFeature


@ns.route('/safety/forbid')
@respond_with_code
class SafetyForbidResource(Resource):
    class Reason(Enum):
        ABNORMAL = "异地登录/被盗"
        STOP = "停用账号"
        OTHER = "其它"

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        reason=EnumField(Reason, required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        user_id = user.id
        reason = kwargs['reason']

        OperationLog.add(user.id, OperationLog.Operation.FORBID_ACCOUNT,
                         reason.value, get_request_platform())
        UnfreezeAccountHelper.freeze_account(user_id)
        tokens = UserLoginTokenCache(user_id).clear_tokens()
        UserLoginState.clear_tokens(tokens)

        # 删除主子账号的API、解绑自己子账号的授权、解绑他人给主账号的授权
        sub_accounts = SubAccount.query.filter(
            SubAccount.main_user_id == user_id,
            SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
        ).with_entities(SubAccount.user_id).all()
        sub_user_ids = [i.user_id for i in sub_accounts]
        all_user_ids = [user_id] + sub_user_ids
        ApiAuth.query.filter(
            ApiAuth.user_id.in_(all_user_ids)
        ).update(
            dict(status=ApiAuth.Status.DELETED),
            synchronize_session=False,
        )
        delete_sub_account_manager_relation(main_user_id=user_id)
        db.session.commit()

        with CacheLock(LockKeys.user_withdrawal(user_id)):
            for w in Withdrawal.query \
                    .filter(Withdrawal.user_id == user_id,
                            Withdrawal.status.in_(
                                [Withdrawal.Status.CREATED,
                                 Withdrawal.Status.AUDIT_REQUIRED,
                                 Withdrawal.Status.AUDITED])):
                cancel_withdrawal(w.id, cancel_type=WithdrawalCancel.CancelType.USER, cancel_user_id=user_id)

        svr_client = ServerClient()
        while True:
            orders = svr_client.user_pending_orders(user_id, limit=100, page=1)
            for order in orders:
                svr_client.cancel_user_order(
                    user_id, order['market'], order['id'])
            if not orders.has_next:
                break
        while True:
            orders = svr_client.user_pending_stop_orders(user_id, limit=100, page=1)
            for order in orders:
                svr_client.cancel_user_stop_order(
                    user_id, order['market'], order['id'])
            if not orders.has_next:
                break

        ppt_client = PerpetualServerClient()
        limit = 100 - 1
        while True:
            orders = ppt_client.pending_order(user_id, None, limit=limit + 1
                                              )['records']
            for order in orders:
                ppt_client.cancel_order(
                    user_id, order['market'], order['order_id'])
            if len(orders) <= limit:
                break
        while True:
            orders = ppt_client.pending_stop(user_id, None, limit=limit + 1
                                             )['records']
            for order in orders:
                ppt_client.cancel_stop(
                    user_id, order['market'], order['order_id'])
            if len(orders) <= limit:
                break
        update_security_statistics([user_id], SecuritySettingType.FREEZE_ACCOUNT)
        return {}


@ns.route('/safety/login')
@respond_with_code
class SafetyLoginResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(10, 1800)
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        login_password=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """App设置指纹/手势密码时校验登录密码"""
        if not g.user.check_login_password(kwargs['login_password']):
            raise InvalidUsernameOrPassword
        return {}


@ns.route('/anti-phishing-code')
@respond_with_code
class AntiPhishingCodeResource(Resource):

    @classmethod
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        anti_phishing_code=fields.String(
            required=True, example='1234, 防钓鱼码, 4-20位')
    ))
    def post(cls, **kwargs):
        """添加防钓鱼码"""
        anti_phishing_code = kwargs['anti_phishing_code']
        if not validate_anti_phishing_code(anti_phishing_code):
            raise InvalidArgument

        OperationLog.add(g.user.id,
                         OperationLog.Operation.ADD_ANTI_PHISHING_CODE,
                         anti_phishing_code,
                         get_request_platform())

        UserPreferences(g.user.id).anti_phishing_code = anti_phishing_code
        return

    @classmethod
    @require_email_code(allow_sub_account=False)
    @ns.use_kwargs(dict(
        anti_phishing_code=fields.String(
            required=True, example='1234, 防钓鱼码, 4-20位')
    ))
    def put(cls, **kwargs):
        """编辑防钓鱼码"""
        anti_phishing_code = kwargs['anti_phishing_code']
        if not validate_anti_phishing_code(anti_phishing_code):
            raise InvalidArgument

        OperationLog.add(g.user.id,
                         OperationLog.Operation.EDIT_ANTI_PHISHING_CODED,
                         anti_phishing_code,
                         get_request_platform())

        UserPreferences(g.user.id).anti_phishing_code = anti_phishing_code
        return


@ns.route('/binding/accounts')
@respond_with_code
class BindingAccountsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        account_type=EnumField(UserBindingAccount.AccountType, enum_by_value=True)
    ))
    def get(cls, **kwargs):
        query = UserBindingAccount.query.filter(
            UserBindingAccount.user_id == g.user.id,
            UserBindingAccount.status == UserBindingAccount.Status.VALID
        )
        if account_type := kwargs.get('account_type'):
            query = query.filter(UserBindingAccount.account_type == account_type)
        result = [dict(
            user_id=x.user_id,
            account_type=x.account_type,
            account_name=x.account_name
        ) for x in query]
        return result


@ns.route('/binding/telegram/url')
@respond_with_code
class TelegramBindingUrlResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(10, 1800)
    def get(cls):
        user_id = g.user.id
        token = new_hex_token()
        cache = TelegramBindingTokenCache(token)
        cache.set(user_id, ex=cache.ttl)
        bot = config['TELEGRAM_BOT']['name']
        return {'url': f'https://t.me/{bot}?start={token}'}


class SecurityMixin:

    @classmethod
    def is_exceed_submit_limit(cls, user_id):
        cache = SecurityResetFrequencyCache(user_id)
        return cache.count() >= cache.count_limit

    @classmethod
    def can_liveness(cls, user_id: int) -> bool:
        applications = SecurityResetApplication.query.filter(
            SecurityResetApplication.status == SecurityResetApplication.StatusType.REJECTED,
            SecurityResetApplication.user_id == user_id,
        ).all()
        reason_types = SecurityResetApplication.Reason
        for item in applications:
            if ({reason_types.INVALID_KYC_INFORMATION, reason_types.UNFREEZE_INVALID_KYC_INFORMATION}
                    & set(item.get_reason_list())):
                return False
        try:
            LivenessCheckBusiness.upload_kyc_data(user_id)
            return True
        except Exception as e:  # noqa
            current_app.logger.error(f"LivenessCheckBusiness upload_kyc_data {user_id} error:{e}")
            return False


@ns.route('/security/reset/account')
@respond_with_code
class SecurityVerificationResource(SecurityMixin, Resource):

    @classmethod
    def get_processing_record(cls, user_id: int):
        record = SecurityResetApplication.query.filter(
            SecurityResetApplication.status.in_((SecurityResetApplication.StatusType.CREATED,
                                                 SecurityResetApplication.StatusType.AUDITED,
                                                 SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE)),
            SecurityResetApplication.reset_type.in_(SecurityResetApplication.TWO_FA_RESET_LIST),
            SecurityResetApplication.user_id == user_id,
        ).first()
        return record

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        account=EmailField(required=True),
        login_password=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """自助重置安全工具，校验账号密码"""
        account: str = kwargs['account'].strip()
        password: str = kwargs['login_password']

        user = SignInResource.do_sign_in(account, password)

        user_id = user.id
        if cls.is_exceed_submit_limit(user_id):
            raise FrequencyExceeded(message=gettext("24H内只允许提交5次"))
        try:
            verify_user_permission(user_id)
        except AuthenticationFailed:
            raise OperationDenied
        token = new_hex_token(LOGIN_TOKEN_SIZE)
        cache = SecurityOperationCache(token)
        cache.hmset(dict(
                user_id=str(user.id),
            ))
        cache.expire()
        if cls.get_processing_record(user_id):
            raise DuplicateSecurityResetApplication
        balance = SecurityQuestionBusiness.get_user_current_balance_usd(user.id)
        return dict(
            operate_token=token,
            email=user.hidden_email,
            uncensored_email=user.email,
            has_totp_auth=bool(user.totp_auth_key),
            has_webauthn=bool(user.web_authn_list),
            has_balance=balance > 0,
            has_kyc=user.kyc_status == User.KYCStatus.PASSED,
            country_code=user.mobile_country_code,
            mobile=(hide_text_default(mobile_num)
                    if (mobile_num := user.mobile_num) else None),
            origin_mobile=user.mobile_num,
            can_liveness=cls.can_liveness(user_id),
        )


@ns.route('/security/reset/account-forget')
@respond_with_code
class NonSensitiveUserInfoResource(SecurityMixin, Resource):

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        flow_case=EnumField(SecurityResetApplication.FlowCase, required=True),
        reset_type=EnumField(SecurityResetApplication.ResetType, required=True),
    ))
    def get(cls, **kwargs):
        """ 异常流程-非登入态获取用户非敏感信息 """
        email = kwargs['email'].strip()
        try:
            user = require_email_exists(email)
        except EmailDoesNotExist:
            raise OperationDenied(message=gettext('用户名错误或权限受限'))
        if kwargs['flow_case'] is SecurityResetApplication.FlowCase.NORMAL:
            raise InvalidArgument
        if cls.is_exceed_submit_limit(user.id):
            raise FrequencyExceeded(message=gettext("24H内只允许提交5次"))
        if user.is_sub_account:
            raise InvalidArgument
        try:
            verify_user_permission(user.id)
        except AuthenticationFailed:
            raise OperationDenied(message=gettext('用户名错误或权限受限'))
        if SecurityVerificationResource.get_processing_record(user.id):
            raise DuplicateSecurityResetApplication
        token = new_hex_token(LOGIN_TOKEN_SIZE)
        cache = SecurityOperationCache(token)
        cache.hmset(dict(
            user_id=str(user.id),
            flow_case=kwargs['flow_case'].name
        ))
        cache.expire()

        balance = SecurityQuestionBusiness.get_user_current_balance_usd(user.id)
        return dict(
            operate_token=token,
            email=user.hidden_email,
            uncensored_email=user.email,
            has_mobile=bool(user.mobile_num),
            has_balance=balance > 0,
            has_kyc=user.kyc_status == User.KYCStatus.PASSED,
            has_totp_auth=bool(user.totp_auth_key),
            has_webauthn=bool(user.web_authn_list),
            protect_type=UserPreferences(user.id).two_fa_type.value,
            can_liveness=cls.can_liveness(user.id),
        )


@ns.route('/security/reset/answer')
@respond_with_code
class SecurityQuestionResource(Resource):
    _required_question_count = 4

    @classmethod
    @require_security_reset_token
    def get(cls, **kwargs):
        if g.user.is_sub_account:
            raise InvalidArgument
        user_id = g.user.id
        reset_method = g.security_reset_info.get('reset_method')
        if reset_method != SecurityResetMethod.ANSWER.name:
            raise InvalidArgument(
                message=gettext("Sorry, you are not qualified to answer questions"))
        questions = SecurityQuestionBusiness.get_questions(user_id)
        return dict(
            questions=questions,
            opportunity_count=SecurityQuestionBusiness.get_opportunity_count(user_id)
        )

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        reset_type=EnumField(SecurityResetApplication.ResetType, required=True),
        answer=fields.List(fields.Dict, required=True),
        email_code_token=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        reset_type = kwargs['reset_type']
        if reset_type not in SecurityResetApplication.TWO_FA_RESET_LIST:
            raise InvalidArgument
        answer = kwargs['answer']
        operation_cache = SecurityOperationCache(g.operate_token)
        user_info = g.security_reset_info
        user_id, reset_method = int(user_info['user_id']), user_info['reset_method']
        email_token = kwargs['email_code_token']
        email_cache = EmailCodeTokenCache(email_token)
        id_, new_email = email_cache.get_user_and_email()
        if not id_ or id_ != user_id:
            raise InvalidSecurityResetToken
        if reset_type == SecurityResetApplication.ResetType.EMAIL:
            if not new_email:
                raise InvalidArgument
            require_email_not_exists(new_email)

        user = User.query.get(user_id)
        if user.is_sub_account:
            raise InvalidArgument
        failed_count = SecurityQuestionBusiness.get_failure_attempt_count(user_id)
        if reset_method != SecurityResetMethod.ANSWER.name \
                or failed_count >= SecurityQuestionBusiness.MAX_ALLOWED_ATTEMPTS:
            raise InvalidArgument(message=gettext("Sorry, you are not qualified to answer questions"))
        provided_question_ids = set()
        available_question_ids = {item.name for item in QuestionType}
        for item in answer:
            if item.keys() != {"subject_id", "answers"} or not \
                    isinstance(item['answers'], list) or \
                    item['subject_id'] not in available_question_ids:
                raise InvalidArgument
            for a in item['answers']:
                # 须以字母作答
                if not (len(a) == 1 and "A" <= a <= "Z"):
                    raise InvalidArgument
            provided_question_ids.add(item['subject_id'])

        # 须回答4个问题
        if len(provided_question_ids) != cls._required_question_count:
            raise InvalidArgument(message="Invalid answer count.")
        has_opportunity = True
        with CacheLock(LockKeys.security_question(user_id)):
            question_detail = dict(
                questions=SecurityQuestionBusiness.get_question_answers(user_id),
                answers=answer
            )
            is_correct = SecurityQuestionBusiness.validate_answers(user_id, answer)
            if not is_correct:
                # 回答不正确
                failed_count += 1
                if failed_count >= SecurityQuestionBusiness.MAX_ALLOWED_ATTEMPTS:
                    has_opportunity = False
                    operation_cache.set_reset_method(SecurityResetMethod.CUSTOMER.name)

                history = SecurityResetAnswerHistory(
                    user_id=user_id,
                    status=SecurityResetAnswerHistory.Status.FAILED,
                    reset_type=reset_type,
                    new_email=new_email,
                    detail=json.dumps(question_detail)
                )
                db.session.add(history)
                db.session.commit()
            else:
                application = SecurityResetApplication(
                    user_id=user_id,
                    reset_type=reset_type,
                    data_type=SecurityResetFile.NO_NEED_FILE,
                    balance_usd=Decimal(user_info['balance_usd']),
                    ip=get_request_ip(),
                    platform=get_request_platform().value,
                    audited_at=now(),
                    status=SecurityResetApplication.StatusType.PASSED
                )
                if new_email:
                    application.new_email = new_email
                db.session.add(application)
                history = SecurityResetAnswerHistory(
                    user_id=user_id,
                    status=SecurityResetAnswerHistory.Status.SUCCEEDED,
                    reset_type=reset_type,
                    new_email=new_email,
                    detail=json.dumps(question_detail)
                )
                db.session.add(history)
                # 直接为用户重置
                reset_security_info(user, reset_type, SecurityToolHistory.OpRole.ANSWER, new_email=new_email)
                db.session.commit()
                email_cache.delete()
                operation_cache.delete()
                send_reset_security_notice_email.delay(
                    user_id, reset_type.name, SecurityResetApplication.StatusType.PASSED.name)
                # 站内信
                if reset_type == SecurityResetApplication.ResetType.EMAIL:
                    content = MessageContent.SECURITY_RESET_EMAIL_SUCCESS.name
                else:
                    content = MessageContent.SECURITY_RESET_2FA_SUCCESS.name
                db.session.add(Message(
                    user_id=user_id,
                    title=MessageTitle.SECURITY_RESET_SUCCESS.name,
                    content=content,
                    params=json.dumps({
                        'reset_type': reset_type.value
                    }),
                    extra_info=json.dumps(
                        dict(
                            web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                            android_link="",
                            ios_link="",
                        )
                    ),
                    display_type=Message.DisplayType.TEXT,
                    channel=Message.Channel.ACCOUNT_SECURITY,
                ))
                db.session.commit()
                SecurityResetFrequencyCache(user_id).add_value()
            SecurityQuestionBusiness.clear_questions(user_id)

        return dict(
            correct=is_correct,
            has_opportunity=has_opportunity
        )


@ns.route('/security/reset/2fa')
@respond_with_code
class Security2FAResource(Resource):
    class _2FAType(Enum):
        MOBILE = 'mobile'
        TOTP = 'totp'
        WEBAUTHN = 'webauthn'

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True),
        type=EnumField(_2FAType, required=True),
        credential=fields.Dict(),
    ))
    def post(cls, **kwargs):
        _2fa_type = kwargs['type']
        user = g.user
        if user.is_sub_account:
            raise InvalidArgument

        if _2fa_type == cls._2FAType.TOTP:
            # TOTP 验证
            TotpCodeValidator(user).validate(kwargs['validate_code'])
        elif _2fa_type == cls._2FAType.WEBAUTHN:
            WebauthnCredentialValidator(user).validate(kwargs['credential'])
        else:
            # 短信 验证
            country_code, mobile = user.mobile_country_code, user.mobile_num

            code = kwargs['validate_code']
            MobileCodeValidator(country_code, mobile, MobileCodeType.SECURITY_RESET).validate(code)

        operation_token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(operation_token).set_user(user.id, _2fa_type)
        UserPreferences(user.id).two_fa_type = TwoFAType(_2fa_type.value)

        return {
            "2fa_token": operation_token
        }


# 此接口用于新邮箱验证
@ns.route('/security/reset/email')
@respond_with_code
class SecurityNewEmailResource(Resource):

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs({
        "email": EmailField(required=True),
        "validate_code": fields.String(required=True),
        "2fa_token": fields.String,
        "flow_case": EnumField(SecurityResetApplication.FlowCase, missing=SecurityResetApplication.FlowCase.NORMAL),
        "reset_type": EnumField(SecurityResetApplication.ResetType, missing=SecurityResetApplication.ResetType.TOTP)
    })
    def post(cls, **kwargs):
        email = kwargs['email'].strip()
        user = g.user
        if email.lower() == user.email.lower():
            raise UserEmailRepeat
        _2fa_token = kwargs.get('2fa_token')
        if user.is_sub_account:
            raise InvalidArgument
        _2fa_cache = UserOperationTokenCache(_2fa_token)
        if user.has_2fa:
            if kwargs['flow_case'] is SecurityResetApplication.FlowCase.NORMAL:
                id_ = _2fa_cache.get_user()
                if not id_ or id_ != user.id:
                    raise InvalidSecurityResetToken
        require_email_not_exists(email)
        code = kwargs['validate_code']
        EmailCodeValidator(email, EmailCodeType.NON_LOGIN_EMAIL_RESET).validate(code)
        require_email_not_exists(email)
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        EmailCodeTokenCache(token).set_user_and_email(user.id, email)
        balance_usd = SecurityQuestionBusiness.get_user_current_balance_usd(user.id)
        if kwargs['flow_case'] is not SecurityResetApplication.FlowCase.NORMAL:
            reset_method = SecurityResetMethod.CUSTOMER
        else:
            has_opportunity = SecurityQuestionBusiness.has_answer_opportunity(user.id)
            if not has_opportunity:
                reset_method = SecurityResetMethod.CUSTOMER
            else:
                reset_method = SecurityResetMethod.ANSWER \
                    if balance_usd < Decimal('10000') else SecurityResetMethod.CUSTOMER

        SecurityOperationCache(g.operate_token).hmset(dict(
            reset_method=reset_method.name,
            balance_usd=amount_to_str(balance_usd),
            flow_case=kwargs['flow_case'].name
        ))
        _2fa_cache.delete()
        file_type = SecurityFileBusiness.get_user_identity_type(
            user, balance_usd=balance_usd, reset_type=kwargs['reset_type'], flow_case=kwargs['flow_case'])

        return dict(
            email_code_token=token,
            reset_method=reset_method.name,
            file_type=file_type.value
        )


@ns.route('/security/statistics')
@respond_with_code
class SecurityStatisticsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True, vdalidate=lambda x: x > 0),
        type=EnumField(SecuritySettingType, required=True)
    ))
    def get(cls, **kwargs):
        update_security_statistics([kwargs['user_id']], kwargs['type'])
        return {}


@ns.route('/unfreeze/account')
@respond_with_code
class UnfreezeAccountResource(Resource):

    @classmethod
    def get_processing_record(cls, user_id: int):
        record = SecurityResetApplication.query.filter(
            SecurityResetApplication.status.in_([SecurityResetApplication.StatusType.CREATED,
                                                 SecurityResetApplication.StatusType.AUDITED,
                                                 SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE]),
            SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_UNFREEZE_RESET_LIST),
            SecurityResetApplication.user_id == user_id,
        ).first()
        return record

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        account=EmailField(required=True),
        login_password=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        account: str = kwargs['account'].strip()
        password: str = kwargs['login_password']

        user = SignInResource.do_sign_in(account, password)

        user_id = user.id
        if UserVisitPermissionCache().check_user_permission(user_id,
                                                            [UserVisitPermissionCache.FORBIDDEN_VALUE]):
            raise OperationDenied
        if not UnfreezeAccountHelper.has_user(user_id):
            raise OperationDenied(message=gettext("该账号未被自助冻结，无法提交"))
        token = new_hex_token(LOGIN_TOKEN_SIZE)
        cache = SecurityOperationCache(token)
        cache.hset('user_id', str(user_id))
        cache.expire()
        application = cls.get_processing_record(user_id)
        if application:
            raise DuplicateSecurityResetApplication
        return dict(
            operate_token=token,
            email=user.hidden_email,
            uncensored_email=user.email,
        )


@ns.route('/unfreeze/email')
@respond_with_code
class UnfreezeEmailResource(Resource):

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs({
        "email": EmailField(required=True),
        "validate_code": fields.String(required=True),
    })
    def post(cls, **kwargs):
        email = kwargs['email'].strip()
        user = g.user
        if cls.is_exceed_submit_limit(user.id):
            raise FrequencyExceeded(message=gettext("24H内只允许提交5次"))
        require_email_exists(email)
        code = kwargs['validate_code']
        EmailCodeValidator(email, EmailCodeType.UNFREEZE_ACCOUNT).validate(code)
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        EmailCodeTokenCache(token).set_user_and_email(user.id, email)
        balance_usd = SecurityQuestionBusiness.get_user_current_balance_usd(user.id)
        SecurityOperationCache(g.operate_token).hmset(dict(
            reset_method=SecurityResetMethod.UNFREEZE_ACCOUNT.name,
            balance_usd=amount_to_str(balance_usd)
        ))
        file_type = SecurityFileBusiness.get_user_identity_type(
            user, reset_type=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT, support_liveness=True)

        return dict(
            email_code_token=token,
            reset_method=SecurityResetMethod.UNFREEZE_ACCOUNT.name,
            file_type=file_type.value
        )

    @classmethod
    def is_exceed_submit_limit(cls, user_id):
        cache = UnfreezeAccountFrequencyCache(user_id)
        return cache.count() >= cache.count_limit


@ns.route('/unfreeze/submit')
@respond_with_code
class UnfreezeSubmitResource(Resource):

    @classmethod
    def get_current_identity_type(cls, user: User):
        identity_type_map = {
            SecurityFileBusiness.UserIdentityType.KYC_USER: SecurityResetApplication.IdentityType.KYC_USER,
            SecurityFileBusiness.UserIdentityType.LIVENESS_CHECK: SecurityResetApplication.IdentityType.KYC_USER,
            SecurityFileBusiness.UserIdentityType.NO_KYC_WITHDRAW_DEPOSIT: SecurityResetApplication.IdentityType.NO_KYC_WITHDRAW_DEPOSIT,
        }
        identity_type = SecurityFileBusiness.get_user_identity_type(
            user, reset_type=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT, support_liveness=True)

        if identity_type in identity_type_map:
            return identity_type_map[identity_type]
        else:
            if ServerClient().get_user_balance_history(user.id, limit=1):
                return SecurityResetApplication.IdentityType.NO_KYC_NO_WITHDRAW_DEPOSIT_HISTORY
            return SecurityResetApplication.IdentityType.NO_KYC_NO_WITHDRAW_DEPOSIT_NO_HISTORY

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        face_with_id_card=fields.String,
        history_notice_snapshot=fields.String,
        third_party_deposit_snapshot=fields.String,
        id_card_front=fields.String,
        id_card_back=fields.String,
    ))
    def post(cls, **kwargs):
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user = g.user
        user_id = user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed
        if reset_method != SecurityResetMethod.UNFREEZE_ACCOUNT.name:
            raise OperationNotAllowed
        if UserSettings(user_id).login_enabled:
            raise InvalidArgument

        img_map = dict()
        img_map[SecurityResetFile.FileType.FACE_WITH_ID_CARD] = \
            kwargs.get('face_with_id_card')
        img_map[SecurityResetFile.FileType.HISTORY_NOTICE_SNAPSHOT] = \
            kwargs.get('history_notice_snapshot')
        img_map[SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_SNAPSHOT] = \
            kwargs.get('third_party_deposit_snapshot')
        img_map[SecurityResetFile.FileType.ID_CARD_FRONT] = \
            kwargs.get('id_card_front')
        img_map[SecurityResetFile.FileType.ID_CARD_BACK] = \
            kwargs.get('id_card_back')

        balance_usd = Decimal(user_info['balance_usd'])
        identity = SecurityFileBusiness.get_user_identity_type(
            user, reset_type=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT, support_liveness=True)
        required_file_type = SecurityFileBusiness.get_required_unfreeze_file_type(identity)

        # check existence
        for type_, img in img_map.items():
            if type_ in required_file_type:
                if not img:
                    raise InvalidArgument(message=gettext("有资料未提交，请补充"))
            else:
                if img:
                    raise InvalidArgument
        application = UnfreezeAccountResource.get_processing_record(user_id)
        if application:
            raise DuplicateSecurityResetApplication
        application = SecurityResetApplication(
            user_id=user_id,
            balance_usd=balance_usd,
            ip=get_request_ip(),
            reset_type=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT,
            platform=get_request_platform().value,
            identity_type=cls.get_current_identity_type(user),
        )
        db.session.add(application)
        db.session.flush()
        for type_, img in img_map.items():
            if type_ in required_file_type:
                db.session.add(SecurityResetFile(
                    application_id=application.id,
                    file_type=type_,
                    file_key=img,
                    id_type=SecurityResetFile.IDType.ID_CARD,
                ))
        db.session.commit()
        email_cache.delete()
        SecurityOperationCache(g.operate_token).delete()
        UnfreezeAccountFrequencyCache(user_id).add_value()


@ns.route('/unfreeze/liveness')
@respond_with_code
class UnfreezeLivenessResource(Resource):

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """
        自助解冻-发起活体识别
        """
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user = g.user
        user_id = user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed
        if reset_method != SecurityResetMethod.UNFREEZE_ACCOUNT.name:
            raise OperationNotAllowed
        if UserSettings(user_id).login_enabled:
            raise InvalidArgument

        application = UnfreezeAccountResource.get_processing_record(user_id)
        if application:
            raise DuplicateSecurityResetApplication

        kyc_info = LivenessCheckBusiness._get_kyc_info_for_liveness(user)
        if not kyc_info:
            raise InvalidArgument

        profile = LivenessCheckProfile.query.filter(
            LivenessCheckProfile.kyc_type == kyc_info['kyc_type'],
            LivenessCheckProfile.kyc_id == kyc_info['kyc_id']
        ).first()
        if not profile:
            raise InvalidArgument
        kyc_id = profile.kyc_id
        action_id = LivenessCheckBusiness.get_action_id()
        applicant_action_id = SumsubClient.create_liveness_action(profile.transaction_id, action_id)
        level_name = config["SUMSUB_CONFIG"]["liveness_action_level_name"]
        access_token = SumsubClient.get_access_token(profile.request_id, level_name, action_id)
        history = LivenessCheckHistory(
            user_id=user_id,
            kyc_id=kyc_id,
            kyc_type=kyc_info['kyc_type'],
            action_id=action_id,
            transaction_id=applicant_action_id,
            business=LivenessCheckHistory.Business.UNFREEZE_ACCOUNT,
        )
        db.session.add(history)
        db.session.commit()

        SecurityOperationCache(g.operate_token).hmset(dict(
            liveness_id=history.id,
            liveness_action_id=action_id,
            liveness_access_token=access_token,
        ))
        return dict(
            access_token=access_token
        )

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True)
    ))
    def patch(cls, **kwargs):
        """
        自助解冻-用户完成生物识别认证
        """
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user = g.user
        user_id = user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed
        if reset_method != SecurityResetMethod.UNFREEZE_ACCOUNT.name:
            raise OperationNotAllowed
        if UserSettings(user_id).login_enabled:
            raise InvalidArgument

        application = UnfreezeAccountResource.get_processing_record(user_id)
        if application:
            raise DuplicateSecurityResetApplication

        balance_usd = Decimal(user_info['balance_usd'])
        history_id = user_info['liveness_id']
        history: LivenessCheckHistory = LivenessCheckHistory.query.get(history_id)
        if not history:
            raise InvalidArgument
        with CacheLock(LockKeys.kyc_liveness_operation(history.user_id), wait=True):
            db.session.rollback()
            history.status = LivenessCheckHistory.Status.AUDIT_REQUIRED
            try:
                SumsubClient.request_action_check(history.transaction_id)
            except BaseHTTPClient.BadResponse as e:
                current_app.logger.warning(
                    f"non-doc request check failed, code:{e.code}, data:{e.data}"
                )

            row = SecurityResetApplication(
                user_id=user_id,
                status=SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE,
                balance_usd=balance_usd,
                ip=get_request_ip(),
                reset_type=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT,
                platform=get_request_platform().value,
                identity_type=UnfreezeSubmitResource.get_current_identity_type(user),
                data_type=SecurityResetFile.LIVENESS_CHECK,
            )
            db.session.add(row)
            db.session.flush()

            history.business_id = row.id
            db.session.commit()

        email_cache.delete()
        SecurityOperationCache(g.operate_token).delete()
        UnfreezeAccountFrequencyCache(user_id).add_value()


# 验证旧邮箱
@ns.route('/security/validate/email')
@respond_with_code
class SecurityEmailValidateResource(Resource):

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email=fields.String(required=True),
        validate_code=fields.String(required=True),
        flow_case=EnumField(SecurityResetApplication.FlowCase, missing=SecurityResetApplication.FlowCase.NORMAL),
        reset_type=EnumField(SecurityResetApplication.ResetType, missing=SecurityResetApplication.ResetType.TOTP),
    ))
    def post(cls, **kwargs):
        email = kwargs['email']
        user = User.query.filter(User.email == email).first()
        if not user:
            raise InvalidArgument
        if not user.id == g.user.id:
            raise InvalidSecurityResetToken
        if user.is_sub_account:
            raise InvalidArgument
        code = kwargs['validate_code']
        EmailCodeValidator(email, EmailCodeType.RESET_2FA).validate(code)

        token = new_hex_token(OPERATION_TOKEN_SIZE)
        EmailCodeTokenCache(token).set_user_and_email(user.id, email)
        balance_usd = SecurityQuestionBusiness.get_user_current_balance_usd(user.id)
        if kwargs['flow_case'] is not SecurityResetApplication.FlowCase.NORMAL:
            reset_method = SecurityResetMethod.CUSTOMER
        else:
            has_opportunity = SecurityQuestionBusiness.has_answer_opportunity(user.id)
            if not has_opportunity:
                reset_method = SecurityResetMethod.CUSTOMER
            else:
                reset_method = SecurityResetMethod.ANSWER \
                    if balance_usd < Decimal('10000') else SecurityResetMethod.CUSTOMER

        SecurityOperationCache(g.operate_token).hmset(dict(
            reset_method=reset_method.name,
            balance_usd=amount_to_str(balance_usd),
            flow_case=kwargs['flow_case'].name
        ))
        file_type = SecurityFileBusiness.get_user_identity_type(
            user, balance_usd=balance_usd, reset_type=kwargs['reset_type'], flow_case=kwargs['flow_case'])
        return dict(
            email_code_token=token,
            reset_method=reset_method.name,
            file_type=file_type.value,
        )


@ns.route('/security/reset/identity')
@respond_with_code
class SecurityFileResource(Resource):

    @classmethod
    def get_processing_record(cls, user_id: int, reset_type: SecurityResetApplication.ResetType):
        return SecurityResetApplication.query.filter(
            SecurityResetApplication.user_id == user_id,
            SecurityResetApplication.reset_type == reset_type,
            SecurityResetApplication.status.in_((SecurityResetApplication.StatusType.CREATED,
                                                 SecurityResetApplication.StatusType.AUDITED,
                                                 SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE))
        ).first()

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        reset_type=EnumField(SecurityResetApplication.ResetType, required=True),
        flow_case=EnumField(SecurityResetApplication.FlowCase, missing=SecurityResetApplication.FlowCase.NORMAL),
        face_with_id_card=fields.String,
        history_notice_snapshot=fields.String,
        third_party_deposit_snapshot=fields.String,
        third_party_deposit_video=fields.String,
    ))
    def post(cls, **kwargs):
        reset_type = kwargs['reset_type']
        flow_case = kwargs['flow_case']
        if reset_type not in SecurityResetApplication.ADMIN_SECURITY_RESET_LIST:
            raise InvalidArgument
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user_id = g.user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed
        if kwargs['flow_case'] is not SecurityResetApplication.FlowCase.NORMAL:
            cache_flow = user_info.get('flow_case')
            if not cache_flow or cache_flow != kwargs['flow_case'].name:
                raise InvalidArgument
        else:
            if reset_method == SecurityResetMethod.ANSWER.name:
                # 仍有答题机会(失败次数小于允许人工审核的次数)
                if SecurityQuestionBusiness.get_failure_attempt_count(
                        user_id) < SecurityQuestionBusiness.MIN_ALLOWED_ADMIN_AUDIT_COUNT:
                    raise InvalidArgument(message="you still have opportunity to answer questions")

        user = User.query.get(user_id)
        balance_usd = Decimal(user_info['balance_usd'])
        identity = SecurityFileBusiness.get_user_identity_type(
            user, balance_usd=balance_usd, reset_type=reset_type, flow_case=flow_case, support_liveness=True)
        required_file_type = SecurityFileBusiness.get_required_file_type(identity)
        if identity is SecurityFileBusiness.UserIdentityType.EX_EMAIL_NO_KYC_ACCOUNT_FORBID:
            # 该账号为0资产账号，无法重置邮箱，并跳转至注册页
            raise InvalidArgument
        # check existence
        file_map = dict()
        file_map[SecurityResetFile.FileType.FACE_WITH_ID_CARD] = \
            kwargs.get('face_with_id_card')
        file_map[SecurityResetFile.FileType.HISTORY_NOTICE_SNAPSHOT] = \
            kwargs.get('history_notice_snapshot')
        file_map[SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_SNAPSHOT] = \
            kwargs.get('third_party_deposit_snapshot')
        file_map[SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_VIDEO] = \
            kwargs.get('third_party_deposit_video')
        for type_, img in file_map.items():
            if type_ in required_file_type:
                if not img:
                    raise InvalidArgument(message=gettext("有资料未提交，请补充"))
            else:
                if img:
                    raise InvalidArgument

        if cls.get_processing_record(user_id, reset_type):
            raise DuplicateSecurityResetApplication

        if reset_type == SecurityResetApplication.ResetType.EMAIL:
            require_email_not_exists(new_email)
        application = SecurityResetApplication(
            user_id=user_id,
            reset_type=reset_type,
            flow=flow_case,
            data_type=SecurityResetFile.get_data_type_desc(file_types=required_file_type),
            new_email=new_email,
            balance_usd=balance_usd,
            ip=get_request_ip(),
            platform=get_request_platform().value,
        )
        db.session_add_and_commit(application)
        for type_, img in file_map.items():
            if type_ in required_file_type:
                db.session.add(SecurityResetFile(
                    application_id=application.id,
                    file_type=type_,
                    file_key=img,
                    id_type=SecurityResetFile.IDType.ID_CARD  # 默认值
                ))
        db.session.commit()
        email_cache.delete()
        SecurityOperationCache(g.operate_token).delete()
        SecurityResetFrequencyCache(user_id).add_value()


@ns.route('/security/reset/liveness')
@respond_with_code
class SecurityLivenessResource(Resource):

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        reset_type=EnumField(SecurityResetApplication.ResetType, required=True),
        flow_case=EnumField(SecurityResetApplication.FlowCase, missing=SecurityResetApplication.FlowCase.NORMAL),
    ))
    def post(cls, **kwargs):
        # 重置安全工具-用户发起人脸识别
        flow_case = kwargs['flow_case']
        reset_type = kwargs['reset_type']
        if reset_type not in SecurityResetApplication.ADMIN_SECURITY_RESET_LIST:
            raise InvalidArgument
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user_id = g.user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed

        user = User.query.get(user_id)
        balance_usd = Decimal(user_info['balance_usd'])
        identity = SecurityFileBusiness.get_user_identity_type(
            user, balance_usd=balance_usd, reset_type=reset_type, flow_case=flow_case, support_liveness=True)
        if identity is not SecurityFileBusiness.UserIdentityType.LIVENESS_CHECK:
            raise InvalidArgument

        if SecurityFileResource.get_processing_record(user_id, reset_type):
            raise DuplicateSecurityResetApplication

        if reset_type == SecurityResetApplication.ResetType.EMAIL:
            require_email_not_exists(new_email)

        kyc_info = LivenessCheckBusiness._get_kyc_info_for_liveness(user)
        if not kyc_info:
            raise InvalidArgument

        profile = LivenessCheckProfile.query.filter(
            LivenessCheckProfile.kyc_type == kyc_info['kyc_type'],
            LivenessCheckProfile.kyc_id == kyc_info['kyc_id']
        ).first()
        if not profile:
            raise InvalidArgument
        kyc_id = profile.kyc_id
        action_id = LivenessCheckBusiness.get_action_id()
        applicant_action_id = SumsubClient.create_liveness_action(profile.transaction_id, action_id)
        level_name = config["SUMSUB_CONFIG"]["liveness_action_level_name"]
        access_token = SumsubClient.get_access_token(profile.request_id, level_name, action_id)
        history = LivenessCheckHistory(
            user_id=user_id,
            kyc_id=kyc_id,
            kyc_type=kyc_info['kyc_type'],
            action_id=action_id,
            transaction_id=applicant_action_id,
            business=LivenessCheckHistory.Business.RESET_SECURITY,
        )
        db.session.add(history)
        db.session.commit()

        SecurityOperationCache(g.operate_token).hmset(dict(
            liveness_id=history.id,
            liveness_action_id=action_id,
            liveness_access_token=access_token,
        ))
        return dict(
            access_token=access_token
        )

    @classmethod
    @require_security_reset_token
    @ns.use_kwargs(dict(
        email_code_token=fields.String(required=True),
        reset_type=EnumField(SecurityResetApplication.ResetType, required=True),
        flow_case=EnumField(SecurityResetApplication.FlowCase, missing=SecurityResetApplication.FlowCase.NORMAL),
    ))
    def patch(cls, **kwargs):
        """
        重置安全工具-用户完成生物识别认证
        """
        flow_case = kwargs['flow_case']
        reset_type = kwargs['reset_type']
        if reset_type not in SecurityResetApplication.ADMIN_SECURITY_RESET_LIST:
            raise InvalidArgument
        email_cache = EmailCodeTokenCache(kwargs['email_code_token'])
        id_, new_email = email_cache.get_user_and_email()
        user_id = g.user.id
        user_info = g.security_reset_info
        reset_method = user_info['reset_method']
        if not user_id or not id_ or id_ != int(user_id):
            raise InvalidSecurityResetToken
        if g.user.is_sub_account:
            raise InvalidArgument
        if not reset_method:
            raise OperationNotAllowed

        user = User.query.get(user_id)
        balance_usd = Decimal(user_info['balance_usd'])
        identity = SecurityFileBusiness.get_user_identity_type(
            user, balance_usd=balance_usd, reset_type=reset_type, flow_case=flow_case, support_liveness=True)
        if identity is not SecurityFileBusiness.UserIdentityType.LIVENESS_CHECK:
            raise InvalidArgument

        if SecurityFileResource.get_processing_record(user_id, reset_type):
            raise DuplicateSecurityResetApplication

        if reset_type == SecurityResetApplication.ResetType.EMAIL:
            require_email_not_exists(new_email)

        history_id = user_info['liveness_id']
        history: LivenessCheckHistory = LivenessCheckHistory.query.get(history_id)
        if not history:
            raise InvalidArgument
        with CacheLock(LockKeys.kyc_liveness_operation(history.user_id), wait=True):
            db.session.rollback()
            history.status = LivenessCheckHistory.Status.AUDIT_REQUIRED
            try:
                SumsubClient.request_action_check(history.transaction_id)
            except BaseHTTPClient.BadResponse as e:
                current_app.logger.warning(
                    f"non-doc request check failed, code:{e.code}, data:{e.data}"
                )

            row = SecurityResetApplication(
                user_id=user_id,
                status=SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE,
                reset_type=reset_type,
                flow=flow_case,
                data_type=SecurityResetFile.LIVENESS_CHECK,
                new_email=new_email,
                balance_usd=balance_usd,
                ip=get_request_ip(),
                platform=get_request_platform().value,
            )
            db.session.add(row)
            db.session.flush()

            history.business_id = row.id
            db.session.commit()

        email_cache.delete()
        SecurityOperationCache(g.operate_token).delete()
        SecurityResetFrequencyCache(user_id).add_value()


@ns.route('/withdraw-password/reset/token')
@respond_with_code
class WithdrawPasswordResetResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.RESET_WITHDRAW_PASSWORD, allow_sub_account=False)
    @require_email_code(allow_sub_account=False)
    def get(cls, **kwargs):
        user = g.user
        user_id = user.id
        # app 版本需要 kyc，等迭代后更改为根据版本控制
        if get_request_platform().is_mobile():
            if user.kyc_status != User.KYCStatus.PASSED:
                raise NoKycQualifications
        model = SecurityResetApplication
        reset_type = model.ResetType.WITHDRAW_PASSWORD
        # 是否有待审核的申请
        application = model.query.filter(
            model.status == model.StatusType.CREATED,
            model.reset_type == reset_type,
            model.user_id == user_id
        ).first()
        if application:
            raise DuplicateSecurityResetApplication

        token = new_hex_token(LOGIN_TOKEN_SIZE)
        balance_usd = SecurityQuestionBusiness.get_user_current_balance_usd(user_id)
        cache = SecurityOperationCache(token)
        cache.hmset(dict(
            user_id=str(user_id),
            reset_method=SecurityResetMethod.CUSTOMER.name,
            balance_usd=amount_to_str(balance_usd),
            flow_case=SecurityResetApplication.FlowCase.NORMAL.name
        ))
        cache.expire()
        EmailCodeTokenCache(token).set_user_and_email(user.id, user.email)
        return dict(
            token=token,
            operate_token=token,
            email_code_token=token,
            file_type=SecurityFileBusiness.get_user_identity_type(g.user, reset_type=reset_type, support_liveness=True)
        )


@ns.route('/security/reset/withdraw-password')
@respond_with_code
class SecurityResetWithdrawPasswordResource(Resource):
    """
        ！！！已废弃，提现密码并入安全工具，由 /security/reset/identity 接口统一处理
        等后续 app 强制升级后可删除代码
    """

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
        face_with_id_card=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        user = g.user
        user_id = user.id

        token = kwargs["token"]
        cache = SecurityOperationCache(token)
        user_info = cache.hgetall()
        if user_info.get("user_id") != str(user_id):
            raise InvalidSecurityResetToken

        model = SecurityResetApplication
        reset_type = model.ResetType.WITHDRAW_PASSWORD

        application = model.query.filter(
            model.user_id == user_id,
            model.reset_type == reset_type,
            model.status == model.StatusType.CREATED
        ).first()
        if application:
            raise DuplicateSecurityResetApplication

        if not kwargs.get('face_with_id_card'):
            raise InvalidArgument(message=gettext("有资料未提交，请补充"))

        identity = SecurityFileBusiness.get_user_identity_type(user, reset_type=reset_type)
        required_file_type = SecurityFileBusiness.get_required_file_type(identity)
        application = model(
            user_id=user_id,
            reset_type=reset_type,
            data_type=SecurityResetFile.get_data_type_desc(file_types=list(required_file_type)),
            new_email=user.email,
            balance_usd=user_info['balance_usd'],
            ip=get_request_ip(),
            platform=get_request_platform().value,
        )
        db.session_add_and_commit(application)

        file_model = SecurityResetFile
        db.session.add(SecurityResetFile(
            application_id=application.id,
            file_type=file_model.FileType.FACE_WITH_ID_CARD,
            file_key=kwargs["face_with_id_card"],
            id_type=SecurityResetFile.IDType.ID_CARD  # 默认值
        ))
        db.session.commit()

        SecurityOperationCache(token).delete()


@ns.route('/security/reset/upload')
@respond_with_code
class SecurityUploadResource(Resource):

    @classmethod
    @limit_ip_frequency(10, 600)
    def post(cls, **kwargs):
        """
        for image/mp4 only
        """
        if "withdraw_password_token" in request.form:
            token = request.form['withdraw_password_token']
        else:
            token = request.form.get('operate_token')
        user_info = SecurityOperationCache(token).read()
        if not user_info:
            raise InvalidSecurityResetToken
        file = request.files.get('my_file')
        if not file:
            raise InvalidArgument
        return handle_request_file_upload(file)


@ns.route("/guide")
@respond_with_code
class UserGuideResource(Resource):
    PAGES = ["deposit", "exchange", "quote"]

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            page=EnumField(PAGES, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 查询用户新手指引的状态 """
        if g.user.created_at.date() <= date(2021, 9, 22) and kwargs["page"] not in ["quote"]:
            # 上线前注册的用户 都默认已完成新手指引
            return {"result": True}

        user_id = g.user.id
        guide = UserGuideHistory.query.filter(
            UserGuideHistory.user_id == user_id,
            UserGuideHistory.type == UserGuideHistory.Type.FLOATING_LAYER.name,
            UserGuideHistory.status == UserGuideHistory.Status.VALID,
            UserGuideHistory.location == kwargs["page"],
        ).first()
        return {"result": bool(guide)}

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            page=EnumField(PAGES, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 更新用户新手指引的状态 """
        user_id = g.user.id
        page = kwargs["page"]
        exist_guide = UserGuideHistory.query.filter(
            UserGuideHistory.user_id == user_id,
            UserGuideHistory.type == UserGuideHistory.Type.FLOATING_LAYER.name,
            UserGuideHistory.location == page,
        ).first()
        if exist_guide:
            exist_guide.status = UserGuideHistory.Status.VALID
        else:
            new_guide = UserGuideHistory(
                user_id=user_id,
                location=page,
                type=UserGuideHistory.Type.FLOATING_LAYER.name,
            )
            db.session.add(new_guide)
        db.session.commit()
        return {}


@ns.route('/profile')
@respond_with_code
class UserProfileResource(Resource):

    @classmethod
    @cached(300)
    def had_spot_trade(cls, user_id: int) -> bool:
        c = ServerClient()
        result = c.market_user_deals(user_id=user_id, market='', account_id=ALL_RECORD_ACCOUNT_ID,
                                     start_time=0, end_time=0, page=1, limit=1)
        return len(result) > 0

    @classmethod
    @cached(300)
    def had_future_trade(cls, user_id: int) -> bool:
        c = PerpetualServerClient()
        result = c.user_deals(user_id=user_id, market='', side=0, limit=1)
        return len(result) > 0

    @classmethod
    @cached(300)
    def had_deposited(cls, user_id: int) -> bool:
        deposit = Deposit.query.filter(
            Deposit.user_id == user_id).first()
        return True if deposit else False

    @classmethod
    @cached(300)
    def had_exchange_trade(cls, user_id: int) -> bool:
        exchange = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id == user_id
        ).first()
        return True if exchange else False

    @classmethod
    @cached(300)
    def had_margin_loan_order(cls, user_id: int) -> bool:
        margin_loan_order = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == user_id).first()
        return True if margin_loan_order else False

    @classmethod
    def get_withdrawal_disabled_after_reset_until(cls, user_id: int, field: ConfigField) -> Optional[int]:
        setting = UserSetting.query.filter(
            UserSetting.user_id == user_id,
            UserSetting.key == field.name,
            UserSetting.value == field.db_value(True),
        ).first()
        if setting and setting.valid_till:
            return datetime_to_time(setting.valid_till)

    @classmethod
    @cached(300)
    def get_withdrawal_disabled_after_security_reset_until(cls, user_id: int) -> Optional[int]:
        return cls.get_withdrawal_disabled_after_reset_until(
            user_id, UserSettings.withdrawals_disabled_after_security_editing)

    @classmethod
    @cached(300)
    def get_last_updated_security_reset_type(cls, user_id: int) -> Optional[str]:
        user: User = User.query.get(user_id)
        security_op_type_map = {
            "MOBILE": (
                SecurityToolHistory.OpType.CHANGE_MOBILE.value,
                SecurityToolHistory.OpType.UNBIND_MOBILE.value,
            ),
            "TOTP": (
                SecurityToolHistory.OpType.CHANGE_TOTP.value,
                SecurityToolHistory.OpType.UNBIND_TOTP.value,
            ),
            "WITHDRAW_PASSWORD": (
                SecurityToolHistory.OpType.RESET_WITHDRAW_PASSWORD.value,
                SecurityToolHistory.OpType.EDIT_WITHDRAW_PASSWORD.value,
            ),
            "WEB_AUTHN": (
                SecurityToolHistory.OpType.UNBIND_WEBAUTHN.value,
            ),
        }
        security_ops = []
        for v in security_op_type_map.values():
            security_ops.extend(v)
        record = SecurityToolHistory.query.filter(
            SecurityToolHistory.account == user.email,
            SecurityToolHistory.op_type.in_(security_ops)
        ).with_entities(SecurityToolHistory.created_at,
                        SecurityToolHistory.op_type).order_by(SecurityToolHistory.id.desc()).first()
        
        security_updated_type = security_updated_at = None
        reset_type_updated_list = [
            ('EMAIL', user.email_updated_at),
            ('LOGIN_PASSWORD', user.login_password_updated_at)
        ]
        if record:
            for k, v in security_op_type_map.items():
                if record.op_type in v:
                    security_updated_at = record.created_at
                    security_updated_type = k
                    break
            if security_updated_at:
                reset_type_updated_list.append((security_updated_type, security_updated_at))
        
        reset_type_updated_list = [
            (k, v) for k, v in reset_type_updated_list if v
        ]
        if not reset_type_updated_list:
            return None
        reset_type_updated_list.sort(key=lambda x: x[1], reverse=True)
        return reset_type_updated_list[0][0]
    
    @classmethod
    def get_view_comment_user_tos(cls):
        pref = UserPreferences(g.user.id)
        if is_old_app_request(3480, 91):
            return pref.view_comment_user_tos or pref.view_comment_user_agreement or pref.user_info_confirmed_for_comment
        return pref.view_comment_user_tos

    @classmethod
    @cached(300)
    def get_withdrawal_disabled_after_withdraw_password_reset_until(cls, user_id: int) -> Optional[int]:
        return cls.get_withdrawal_disabled_after_reset_until(
            user_id, UserSettings.withdrawals_disabled_after_withdraw_password_editing)

    @classmethod
    def get_settle_switch_status(cls, user_id):
        client = PerpetualServerClient()
        return client.get_settle_switch(user_id)['settle_switch']

    @classmethod
    def get_settle_switch_and_had_trade(cls, user_id: int):
        cache = UserPreferenceFrontendCache(user_id)
        try:
            settle_switch = cls.get_settle_switch_status(user_id)
            had_future_trade = cls.had_future_trade(user_id)
        except Exception as e:
            if not (cache_data := cache.get_data()):
                raise e
            settle_switch = cache_data.get('settle_switch')
            had_future_trade = cache_data.get('had_future_trade')
            return settle_switch, had_future_trade
        cache.set_data({
            "settle_switch": settle_switch,
            "had_future_trade": had_future_trade,
        })
        return settle_switch, had_future_trade


    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls):
        """用户属性信息"""
        user: User = g.user
        pref = UserPreferences(user.id)
        settle_switch, had_future_trade = cls.get_settle_switch_and_had_trade(user.id)
        preferences = dict(
            currency=pref.currency,
            allows_announcement_emails=pref.allows_announcement_emails,
            allows_activity_emails=pref.allows_activity_emails,
            allows_blog_emails=pref.allows_blog_emails,
            allows_deposit_withdrawal_emails=pref.allows_deposit_withdrawal_emails,
            allows_announcement_navigation=pref.allows_announcement_navigation,
            allows_announcement_app=pref.allows_announcement_app,
            allows_activity_app=pref.allows_activity_app,
            allows_blog_app=pref.allows_blog_app,
            two_fa_type=pref.two_fa_type,
            order_confirmation=pref.order_confirmation,
            perpetual_order_confirmation=pref.perpetual_order_confirmation,
            cet_discount=pref.cet_discount_enabled,
            margin=pref.opening_margin_function,
            perpetual=pref.opening_perpetual_trading,
            perpetual_pricing_basis=pref.perpetual_pricing_basis.name,
            opening_account_profit_loss=pref.opening_account_profit_loss,
            anti_phishing_code=pref.anti_phishing_code,
            auto_put_margin_loan_order=pref.auto_put_margin_loan_order,
            auto_flat_margin_loan_order=pref.auto_flat_margin_loan_order,
            app_deposit_withdrawal_notice=pref.app_deposit_withdrawal_notice,
            app_exchange_notice=pref.app_exchange_notice,
            app_ieo_notice=pref.app_ieo_notice,
            app_holding_asset_notice=pref.app_holding_asset_notice,
            app_favorite_asset_notice=pref.app_favorite_asset_notice,
            app_asset_level_break_notice=pref.app_asset_level_break_notice,
            app_new_asset_rise_notice=pref.app_new_asset_rise_notice,
            app_spot_limit_order_notice=pref.app_spot_limit_order_notice,
            app_spot_stop_order_notice=pref.app_spot_stop_order_notice,
            app_perpetual_limit_order_notice=pref.app_perpetual_limit_order_notice,
            app_perpetual_stop_order_notice=pref.app_perpetual_stop_order_notice,
            app_perpetual_profit_loss_notice=pref.app_perpetual_profit_loss_notice,
            app_information_notice=pref.app_information_notice,
            news_popup_display=pref.news_popup_display,
            settle_switch=settle_switch,
            app_spot_grid_take_profit_notice=pref.app_spot_grid_take_profit_notice,
            app_spot_grid_stop_loss_notice=pref.app_spot_grid_stop_loss_notice,
            app_spot_grid_exceed_price_notice=pref.app_spot_grid_exceed_price_notice,
            app_auto_invest_profit_arrive_notice=pref.app_auto_invest_profit_arrive_notice,
            only_view_followed_analysts=pref.only_view_followed_analysts,
            view_coinex_protocol=pref.view_coinex_protocol,
            only_allow_sms_verification=pref.only_allow_sms_verification,
            view_comment_user_tos=cls.get_view_comment_user_tos(),
            view_comment_user_agreement=pref.view_comment_user_agreement,
            user_info_confirmed_for_comment=pref.user_info_confirmed_for_comment,
            app_comment_message_up_notice=pref.app_comment_message_up_notice,
            app_comment_message_at_notice=pref.app_comment_message_at_notice,
            app_comment_message_reply_notice=pref.app_comment_message_reply_notice,
            app_comment_message_tip_notice=pref.app_comment_message_tip_notice,
            opening_onchain_trade_function=pref.opening_onchain_trade_function,
            finish_perpetual_tutorial_question=pref.finish_perpetual_tutorial_question,
            finish_margin_tutorial_question=pref.finish_margin_tutorial_question,
        )

        tags = dict(
            had_future_trade=had_future_trade,  # 表示合约，兼容老字段
            had_perpetual_trade=had_future_trade,
            had_spot_trade=cls.had_spot_trade(user.id),
            had_deposited=cls.had_deposited(user.id),
            had_p2p_buy=has_p2p_buy(user.id),
            had_margin_loan_order=cls.had_margin_loan_order(user.id),
            had_kyc_verified=user.kyc_status == User.KYCStatus.PASSED,
            withdrawal_disabled_after_security_reset_until=cls.get_withdrawal_disabled_after_security_reset_until(
                user.id),
            last_updated_security_reset_type=cls.get_last_updated_security_reset_type(user.id),
            withdrawal_disabled_after_withdraw_password_reset_until=cls.get_withdrawal_disabled_after_withdraw_password_reset_until(
                user.id),
            had_exchange_trade=cls.had_exchange_trade(user.id),
        )
        return dict(
            preferences=preferences,
            tags=tags
        )


@ns.route('/switch-users')
@respond_with_code
class SwitchUserListResource(Resource):

    class SwitchSource(Enum):
        # 按鉴权身份来识别切换的人是主账号，还是委托人
        MAIN_ACCOUNT = '主账号'
        MANAGER_ACCOUNT = '授权/托管账号'

    @classmethod
    @require_login
    def get(cls):
        """ 可切换的账号列表 """
        auth_user: User = g.auth_user  # 当前登入的主账号

        # 自己的子账号
        sub_accounts = SubAccount.query.filter(
            SubAccount.main_user_id == auth_user.id,
            SubAccount.status == SubAccount.Status.VALID,
            SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
            SubAccount.is_visible.is_(True),
        ).all()
        sub_user_ids = [i.user_id for i in sub_accounts]
        # 所有关系
        manage_relations = SubAccountManagerRelation.query.filter(
            or_(
                SubAccountManagerRelation.manager_id == auth_user.id,
                SubAccountManagerRelation.main_user_id == auth_user.id,
            ),
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).all()
        relations = {x.user_id: x for x in manage_relations}
        manage_sub_user_ids = [i.user_id for i in manage_relations]
        all_sub_user_ids = list(set(sub_user_ids + manage_sub_user_ids))
    
        sub_users = []
        if all_sub_user_ids:
            for chunk_user_ids in batch_iter(all_sub_user_ids, 500):
                sub_users.extend(
                    User.query.filter(
                        User.id.in_(chunk_user_ids),
                    ).with_entities(User.id, User.name).all()
                )

        login_disabled_subs = set()
        if all_sub_user_ids:
            login_disabled_subs = UserConfigKeyCache.get_login_disabled_by_admin_ids(all_sub_user_ids)

        login_disabled_subs |= SubAccountManagerRelation.get_disabled_sub_user_set(auth_user.id)

        # 按切换时间降序
        sub_user_switch_ts_map = SubAccountSwitchCache(auth_user.id).get_switch_users()
        sub_users.sort(key=lambda u: sub_user_switch_ts_map.get(u.id, 0), reverse=True)

        result = [
            {
                "name": auth_user.nickname,
                "user_id": auth_user.id,
                "user_type": "main_user",
                "status": "VALID",
                "from": cls.SwitchSource.MAIN_ACCOUNT,
            },
        ]
        for sub_user in sub_users:
            if sub_user.id in sub_user_ids:
                from_ = cls.SwitchSource.MAIN_ACCOUNT.name
            else:
                from_ = cls.SwitchSource.MANAGER_ACCOUNT.name
            status = "INVALID" if sub_user.id in login_disabled_subs else "VALID"
            result.append(
                {
                    "name": sub_user.name,
                    "user_id": sub_user.id,
                    "user_type": cls._get_sub_user_type(relations.get(sub_user.id)),
                    "status": status,
                    "from": from_,
                }
            )

        return result

    @classmethod
    def _get_sub_user_type(cls, relation: SubAccountManagerRelation = None):
        if not relation:
            return 'sub_user'
        if relation.manage_type == SubAccountManagerRelation.ManageType.MANAGED:
            user_type = 'manage_sub_user'
        elif relation.manage_type == SubAccountManagerRelation.ManageType.HOSTING:
            user_type = 'hosting_sub_user'
        else:
            raise InvalidArgument
        return user_type


@ns.route('/report-switch-user')
@respond_with_code
class ReportSwitchUserResource(Resource):
    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """ 切换子账号上报 """
        auth_user: User = g.auth_user  # 当前登入的主账号
        SubAccountSwitchCache(auth_user.id).add_switch_user(kwargs["user_id"])


@ns.route("/user-education-record")
@respond_with_code
class UserEducationRecorde(Resource):
    params = dict(
        key=fields.String(required=True, validate=lambda x: x in UserPreferences.guide_list())
    )

    @classmethod
    @require_login
    @ns.use_kwargs(params)
    def put(cls, **kwargs):
        pref = UserPreferences(g.user.id)
        config_key = kwargs["key"]
        setattr(pref, config_key, True)
        return

    @classmethod
    @require_login
    @ns.use_kwargs(params)
    def get(cls, **kwargs):
        pref = UserPreferences(g.user.id)
        config_key = kwargs["key"]
        return getattr(pref, config_key)


@ns.route("/third-party/account")
@respond_with_code
class ThirdPartyAccountResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """
        第三方账号信息
        """
        accounts = ThirdPartyAccount.query.filter(
            ThirdPartyAccount.user_id == g.user.id,
            ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
        ).all()
        return dict(accounts=[dict(source=item.source.name,
                                   name=hide_text_default(item.name)) for item in accounts])

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
        source=EnumField(ThirdPartyAccount.Source, required=True),
    ))
    def post(cls, **kwargs):
        """
        第三方账号绑定
        """
        if not is_old_app_request(3380, 65):
            if g.user.has_2fa:
                require_2fa(MobileCodeType.BIND_THIRD_PARTY_ACCOUNT)(lambda: None)()
            else:
                require_email_code(allow_sub_account=False)(lambda: None)()
        client = get_oauth_client(kwargs["source"].name)
        info = client.get_user_info(kwargs["code"])
        with CacheLock(LockKeys.third_party_account_operation(info['user_id']), wait=False):
            db.session.rollback()
            account = ThirdPartyAccount.query.filter(
                ThirdPartyAccount.third_party_id == info['user_id'],
                ThirdPartyAccount.source == kwargs["source"],
                ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
            ).first()
            if account:
                if kwargs["source"] == ThirdPartyAccount.Source.GOOGLE:
                    msg = gettext("%(source)s邮箱已注册，暂无法绑定", source=kwargs["source"].value)
                else:
                    # common message
                    msg = gettext("%(source)s账号已绑定其他CoinEx账号，无法重复绑定", source=kwargs["source"].value)
                raise ThirdPartyAccountExists(message=msg,
                                              data=hide_text_default(info['email']),
                                              source=kwargs["source"].value)
            self_account = ThirdPartyAccount.query.filter(
                ThirdPartyAccount.user_id == g.user.id,
                ThirdPartyAccount.source == kwargs["source"],
                ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
            ).first()
            if self_account:
                raise AccountHasBeenBinded(source=kwargs["source"].value)

            account = ThirdPartyAccount.get_or_create(
                user_id=g.user.id,
                source=kwargs["source"],
                third_party_id=info['user_id'],
            )
            account.name = info['email']
            account.status = ThirdPartyAccount.Status.VALID
            db.session_add_and_commit(account)
            OperationLog.add(g.user.id, OperationLog.Operation.BIND_THIRD_PARTY_ACCOUNT, info['email'],
                             get_request_platform())

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        source=EnumField(ThirdPartyAccount.Source, required=True),
    ))
    def delete(cls, **kwargs):
        """
        第三方账号解绑
        """
        if not g.user.login_password_hash:
            raise UnbindThirdPartyAccountNotAllowed
        account = ThirdPartyAccount.query.filter(
            ThirdPartyAccount.source == kwargs["source"],
            ThirdPartyAccount.user_id == g.user.id,
            ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
        ).first()
        if not account:
            raise InvalidArgument
        account.status = ThirdPartyAccount.Status.DELETED
        db.session.commit()
        OperationLog.add(g.user.id, OperationLog.Operation.UNBIND_THIRD_PARTY_ACCOUNT,
                         account.name,
                         get_request_platform())


@ns.route("/third-party/sign/up")
@respond_with_code
class ThirdPartyAccountSignUpResource(Resource):

    @classmethod
    @require_geetest
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
        source=EnumField(ThirdPartyAccount.Source, required=True),
        refer_code=fields.String(),
        channel=fields.String,
        device_id=DeviceIdField(required=True),
        activity_party=EnumField(ActivityPartyUser.ActivityParty, enum_by_value=True),
    ))
    def post(cls, **kwargs):
        """
        第三方账号注册
        """
        data = dict(kwargs)
        auth_token_cache = ThirdPartyAccountAuthTokenCache(kwargs["source"].name, kwargs['code'])
        user_info = auth_token_cache.read()
        if user_info:
            user_info = json.loads(user_info)
            auth_token_cache.delete()
        else:
            client = get_oauth_client(kwargs["source"].name)
            user_info = client.get_user_info(kwargs["code"])
        data['email'] = user_info['email']
        if is_disposable_email(data['email']):
            raise EmailNotAllowed
        with CacheLock(LockKeys.third_party_account_operation(user_info['user_id']), wait=False):
            db.session.rollback()
            account = ThirdPartyAccount.query.filter(
                ThirdPartyAccount.third_party_id == user_info['user_id'],
                ThirdPartyAccount.source == kwargs["source"],
                ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
            ).first()
            if account:
                raise ThirdPartyAccountExists(data=hide_text_default(user_info['email']),
                                              source=kwargs["source"].value)
            try:
                info = SignUpResource.before_sign_up(data, validate_email_code=False)
            except EmailAlreadyExists:
                raise EmailAlreadyExists(message=gettext("邮箱已存在，无法重复注册"))
            user = User(
                email=user_info['email'],
                email_updated_at=now(),
                registration_ip=info['ip'],
                registration_location=info['location'],
                name='',
                channel=kwargs.get('channel'),
            )
            db.session.add(user)
            db.session.flush()
            account = ThirdPartyAccount.get_or_create(
                user_id=user.id,
                source=kwargs["source"],
                third_party_id=user_info['user_id'],
                created_at=user.created_at,
            )
            account.name = user_info['email']
            account.status = ThirdPartyAccount.Status.VALID
            db.session.add(account)
            if activity_party := kwargs.get('activity_party'):
                SignUpResource.create_party_user(user.id, activity_party)
            db.session.commit()
            info = SignUpResource.after_sign_up(user, info, is_third_party=True)
        return _user_to_dict(user, user, token=info['token'])


@ns.route("/third-party/sign/in")
@respond_with_code
class ThirdPartyAccountSignInResource(Resource):

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
        source=EnumField(ThirdPartyAccount.Source, required=True),
        device_id=DeviceIdField(required=True)
    ))
    def post(cls, **kwargs):
        """
        第三方账号登录
        """
        client = get_oauth_client(kwargs["source"].name)
        info = client.get_user_info(kwargs["code"])
        email, third_party_id = info["email"], info["user_id"]
        account = ThirdPartyAccount.query.filter(
            ThirdPartyAccount.third_party_id == third_party_id,
            ThirdPartyAccount.source == kwargs["source"],
            ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
        ).first()
        if not account:
            # try:
            #     require_email_exists(email)
            #     raise ThirdPartyAccountDoesNotExist(data=hide_text_default(email),
            #                                         source=kwargs["source"].value)
            # except EmailDoesNotExist:
            cache_cls = ThirdPartyAccountAuthTokenCache
            cache_cls(kwargs["source"].name, kwargs['code']).set(
                json.dumps(dict(email=email, user_id=third_party_id)), ex=cache_cls.ttl
            )
            raise AccountDoesNotExist(dict(data=hide_text_default(email),
                                      email=email,
                                      is_private_email=info.get('is_private_email', False))
                                      )

        user = User.query.get(account.user_id)
        SignInResource.validate_login_permission(user)
        info = SignInResource.after_sign_in(user, dict(account=email,
                                                       device_id=kwargs['device_id'], **info))
        update_security_statistics([user.id], SecuritySettingType.THIRD_PARTY_ACCOUNT)
        return _user_to_dict(user, user, token=info['token'])


@ns.route("/third-party/bind")
@respond_with_code
class ThirdPartyAccountBindResource(Resource):

    @classmethod
    @limit_ip_frequency(*LOGIN_IP_LIMIT)
    @ns.use_kwargs(dict(
        account=EmailField(required=True),
        login_password=fields.String(required=True),
        code=fields.String(required=True),
        source=EnumField(ThirdPartyAccount.Source, required=True),
        device_id=DeviceIdField(required=True),
        email_code_token=fields.String,
    ))
    def post(cls, **kwargs):
        """
        非登录态绑定第三方账号
        """
        SignInResource.validate_ip()

        account: str = kwargs['account'].strip()
        password: str = kwargs['login_password']

        user = SignInResource.do_sign_in(account, password)
        if user.is_sub_account:
            raise AuthenticationFailed
        user_id = user.id
        SignInResource.validate_login_permission(user)
        user: User
        g.auth_user = g.user = user
        if user.has_2fa:
            try:
                require_2fa(MobileCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT)(lambda: None)()
            except TwoFactorAuthenticationFailed:
                token = new_hex_token(OPERATION_TOKEN_SIZE)
                cache = ThirdPartyAccountOperationTokenCache(token)
                cache.set(user_id, ex=cache.ttl)
                raise TwoFactorAuthenticationFailed(dict(
                    email=(hide_email(email)
                       if (email := user.email) else None),
                    origin_email=user.email,
                    country_code=(str(country_code)
                                if (country_code := user.mobile_country_code)
                                else None),
                    mobile=(hide_text_default(mobile_num)
                            if (mobile_num := user.mobile_num)
                            else None),
                    origin_mobile=user.mobile_num,
                    has_totp_auth=bool(user.totp_auth_key),
                    has_webauthn=bool(user.web_authn_list),
                    protect_type=UserPreferences(user_id).two_fa_type.value or (TwoFAType.TOTP.value \
                                                                                if bool(user.totp_auth_key) else TwoFAType.MOBILE.value \
                                                                                    if bool(user.mobile_num) else TwoFAType.WEBAUTHN.value \
                                                                                        if bool(user.web_authn_list) else ""),
                    require_2fa=bool(user.totp_auth_key),
                    require_email_code=not bool(user.totp_auth_key),
                    operate_token=token,
                ))
        else:
            email_code_token = kwargs.get('email_code_token')
            if not email_code_token:
                raise EmailCodeVerificationFailed
            cache = EmailCodeTokenCache(kwargs['email_code_token'])
            _email = cache.get_email()
            if _email != user.email:
                raise EmailCodeVerificationFailed
            cache.delete()
        auth_token_cache = ThirdPartyAccountAuthTokenCache(kwargs["source"].name, kwargs['code'])
        info = auth_token_cache.read()
        if info:
            info = json.loads(info)
            auth_token_cache.delete()
        else:
            client = get_oauth_client(kwargs["source"].name)
            info = client.get_user_info(kwargs["code"])
        email, third_party_id = info["email"], info["user_id"]
        with CacheLock(LockKeys.third_party_account_operation(third_party_id), wait=False):
            db.session.rollback()
            account = ThirdPartyAccount.query.filter(
                ThirdPartyAccount.third_party_id == third_party_id,
                ThirdPartyAccount.source == kwargs["source"],
                ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
            ).first()
            if account:
                raise ThirdPartyAccountExists(data=hide_text_default(email),
                                              source=kwargs["source"].value)

            self_account = ThirdPartyAccount.query.filter(
                ThirdPartyAccount.user_id == user.id,
                ThirdPartyAccount.source == kwargs["source"],
                ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
            ).first()
            if self_account:
                raise AccountHasBeenBinded(source=kwargs["source"].value)

            account = ThirdPartyAccount.get_or_create(
                user_id=user.id,
                source=kwargs["source"],
                third_party_id=third_party_id,
            )
            account.name = email
            account.status = ThirdPartyAccount.Status.VALID
            db.session_add_and_commit(account)
            info = SignInResource.after_sign_in(user,
                                                dict(account=email, device_id=kwargs['device_id']))
        OperationLog.add(user.id, OperationLog.Operation.BIND_THIRD_PARTY_ACCOUNT,
                         account.name,
                         get_request_platform())
        return _user_to_dict(user, user, token=info['token'])


@ns.route("/kyc-pro")
@respond_with_code
class P2pUserKycProResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        address=fields.String(required=True),
        name=fields.String(required=True),
        address_file_ids=fields.List(fields.Integer(required=True), required=True),
        file_type=EnumField(KycVerificationPro.FileType),
    ))
    def post(cls, **kwargs):
        if (not g.user.kyc_status == User.KYCStatus.PASSED
                or g.user.kyc_pro_status == User.KycProStatus.PROCESSING):
            raise InvalidArgument
        kwargs['country'] = g.user.kyc_country
        kyc_config = KycCountryConfig.query.filter(
            KycCountryConfig.country == kwargs['country'],
        ).first()
        support_kyc_pro = kyc_config.pro_supported if kyc_config else True
        if not support_kyc_pro:
            raise CountryNotSupportKycPro
        kwargs['address_file_ids'] = ','.join(map(str, kwargs['address_file_ids']))
        name = kwargs.pop('name')
        kwargs['pro_name'] = name
        kwargs['platform'] = KycVerificationPro.Platform.WEB if get_request_platform().is_web() else KycVerificationPro.Platform.APP
        kwargs['flow'] = KYCFlow.STEP_BY_STEP
        kyc_pro = KycProBusiness.create_kyc_verification_pro(g.user, kwargs)
        return kyc_pro.id

    @classmethod
    def get_rejection_reason(cls, user_id) -> str:
        rejection_reason = ""
        last_record = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == user_id,
        ).order_by(KycVerificationPro.id.desc()).first()
        if not last_record:
            return rejection_reason
        lang = get_request_language()
        with force_locale(lang.value):
            return last_record.get_reject_reason() or ""

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user = g.user
        pro_kyc_pass_record: KycVerificationPro = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == user.id,
            KycVerificationPro.status == KycVerificationPro.Status.PASSED
        ).first()
        rejection_reason = cls.get_rejection_reason(user.id)
        if not pro_kyc_pass_record:
            return dict(
                kyc_pro_status=user.kyc_pro_status,
                country='',
                address='',
                rejection_reason=rejection_reason,
                address_file_ids=[],
                address_file_urls=[],
                kyc_name=user.kyc_full_name,
            )
        return dict(
            kyc_pro_status=user.kyc_pro_status,
            country=pro_kyc_pass_record.country,
            address=pro_kyc_pass_record.address,
            rejection_reason="",
            address_file_ids=pro_kyc_pass_record.address_file_ids.split(','),
            address_file_urls=pro_kyc_pass_record.address_file_urls,
            kyc_name=pro_kyc_pass_record.pro_name
        )


@ns.route("/kyc-pro/category")
@respond_with_code
class KycProFileCategoryResource(Resource):

    @classmethod
    def get(cls):
        lang = get_request_language()
        with force_locale(lang.value):
            category_list, category_types_map = KycVerificationPro.get_category_types_map()
            return {
                'category_list': category_list,
                'category_types_map': category_types_map,
                'category_display_map': {i.name: gettext(i.value) for i in KycVerificationPro.FileCategory},
                'type_display_map': {i.name: gettext(i.value) for i in KycVerificationPro.FileType},
            }


@ns.route('/activity-party')
@respond_with_code
class ActivityPartyUserResource(Resource):
    model = ActivityPartyUser

    @classmethod
    @ns.use_kwargs(dict(
        activity_party=EnumField(ActivityPartyUser.ActivityParty, required=True),
    ))
    def get(cls, **kwargs):

        activity_party = kwargs["activity_party"]
        user = get_request_user()
        err = RecordNotFound(message=f"{activity_party.name} user not found")
        if not user:
            raise err
        row = cls.model.query.filter(
            cls.model.user_id == user.id,
            cls.model.activity_party == activity_party,
            cls.model.status == cls.model.Status.VALID
        ).first()
        if not row:
            raise err
        return dict(
            mask_id=row.mask_id,
        )


@ns.route("/edit-account-name/callback")
@respond_with_code
class EditPopupUserResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        HadPoppedEditNameUserCache().add_user(g.user.id)


@ns.route("/account-info")
@respond_with_code
class USerAccountInfoResource(Resource):

    @classmethod
    @cached(3600)
    def get_default_avatar_data(cls):
        res = []
        for key in UserRepository.DEFAULT_AVATAR_LIST:
            avatar_info = {
                "file_key": key,
                "file_url": AWSBucketPublic.get_file_url(key)
            }
            for size in DEFAULT_THUMBNAIL_SCALE:
                avatar_info[f'{size.scale_key}_thumbnail_url'] = AWSBucketPublic.get_file_url(
                    size.gen_thumbnail_key(key))

            res.append(avatar_info)
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user = g.user
        user_extra = user.extra
        return dict(
            default_avatar_list=cls.get_default_avatar_data(),
            account_info=dict(
                name=user.nickname,
                account_name=user_extra.display_account_name,
                avatar=user_extra.avatar_url,
                avatar_key=user_extra.avatar or UserRepository.get_default_avatar(),
                has_set_account_name=user_extra.check_has_set_account_name(),
            )
        )


@ns.route("/status")
@respond_with_code
class UserStatusCheckResource(Resource):


    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        account=fields.String(required=True),
    ))
    @limit_user_frequency(count=10, interval=300)
    def get(cls, **kwargs):
        account = kwargs['account']
        user = User.from_account(account)
        if not user:
            raise InvalidArgument
        is_active = not UserVisitPermissionCache().check_user_permission(
                user.id,
                [UserVisitPermissionCache.FORBIDDEN_VALUE,
                 UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE]
            )
        return dict(
            is_active=is_active
        )


@ns.route("/custom-layouts")
@respond_with_code
class UserCustomLayoutResource(Resource):
    model = UserBusinessCustomLayout


    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        business=EnumField(
            model.Business,
            missing=model.Business.PERPETUAL
        ),
    ))
    def get(cls, **kwargs):
        user = g.user
        model = cls.model
        rows = model.query.filter(
            model.user_id == user.id,
            model.business == kwargs['business'],
        ).all()
        ret = []
        for row in rows:
            ret.append(dict(
                id=row.id,
                name=row.name,
                details=row.details,
            ))
        return ret

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=max_length_validator(15)),
        details=fields.List(fields.Dict, required=True),
        business=EnumField(
            model.Business,
            missing=model.Business.PERPETUAL
        ),
    ))
    def post(cls, **kwargs):
        user = g.user
        business = kwargs['business']
        model = cls.model
        user_id = user.id
        with CacheLock(LockKeys.user_biz_layout(user_id)):
            db.session.rollback()
            exist_count = model.query.filter(
                model.user_id == user_id,
                model.business == business,
            ).count()
            if exist_count >= model.USER_MAX_COUNT:
                raise InvalidArgument

            row = model(
                user_id=user_id,
                business=business,
                name=kwargs['name'],
                details=kwargs['details'],
            )
            db.session.add(row)
            db.session.commit()
            return row.id

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        name=fields.String(required=True, validate=max_length_validator(15)),
        details=fields.List(fields.Dict, required=True),
    ))
    def put(cls, **kwargs):
        user = g.user
        model = cls.model
        user_id = user.id
        with CacheLock(LockKeys.user_biz_layout(user_id)):
            db.session.rollback()
            row = model.query.filter(
                model.id == kwargs['id'],
                model.user_id == user_id,
            ).first()
            if not row:
                raise InvalidArgument

            row.name = kwargs['name']
            row.details = kwargs['details']
            db.session.commit()

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        name=fields.String(required=True, validate=max_length_validator(15)),
    ))
    def patch(cls, **kwargs):
        user = g.user
        model = cls.model
        user_id = user.id
        with CacheLock(LockKeys.user_biz_layout(user_id)):
            db.session.rollback()
            row = model.query.filter(
                model.id == kwargs['id'],
                model.user_id == user_id,
            ).first()
            if not row:
                raise InvalidArgument

            row.name = kwargs['name']
            db.session.commit()

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
    ))
    def delete(cls, **kwargs):
        user = g.user
        model = cls.model
        user_id = user.id
        with CacheLock(LockKeys.user_biz_layout(user_id)):
            db.session.rollback()
            row = model.query.filter(
                model.id == kwargs['id'],
                model.user_id == user_id,
            ).first()
            if not row:
                raise InvalidArgument

            db.session.delete(row)
            db.session.commit()


@ns.route("/active-layout")
@respond_with_code
class UserActiveLayoutResource(Resource):
    model = UserBusinessActiveLayout
    biz_model = UserBusinessCustomLayout


    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        business=EnumField(
            biz_model.Business,
            missing=biz_model.Business.PERPETUAL
        ),
    ))
    def get(cls, **kwargs):
        user = g.user
        model = cls.model
        row = model.query.with_entities(
            model.layout_id
        ).filter(
            model.user_id == user.id,
            model.business == kwargs['business'],
        ).first()
        return row.layout_id if row else None

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        layout_id=fields.Integer(required=True),
        business=EnumField(
            biz_model.Business,
            missing=biz_model.Business.PERPETUAL
        ),
    ))
    def post(cls, **kwargs):
        user = g.user
        business = kwargs['business']
        model = cls.model
        user_id = user.id
        layout_id = kwargs['layout_id']
        with CacheLock(LockKeys.user_biz_layout(user_id)):
            db.session.rollback()
            cls._check_layout_id(layout_id, user_id, business)

            row = model.query.filter(
                model.user_id == user_id,
                model.business == business,
            ).first()
            if not row:
                db.session.add(model(
                    user_id=user_id,
                    business=business,
                    layout_id=layout_id
                ))
                db.session.commit()
                return

            if row.layout_id != layout_id:
                row.layout_id = layout_id
                db.session.commit()

    @classmethod
    def _check_layout_id(
            cls,
            layout_id: int,
            user_id: int,
            business: UserBusinessCustomLayout.Business
    ):
        if layout_id == 0:
            raise InvalidArgument
        if layout_id < 0 and layout_id not in cls.model.SYSTEM_LAYOUT_TUPLE:
            raise InvalidArgument
        if layout_id in cls.model.SYSTEM_LAYOUT_TUPLE:
            return

        model = UserBusinessCustomLayout
        custom_layout = model.query.filter(
            model.id == layout_id,
            model.user_id == user_id,
            model.business == business,
        ).first()
        if not custom_layout:
            raise InvalidArgument