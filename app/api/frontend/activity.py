# -*- coding: utf-8 -*-
import base64
import copy
import decimal
import json
import math
import random
import re
from collections import defaultdict
from datetime import timedelta, date, datetime
from decimal import Decimal
import rsa
from enum import Enum
from itertools import chain
from json import loads as json_loads
from typing import Set

from flask import g
from flask_babel import gettext as _
from flask_restx import fields as fx_fields, marshal
from marshmallow import Schema
from sqlalchemy import func
from webargs import fields

from app.api.common.decorators import limit_user_frequency
from app.business.activity.airdrop import (
    get_airdrop_coupon_details, send_airdrop_coupons, get_airdrop_activity_rewards, batch_send_airdrop_equity_rewards,
    get_airdrop_equity_details,
)
from app.business.activity.trade import TradeActivityBusiness
from app.business.site import BusinessSettings
from app.business.user_group import UserGroupValidator, build_novice_condition_info, add_user_task_info, \
    check_novice_package_user_valid
from app.common.countries import get_country
from app.exceptions import NotSupportedByCountryInOnly<PERSON>ithdrawal, RecordNotFound
from app.exceptions.user import CouponCodeExpired, CouponCodeNotExist, CouponCodeOutOfTimeRange, CouponCodeUsed, \
    CouponHasRunOut, UsingCouponObtained, CouponPoolExpired, CouponSendEnd, UsingCouponStatusInvalid, \
    UsingCouponExpired, UsingCouponInvalid, UserAlreadyOpenBox, BoxGrabEnd, BoxTriggerRiskControl, \
    UserAlreadyOpenOtherBox, NoviceValidateInvalid, ActivityAbnormalUserError, NoviceRiskControl
from app.utils.files import AWSBucketPublic
from app.utils.offset_to_page import query_to_page
from app.utils.text import hide_email, hide_text
from ..common import (Resource, Namespace, respond_with_code, require_login,
                      get_request_platform, get_request_user,
                      json_string_success, require_geetest, response_replace_host_url)
from ..common.fields import PageField, LimitField, TimestampMarshalField, \
    AmountField, EnumMarshalField, EnumField, PositiveDecimalField, mm_fields, AssetField, DeviceIdField, \
    TimestampField
from ..common.request import is_old_app_request, get_request_ip, get_request_language
from ... import config
from ...assets import get_asset_chain_config
from ...business import (
    CacheLock, LockKeys, Locked,
    update_gift_history_task, ServerClient, PerpetualServerClient, RESTClient,
    cached, mem_cached, current_app, BalanceManager, PriceManager, ALL_MARGIN_ACCOUNT_ID
)
from ...business.activity.activity_party import WallacyActivityMixin, VertusActivityMixin
from ...business.coupon import VipUpgradeCouponStatus
from ...business.equity_center.helper import EquityCenterService
from ...business.kline import aggregate_price_kline_data, AggregatePeriodType
from ...business.activity.airdrop import AIRDROP_ADMIN_USER_ID
from ...business.activity.ambassador import ActivityBusiness
from ...business.activity.discount import process_discount_activity_order
from ...business.activity.fifth import (
    ANNIVERSARY_BOX_NUM,
    ULTIMATE_BOX_NUM,
    FifthBoxManager,
    report_ip_box_risk_event,
    report_device_box_risk_event,
)
from ...business.activity.ieo import process_ieo_activity_order, cancel_ieo_activity_order
from ...business.activity.mining import (
    get_user_mining_qualify_info,
    PledgeAmmMiningHelper,
    is_mining_join_user,
    add_mining_join_user,
    update_activity_detail_cache_task,
)
from ...business.activity.launch_pool import LaunchMiningOp
from ...business.risk_control.user import check_new_device_user
from ...business.trade import get_main_and_sub_ids, get_user_spot_no_strategy_trade_amount, \
    get_user_perpetual_trade_amount, get_user_amm_trade_amount, get_one_user_exchange_data, \
    get_user_auto_invest_trade_amount, get_user_spot_grid_trade_amount, get_user_all_trade_amount
from ...business.activity.sixth import SixthRewardManager, SixthQuestionManager, \
    SixthUserRewardSender, SixthQrcodeManager, SixthConfigManager
from ...business.activity.seventh import SeventhRewardManager, SeventhUserRewardSender, \
    SeventhConfigManager, SeventhQrcodeManager
from ...business.coupon.base import UserCouponStatus, get_coupon_service, CouponPoolStatus
from ...business.coupon.pool import get_user_coupon_pools, get_user_available_pool, check_user_active_coupon, \
    get_coupon_activity_server
from ...business.coupon.utils import CouponTool
from ...business.user import UserRepository, get_forbidden_region_code, UserPreferences
from ...business.referral import ReferralBusiness, TreeAmbHelper
from ...business.bus_referral import BusRelationUserQuerier
from ...caches import AmmMarketCache
from ...caches import PopupWindowCache, ActivityViewCache
from ...caches.activity import (ActivateReportCache, FifthAnniversaryBillStatisticsCache,
                                FifthAnniversaryHighFiveCache,
                                MiningActivityCache, MiningActivityDetailCache,
                                MiningActivityProfitPredictCache, MiningActivityAmmCache,
                                BannerCache,
                                AirdropActivityCache, AirdropActivityDetailCache,
                                FifthBoxRemainNumCache, FifthUserBoxRewardCache,
                                FifthBroadcastCache, FifthAnniversaryThankLetterCache,
                                FifthBoxOpenDeviceCache, FifthBoxOpenIpCache, FifthRiskUserCache,
                                FifthUserFeeCache,
                                AirdropActivityReceivedCountCache, AirdropActivityLotteryCache,
                                IeoActivityCache, IeoActivityDetailCache,
                                IeoActivityUserSignedContractCache,
                                AirdropActivityUserHasAnsweredCache,
                                AirdropActivityQuestionBankCache,
                                CouponPopupReadCache, CouponPoolCache, CalendarActivityCache,
                                PushAvailablePoolCache,
                                DynamicUserCouponCache,
                                PerpetualActivityBannerCache, PerpetualSpecialSummaryDataCache,
                                PerpetualSpecialUsersCache, PerpetualSpecialVisitCache,
                                LuckyDrawDateCache,
                                NoviceActivityCache, NoviceUserCache, NoviceUserTradeCache,
                                DiscountActivityDetailCache, DiscountActivityCache,
                                DiscountActivityLotteryCache, CoinHalvingActivityCache, UserActivityTradeValueHourCache,
                                DepositBonusActivityCache, DepositBonusActivityDetailCache,
                                DepositBonusActivityBannerCache)
from ...caches.coinex_wallet import TrafficCache
from ...caches.kline import AssetRecentChangeRateCache, AssetInformationCache, TagInfoCache
from ...caches.user import UserVisitPermissionCache
from ...caches.perpetual import DirectPerpetualProfitRealMonthRankCache, DirectPerpetualProfitRealYearRankCache
from ...common import Language, PrecisionEnum, BalanceBusiness, NoviceTradeType, SUB_ACCOUNT_NUM_LIMIT
from ...exceptions import (
    ActivityNotExists,
    InsufficientBalance,
    InvalidArgument, AnswerNotCorrect, UsingCouponLimit,
)
from ...models import (
    Banner, User,
    DepositActivity, DepositActivityRank,
    db, PopupWindow, BalanceUpdateBusiness,
    GiftHistory, MiningActivity, MiningActivityDetail,
    MiningUserRewordHistory, MiningUserPledgeAmmRewardHistory,
    MiningUserPledgeInfo, MiningPledgeConfig,
    MiningUserAmmInfo, MiningUserPledgeAmmOperateHistory, VipUser,
    SubAccount, PerpetualBalanceTransfer,
    Deposit, FiatOrder, Ambassador, InvestmentTransferHistory, LiquidityHistory, CoinInformation
)
from ...models.activity import CouponCodePool, CouponExchangeHistory, CouponPool, FifthAnniversarySummary, UserCoupon, \
    Coupon, CouponApply, SatoshiBidHistory, \
    ExperienceFeeUserCoupon, TradingGiftUserCoupon, InvestmentIncRateUserCoupon, CashBackFeeUserCoupon, \
    FifthAnniversaryChallenge, FifthAnniversaryBox, UserPerpetualTask, PerpetualNoviceBenefits, \
    NovicePrefectureActivity, SixthAnniversaryUser, SeventhAnniversaryUser, CopyTradingExperienceFeeUserCoupon, \
    SeventhAnniversaryUserBill, SEVENTH_TOP_TRADE_ASSETS, SEVENTH_TOP_TRENDING_ASSETS
from ...models.auto_invest import AutoInvestPlan
from ...models.equity_center import EquityType
from ...models.exchange import AssetExchangeOrder
from ...models.mongo.coinex_wallet import TrafficMySQL as Traffic
from ...models.operation import ActivityBlackList, ActivityWhiteList, AirdropActivityDetail, \
    AirdropActivity, AirdropActivityRewardHistory, AirdropActivityCondition, AirdropActivityReward, \
    AirdropActivityLotteryHistory, IeoActivityDetail, IeoActivity, IeoActivityOrder, \
    IeoActivityLotteryHistory, AirdropActivityUserRecord, \
    IeoAssetInformation, TradeRankActivityJoinUser, TradeRankActivityUserInfo, TradeRankActivity, \
    TradeRankActivityDetail, CalendarContent, BannerContent, OperationAmbassadorActivity, \
    OperationAmbassadorActivityContent, AmbassadorActivityApplyUser, AmbassadorActivityUserInfo, \
    AirdropActivityStatistic, DiscountActivityDetail, DiscountActivity, \
    DiscountActivityLotteryHistory, DiscountActivityCondition, \
    DiscountActivityOrder, ActivityCondition, DiscountActivityStatistic, ExposureActivitySetting, \
    IeoActivityCondition, \
    WallacyActivityUser, WallacyActivityGiftHistory, DepositBonusActivity, \
    DepositBonusActivityApplyUser, \
    DepositBonusActivityConfig, DepositBonusActivityStatistic, DepositBonusActivityUserInfo, \
    DepositBonusActivityCondition, DepositBonusActivityContent
from ...models.pledge import PledgePosition
from ...models.strategy import UserStrategy
from ...schedules.activity import update_ieo_activity_schedule, update_perpetual_special_task_status_task, \
    check_task_finished_and_update_pool
from ...utils import (
    now, amount_to_str,
    quantize_amount, current_timestamp, GeoIP
)
from ...utils.date_ import date_to_datetime, datetime_to_time, today, str_to_datetime, today_datetime
from ...utils.date_ import timestamp_to_datetime
from ...utils.text import hide_text_default

ns = Namespace('Activities')


@ns.route('/banners')
@respond_with_code
class BannersResource(Resource):

    @classmethod
    def get(cls):
        platform = (Banner.Platform.APP if get_request_platform().is_mobile()
                    else Banner.Platform.WEB)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in Banner.AVAILABLE_LANGS:
            lang = Banner.AVAILABLE_LANGS[0]
        cache = BannerCache(platform, lang)
        if data := cache.read():
            return json_string_success(data)

        return []


@ns.route('/calendar')
@respond_with_code
class CalendarResource(Resource):

    @classmethod
    @mem_cached(300)
    def _get_banner(cls, lang):
        _now = now()
        banner_data = {}
        # 活动日历的banner最多一个
        banner = Banner.query.filter(
            Banner.status == Banner.Status.VALID,
            Banner.started_at < _now,
            Banner.ended_at > _now,
            Banner.affect_calendar.is_(True),
        ).first()
        if banner:
            banner_id = banner.id
            banner_content = BannerContent.query.filter(
                BannerContent.owner_id == banner_id,
                BannerContent.lang == lang,
            ).first()
            if banner_content:
                banner_url = banner_content.url or banner.url
                img_src = banner_content.img_src
                banner_data['param'] = Banner.build_legacy_params(Banner.Platform.WEB, banner_url)
                banner_data['img_src'] = img_src
                banner_data['started_at'] = banner.started_at
                banner_data['ended_at'] = banner.ended_at
        return banner_data

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=5),
        page=PageField(missing=1),
    ))
    def get(cls, **kwargs):
        """
        日历-活动日历
        """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in CalendarContent.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        banner_data = cls._get_banner(lang.name)
        calendar_data = json_loads(CalendarActivityCache(lang.value).read())

        result = dict(
            calendar=calendar_data,
            banner=banner_data
        )
        return result


@ns.route('/popup-windows')
@respond_with_code
class PopupWindowsResource(Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        platform = (PopupWindow.Platform.APP if get_request_platform().is_mobile()
                    else PopupWindow.Platform.WEB)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in PopupWindow.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        user_id = user.id if (user := get_request_user()) else None
        if user and user.is_sub_account:
            return []
        return PopupWindowCache(platform, lang).filter_by_user(user_id)


@ns.route('/list')
@respond_with_code
class ActivitiesResource(Resource):

    @classmethod
    def get(cls):
        platform = ActivityViewCache.APP if get_request_platform().is_mobile() else ActivityViewCache.WEB
        res = cls._get(platform)
        if is_old_app_request(0, 12):
            info = copy.copy(res)
            info['trade_activity'] = []
            return info
        return res

    @classmethod
    @mem_cached(120)
    def _get(cls, platform: str):
        if data := ActivityViewCache(platform).read():
            return json.loads(data)
        return {
            'trade_activity': [],
            'deposit_activity': [],
            'pledge_mining_activity': [],
            'amm_mining_activity': [],
            'airdrop_activities': [],
            'dibs_activities': [],
        }


@ns.route('/exposure/list')
@respond_with_code
class ExposureActivitiesResource(Resource):

    @classmethod
    def get(cls):
        return cls._get()

    @classmethod
    @mem_cached(120)
    def _get(cls):
        exposure_list = ExposureActivitySetting.query.filter(
            ExposureActivitySetting.state == ExposureActivitySetting.StateType.VALID,
        ).all()
        submenu_list = set()
        menu_list = set()
        for row in exposure_list:
            if row.active_status != ExposureActivitySetting.ActiveStatus.STARTED:
                continue
            if row.submenu:
                submenu_list.add(row.submenu.name)
            if row.menu:
                menu_list.add(row.menu.name)
        return dict(
            submenu_list=list(submenu_list),
            menu_list=list(menu_list),
        )


@ns.route('/qualification/<int:trade_activity_id>')
@respond_with_code
class ActivityQualificationResource(Resource):

    @classmethod
    @require_login
    def get(cls, trade_activity_id):
        """过时接口，以后可移除"""
        return False


@ns.route('/trade/rank')
@respond_with_code
class TradeActivityRankListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(['online', 'coming', 'finish'])
    ))
    def get(cls, **kwargs):
        """
        交易排名（过时接口，以后可移除）
        """
        return []


@ns.route('/perpetual-trade/rank')
@respond_with_code
class PerpetualTradeActivityRankListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(['online', 'coming', 'finish'])
    ))
    def get(cls, **kwargs):
        """
        合约交易排名（过时接口，以后可移除）
        """
        return []


# noinspection PyUnresolvedReferences
@ns.route('/trade/rank/<int:id_>')
@respond_with_code
class TradeActivityRankResource(Resource):

    @classmethod
    def get(cls, id_):
        """
        用户交易排名（过时接口，以后可移除）
        """
        return {}


@ns.route('/perpetual-trade/rank/<int:id_>')
@respond_with_code
class PerpetualTradeActivityRankResource(Resource):

    @classmethod
    def get(cls, id_):
        """
        合约用户交易排名（过时接口，以后可移除）
        """
        return {}


@ns.route('/trade-rank/list')
@respond_with_code
class TradeRankActivityListResource(Resource):
    marshal_fields = {
        'id': fx_fields.Integer,
        'title': fx_fields.String,
        'gift_asset': fx_fields.String,
        'gift_amount': fx_fields.String,
        'started_at': TimestampMarshalField,
        'ended_at': TimestampMarshalField,
        'status': fx_fields.String,
        'cover_url': fx_fields.String
    }

    class Status:
        PENDING = 'PENDING'
        ONGOING = 'ONGOING'
        ENDED = 'ENDED'
        
    class FilterStatus(Enum):
        ONLINE = 'ONLINE'
        ENDED = 'ENDED'

    @classmethod
    @ns.use_kwargs(dict(
        category=EnumField(TradeRankActivity.ActivityCategory),
        page=PageField,
        limit=LimitField,
        status=EnumField(FilterStatus)
    ))
    def get(cls, **kwargs):
        """
        交易排名活动列表
        """
        status = kwargs.get('status')
        activity_category = kwargs.get('category')
        page, limit = kwargs['page'], kwargs['limit']
        query = TradeRankActivity.query.order_by(TradeRankActivity.started_at.desc())
        if status == cls.FilterStatus.ONLINE:
            query = query.filter(TradeRankActivity.status == TradeRankActivity.Status.ONLINE)
        else:
            query = query.filter(TradeRankActivity.status.in_((TradeRankActivity.Status.ONLINE,
                                          TradeRankActivity.Status.FINISHED)))
        if not activity_category:
            records = query.all()
        else:
            if activity_category == TradeRankActivity.ActivityCategory.SPOT:
                activity_types = [TradeRankActivity.Type.SPOT_TRADE, TradeRankActivity.Type.SPOT_NET_BUY]
            else:
                activity_types = [TradeRankActivity.Type.PERPETUAL_TRADE, TradeRankActivity.Type.PERPETUAL_INCOME,
                                  TradeRankActivity.Type.PERPETUAL_INCOME_RATE]
            records = query.filter(
                TradeRankActivity.type.in_(activity_types)
            ).all()
        res = cls.fmt_records(records, page, limit, status)
        return res

    @classmethod
    def fmt_records(cls, records, page, limit, status):
        if not records:
            return dict(
                has_next=False,
                curr_page=page,
                count=0,
                data=[],
                items=[],  # 老字段保留兼容
                total=0,
                total_page=0,
            )
        
        lang = Language(g.lang)
        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id.in_([a.id for a in records]),
            TradeRankActivityDetail.lang == lang
        ).with_entities(
            TradeRankActivityDetail.trade_activity_id,
            TradeRankActivityDetail.title,
        ).all()
        detail_map = dict(details)
        now_ = now()
        online_list, ended_list = [], []
        for item in records:
            if item.status == TradeRankActivity.Status.FINISHED:
                ended_list.append(item)
            elif item.ended_at < now_:
                ended_list.append(item)
            else:
                online_list.append(item)
        res = []
        if status == cls.FilterStatus.ONLINE:
            act_lis = online_list
        elif status == cls.FilterStatus.ENDED:
            act_lis = ended_list
        else:
            act_lis = online_list + ended_list
        total = len(act_lis)
        for item in act_lis:
            item = item.to_dict()
            if item['ended_at'] < now_:
                item['status'] = cls.Status.ENDED
            elif item['started_at'] > now_:
                item['status'] = cls.Status.PENDING
            else:
                item['status'] = cls.Status.ONGOING
            item['title'] = detail_map.get(item['id'])
            item['cover_url'] = AWSBucketPublic.get_file_url(item['cover'])
            res.append(item)
        data = res[(page - 1) * limit: page * limit]
        end = page * limit
        marshal_items = marshal(data, cls.marshal_fields)
        return dict(
            has_next=end < total,
            curr_page=page,
            count=len(data),
            data=marshal_items,
            items=marshal_items,  # 老字段保留兼容
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/trade-rank/<int:id_>')
@respond_with_code
class TradeRankActivityResource(Resource):
    class Status:
        PENDING = 'PENDING'
        ONGOING = 'ONGOING'
        ENDED = 'ENDED'

    @classmethod
    def get_trade_rank_info(cls, user, activity):
        activity: TradeRankActivity
        rule_data = json.loads(activity.gift_rules)
        rules = []
        for rule in rule_data:
            rank_min, rank_max = rule['rank_min'], rule['rank_max']
            count = rule['rank_max'] - rule['rank_min'] + 1
            amount = Decimal(rule['rank_amount'])
            rules.append(dict(
                rank=f'{rank_min}-{rank_max}' if rank_min < rank_max else f'{rank_min}',
                amount_per_user=amount_to_str(amount, 8),
                total_amount=amount_to_str(amount * count, 8)
            ))
        limit = rule_data[-1]['rank_max']
        displayed_rank_ids = [rule['rank_max'] for rule in rule_data]

        last_record = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == activity.id,
        ).with_entities(func.max(TradeRankActivityUserInfo.report_at).label('report_at')).first()
        if not last_record:
            return dict(ranks=[], rules=rules, user_gift_amount=0, user_rank=None, max_rank=limit)
        else:
            report_at = last_record.report_at
        records = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == activity.id,
            TradeRankActivityUserInfo.report_at == report_at,
            TradeRankActivityUserInfo.rank.isnot(None)
        ).order_by(TradeRankActivityUserInfo.rank).limit(limit)

        user_gift_amount, user_trade_amount, user_total_amount, user_rank = 0, 0, 0, None
        if user:
            user_info = TradeRankActivityUserInfo.query.filter(
                TradeRankActivityUserInfo.trade_activity_id == activity.id,
                TradeRankActivityUserInfo.user_id == user.id,
                TradeRankActivityUserInfo.report_at == report_at
            ).first()
            if user_info:
                user_gift_amount = user_info.gift_amount
                user_rank = user_info.rank
                user_total_amount = user_info.target_value
                user_trade_amount = user_info.trade_amount
        rank_gift_amount = sum((r['rank_max'] - r['rank_min'] + 1) * Decimal(r['rank_amount']) for r in rule_data)
        split_gift_amount = max(activity.gift_amount - rank_gift_amount, 0)
        ranks = []
        for item in records:
            if item.rank in displayed_rank_ids:
                u = User.query.get(item.user_id)
                ranks.append(
                    [
                        item.rank,
                        u.nickname,
                        '',     # email字段，兼容保留
                        quantize_amount(item.target_value, 8)
                    ]
                )
        data = dict(
            ranks=ranks,
            rules=rules,
            user_total_amount=amount_to_str(user_total_amount, 8),
            user_trade_amount=amount_to_str(user_trade_amount, 8),
            split_gift_amount=max(split_gift_amount, 0),
            user_gift_amount=user_gift_amount,
            user_rank=user_rank,
            max_rank=limit
        )
        black_user_ids = ActivityBlackList.query.filter(
            ActivityBlackList.activity_id == activity.activity_id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED
        ).with_entities(ActivityBlackList.user_id).all()
        black_user_ids = [item.user_id for item in black_user_ids]
        total_trade_value = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == activity.id,
            TradeRankActivityUserInfo.report_at == report_at,
            TradeRankActivityUserInfo.rank > limit,
            TradeRankActivityUserInfo.user_id.notin_(black_user_ids)
        ).with_entities(func.sum(TradeRankActivityUserInfo.trade_amount)).scalar() or 0
        data['total_amount'] = amount_to_str(total_trade_value, 8)
        return data

    @classmethod
    def get(cls, id_):
        """
        交易排名活动详情
        """
        activity = TradeRankActivity.query.filter(
            TradeRankActivity.status.in_((TradeRankActivity.Status.ONLINE,
                                          TradeRankActivity.Status.FINISHED)),
            TradeRankActivity.id == id_
        ).first()
        if not activity:
            raise InvalidArgument
        lang = Language(g.lang)
        detail = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id == id_,
            TradeRankActivityDetail.lang == lang
        ).first()

        is_online = activity.status == TradeRankActivity.Status.ONLINE and \
                    activity.ended_at > now()

        is_black_list_user = can_join = is_join_user = False

        conditions = json.loads(activity.user_group_condition)
        if user := get_request_user():
            black_list_user = ActivityBlackList.query.filter(
                ActivityBlackList.user_id == user.id,
                ActivityBlackList.activity_id == activity.activity_id,
                ActivityBlackList.status == ActivityBlackList.Status.PASSED
            ).first()

            validate_passed, failed_items = UserGroupValidator(user.id,
                                                               activity.user_group_condition).validate()
            failed_keys = [item['key'] for item in failed_items]
            for c in conditions:
                c['result'] = c['key'] not in failed_keys
            is_black_list_user = bool(black_list_user)
            can_join = not is_black_list_user and validate_passed and is_online

            join_user = TradeRankActivityJoinUser.query.filter(
                TradeRankActivityJoinUser.user_id == user.id,
                TradeRankActivityJoinUser.trade_activity_id == activity.id
            ).first()
            is_join_user = bool(join_user)
        else:
            can_join = False
            for c in conditions:
                c['result'] = False
        now_ = now()
        if activity.ended_at < now_:
            status = cls.Status.ENDED
        elif activity.started_at > now_:
            status = cls.Status.PENDING
        else:
            status = cls.Status.ONGOING
        if activity.market_type == TradeRankActivity.MarketType.ALL and not activity.exclude_markets:
            markets = None
        else:
            markets = TradeActivityBusiness(activity.id).get_markets()
        if activity.type in TradeRankActivity.PERPETUAL_TYPES:
            activity_category = TradeRankActivity.ActivityCategory.PERPETUAL.name
        else:
            activity_category = TradeRankActivity.ActivityCategory.SPOT.name
        return dict(
            title=detail.title,
            announce_url=activity.announce_url,
            type=activity.type.name,
            category=activity_category,
            status=status,
            markets=markets,
            started_at=int(activity.started_at.timestamp()),
            ended_at=int(activity.ended_at.timestamp()),
            gift_asset=activity.gift_asset,
            gift_amount=quantize_amount(activity.gift_amount, 8),
            least_trade_amount=quantize_amount(activity.least_trade_amount, 8),
            max_split_reward_amount=quantize_amount(activity.max_split_reward_amount, 8) \
                if activity.max_split_reward_amount else None,
            trade_asset=TradeActivityBusiness(activity.id).get_trade_asset(),
            cover_url=AWSBucketPublic.get_file_url(activity.cover),
            conditions=conditions,
            is_black_list_user=is_black_list_user,
            is_join_user=is_join_user,
            can_join=can_join,
            **cls.get_trade_rank_info(user, activity)
        )


@ns.route('/trade-rank/<int:id_>/user')
@respond_with_code
class TradeRankActivityUserResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, id_):
        """
        交易排名活动报名
        """
        if UserRepository.is_abnormal_user(g.user.id):
            raise ActivityAbnormalUserError(message=_("你的账户暂被限制参与活动，如有疑问请联系客服。"))
        activity = TradeRankActivity.query.filter(
            TradeRankActivity.id == id_,
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.ended_at > now()
        ).first()
        if not activity:
            raise ActivityNotExists
        join_user = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.user_id == g.user.id,
            TradeRankActivityJoinUser.trade_activity_id == activity.id
        ).first()
        if join_user:
            raise InvalidArgument

        result, items = UserGroupValidator(g.user.id, activity.user_group_condition).validate()
        if not result:
            raise InvalidArgument(message="Not qualified")
        black_list_user = ActivityBlackList.query.filter(
            ActivityBlackList.user_id == g.user.id,
            ActivityBlackList.activity_id == activity.activity_id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED
        ).first()
        if black_list_user:
            raise InvalidArgument(message="Black list user")
        TradeActivityBusiness(activity.id).add_user(g.user.id, commit=True)


@ns.route('/trade-rank/history')
@respond_with_code
class TradeRankActivityJoinHistoryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        交易排名活动 参与记录
        """
        query = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.user_id == g.user.id
        ).order_by(TradeRankActivityJoinUser.id.desc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = pagination.items
        if not items:
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                items=[],  # 老字段保留兼容
                total=0,
                total_page=1,
            )

        ids = [item.trade_activity_id for item in items]
        lang = Language(g.lang)
        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id.in_(ids),
            TradeRankActivityDetail.lang == lang
        ).with_entities(TradeRankActivityDetail.trade_activity_id,
                        TradeRankActivityDetail.title).all()
        detail_map = dict(details)
        activities = TradeRankActivity.query.filter(
            TradeRankActivity.id.in_(ids)
        ).all()
        activity_map = {item.id: item for item in activities}
        infos = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.user_id == g.user.id,
            TradeRankActivityUserInfo.trade_activity_id.in_(ids)
        ).all()
        info_map = {item.trade_activity_id: item for item in infos}
        res = []
        for item in items:
            activity = activity_map[item.trade_activity_id]
            info = info_map.get(item.trade_activity_id)
            res.append(dict(
                id=item.trade_activity_id,
                title=detail_map.get(item.trade_activity_id),
                gift_amount=activity.gift_amount,
                gift_asset=activity.gift_asset,
                user_gift_amount=info.gift_amount if info else Decimal()
            ))
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(res),
            data=res,
            items=res,  # 老字段保留兼容
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/deposit/rank')
@respond_with_code
class DepositActivityRankListResource(Resource):
    marshal_fields = {
        'deposit_activity_id': fx_fields.Integer(attribute='id'),
        'deposit_coin': fx_fields.String(attribute='deposit_asset'),
        'name': fx_fields.String,
        'gift_coin': fx_fields.String(attribute='gift_asset'),
        'total_amount': AmountField,
        'least_amount': AmountField,
        'logo_url': fx_fields.String,
        'gift_type': EnumMarshalField(
            DepositActivity.Type, attribute='type', output_field_name=False),
        'start_time': TimestampMarshalField(attribute='started_at'),
        'end_time': TimestampMarshalField(attribute='ended_at'),
        'zendesk_url': fx_fields.String,
        'status': EnumMarshalField(
            DepositActivity.Status, output_field_name=False),
    }

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(['online', 'coming', 'finish'])
    ))
    def get(cls, **kwargs):
        """
        充值排名
        """
        query = DepositActivity.query.filter(
            DepositActivity.status.in_([DepositActivity.Status.PASSED,
                                        DepositActivity.Status.FINISHED]),
            DepositActivity.type.in_([DepositActivity.Type.PARTITION,
                                      DepositActivity.Type.RANK]),
            DepositActivity.ended_at >= now() + timedelta(days=-7)
        ).order_by(DepositActivity.started_at.desc())
        if status := kwargs.get('status'):
            if status == 'online':
                query = query.filter(
                    DepositActivity.status == DepositActivity.Status.PASSED,
                    DepositActivity.started_at <= now(),
                    DepositActivity.ended_at > now()
                )
            elif status == 'coming':
                query = query.filter(
                    DepositActivity.status == DepositActivity.Status.PASSED,
                    DepositActivity.started_at > now()
                )
            elif status == 'finish':
                query = query.filter(
                    DepositActivity.status == DepositActivity.Status.FINISHED,
                )
        return marshal(query.all(), cls.marshal_fields)


def get_deposit_rank_info(record: DepositActivity):
    last_record: DepositActivityRank = DepositActivityRank.query.filter(
        DepositActivityRank.activity_id == record.activity_id
    ).order_by(DepositActivityRank.id.desc()).first()
    if not last_record:
        return []
    query = DepositActivityRank.query.filter(
        DepositActivityRank.activity_id == last_record.activity_id,
        DepositActivityRank.rank_at == last_record.rank_at
    ).order_by(DepositActivityRank.rank).limit(100)
    result = []
    for item in query:
        result.append(
            [
                item.rank,
                item.user.hidden_name,
                amount_to_str(item.deposit_amount, 8)
            ]
        )
    return result


# noinspection PyUnresolvedReferences
@ns.route('/deposit/rank/<int:id_>')
@respond_with_code
class DepositActivityRankResource(Resource):
    marshal_fields = {
        'deposit_activity_id': fx_fields.Integer(attribute='id'),
        'deposit_coin': fx_fields.String(attribute='deposit_asset'),
        'name': fx_fields.String,
        'gift_coin': fx_fields.String(attribute='gift_asset'),
        'total_amount': AmountField,
        'least_amount': AmountField,
        'logo_url': fx_fields.String,
        'gift_type': EnumMarshalField(
            DepositActivity.Type, attribute='type', output_field_name=False),
        'start_time': TimestampMarshalField(attribute='started_at'),
        'end_time': TimestampMarshalField(attribute='ended_at'),
        'zendesk_url': fx_fields.String,
        'status': EnumMarshalField(
            DepositActivity.Status, output_field_name=False),
        'rank_info': fx_fields.Raw(attribute=lambda x: get_deposit_rank_info(x)),
        'update_time': fx_fields.Integer(
            attribute=lambda x: current_timestamp(to_int=True))
    }

    @classmethod
    def get(cls, id_):
        """
        用户充值排名
        """
        activity: DepositActivity = DepositActivity.query.filter(
            DepositActivity.id == id_,
            DepositActivity.status != DepositActivity.Status.DELETED,
            DepositActivity.ended_at >= now() + timedelta(days=-7)
        ).first()
        if not activity:
            raise ActivityNotExists
        result = marshal(activity, cls.marshal_fields)
        user_name, user_rank, user_deposit_amount = None, None, None
        if user := get_request_user():
            if rank_user := DepositActivityRank.query.filter(
                    DepositActivityRank.activity_id == activity.activity_id,
                    DepositActivityRank.user_id == user.id
            ).first():
                user_name = rank_user.user.name_displayed
                user_rank = rank_user.rank
                user_deposit_amount = rank_user.deposit_amount
            result.update(dict(
                user_name=user_name, user_rank=user_rank,
                user_deposit_amount=amount_to_str(user_deposit_amount, 8)
                if user_deposit_amount else user_deposit_amount
            ))
        return result


@ns.route('/mining')
@respond_with_code
class MiningActivityListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        mining_mode=EnumField(MiningActivity.MiningMode, required=False)
    ))
    def get(cls, **kwargs):
        """
        挖矿-活动列表
        """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in MiningActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        mode = kwargs.get('mining_mode')
        return cls._get(mode.value if mode else mode, lang.value)

    @classmethod
    @mem_cached(120)
    def _get(cls, mode, lang):
        if data := MiningActivityCache(mode, lang).read():
            return json_string_success(data)
        return []


@ns.route('/mining/<int:activity_id>')
@respond_with_code
class MiningActivityDetailResource(Resource):

    @classmethod
    def get(cls, activity_id):
        """ 挖矿活动详情 """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in MiningActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT

        if res := MiningActivityDetailCache.get(activity_id, lang):
            return json_string_success(res)
        raise InvalidArgument

    @classmethod
    @require_login
    def post(cls, activity_id):
        """ 挖矿活动-用户报名 """
        user_id = g.user.id
        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument
        if activity.active_status == MiningActivity.ActiveStatus.FINISHED:
            raise InvalidArgument

        is_qualified, _ = get_user_mining_qualify_info(activity, g.user)
        if not is_qualified:
            raise InvalidArgument(message="You are not qualified for this mining activity")

        add_mining_join_user(activity, user_id)


@ns.route('/mining/user-info')
@respond_with_code
class MiningActivityUserInfoResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """ 用户某个挖矿活动的信息 """
        activity_id = kwargs['activity_id']
        user_id = g.user.id
        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument

        joined = is_mining_join_user(activity, user_id)  # 是否已加入
        if joined:
            is_qualified = True
            qualify_tip = ""
        else:
            is_qualified, qualify_tip = get_user_mining_qualify_info(activity, g.user)  # 是否有参与资格

        zero = Decimal()
        res = {
            "is_qualified": is_qualified,
            "qualify_tip": qualify_tip,
            "joined": joined,
            "gift_asset": activity.mining_asset,
        }
        if not joined:
            if activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
                config = MiningPledgeConfig.query.filter(
                    MiningPledgeConfig.mining_activity_id == activity_id,
                ).first()
                res["total_gift_amount"] = res["remain_gift_amount"] = res["mining_amount"] = zero
                res["mining_asset"] = config.pledge_asset
            return res

        if activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
            info = MiningUserPledgeInfo.query.filter(
                MiningUserPledgeInfo.mining_activity_id == activity_id,
                MiningUserPledgeInfo.user_id == user_id
            ).first()
            res["total_gift_amount"] = quantize_amount(info.total_gift_amount if info else zero, 8)
            res["remain_gift_amount"] = quantize_amount(info.remain_gift_amount if info else zero, 8)
            res["mining_amount"] = info.pledge_amount if info else zero
            config = MiningPledgeConfig.query.filter(MiningPledgeConfig.mining_activity_id == activity_id).first()
            res["mining_asset"] = config.pledge_asset
        elif activity.mining_mode == MiningActivity.MiningMode.AMM:
            info = MiningUserAmmInfo.query.filter(
                MiningUserAmmInfo.mining_activity_id == activity_id,
                MiningUserAmmInfo.user_id == user_id,
            ).first()
            res["total_gift_amount"] = quantize_amount(info.total_gift_amount if info else zero, 8)
            res["remain_gift_amount"] = quantize_amount(info.remain_gift_amount if info else zero, 8)
            res['mining_amount'] = MiningActivityAmmCache(activity_id).get_user_liq_usd(user_id)
        elif activity.mining_mode in (MiningActivity.MiningMode.TRADE, MiningActivity.MiningMode.PENDING):
            if info := MiningActivityProfitPredictCache(activity_id).get_predict(user_id):
                res.update(info)
            else:
                res['daily_gift_amount'] = zero
                res['mining_amount'] = zero

            total_gift_amount = MiningUserRewordHistory.query.filter(
                MiningUserRewordHistory.mining_activity_id == activity_id,
                MiningUserRewordHistory.user_id == user_id
            ).with_entities(
                func.sum(MiningUserRewordHistory.gift_amount)
            ).scalar() or zero
            res["total_gift_amount"] = total_gift_amount

        return res


@ns.route('/mining/reward')
@respond_with_code
class MiningActivityRewardResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            activity_id=fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 挖矿收益记录 """
        activity_id = kwargs['activity_id']
        page, limit = kwargs['page'], kwargs['limit']
        user_id = g.user.id
        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument

        if activity.mining_mode in (
                MiningActivity.MiningMode.TRADE,
                MiningActivity.MiningMode.PENDING,
        ):
            query = MiningUserRewordHistory.query.filter(
                MiningUserRewordHistory.mining_activity_id == activity_id,
                MiningUserRewordHistory.user_id == user_id,
                MiningUserRewordHistory.status == MiningUserRewordHistory.StatusType.FINISHED,
            ).order_by(MiningUserRewordHistory.report_date.desc())
        else:
            query = MiningUserPledgeAmmRewardHistory.query.filter(
                MiningUserPledgeAmmRewardHistory.mining_activity_id == activity_id,
                MiningUserPledgeAmmRewardHistory.user_id == user_id,
                MiningUserPledgeAmmRewardHistory.status == MiningUserPledgeAmmRewardHistory.StatusType.FINISHED,
            ).order_by(MiningUserPledgeAmmRewardHistory.reward_at.desc())

        mining_asset = None
        if activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
            pledge_config = MiningPledgeConfig.query.filter(
                MiningPledgeConfig.mining_activity_id == activity_id,
            ).first()
            mining_asset = pledge_config.pledge_asset

        result_items = []
        records = query.paginate(page, limit, error_out=False)
        for item in records.items:
            if activity.mining_mode in (
                    MiningActivity.MiningMode.TRADE,
                    MiningActivity.MiningMode.PENDING,
            ):
                # 交易、挂单的report_date是某一天的奖励
                time_ = date_to_datetime(item.report_date) + timedelta(days=1)
            else:
                time_ = item.reward_at
            if activity.mining_mode == MiningActivity.MiningMode.AMM:
                # amm是市值, *_usd字段
                mining_amount = item.mining_usd
                total_mining_amount = item.total_mining_usd
            else:
                mining_amount = item.mining_amount
                total_mining_amount = item.total_mining_amount
            r = dict(
                mining_amount=quantize_amount(mining_amount, PrecisionEnum.COIN_PLACES),
                total_mining_amount=quantize_amount(total_mining_amount, PrecisionEnum.COIN_PLACES),
                time=time_,
                percent=quantize_amount(item.mining_amount / item.total_mining_amount, 4),
                gift_amount=item.gift_amount,
                gift_asset=item.gift_asset,
            )
            if activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
                r['mining_asset'] = mining_asset
            result_items.append(r)

        total = query.count()
        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(result_items),
            data=result_items,
            total=total,
            total_page=math.ceil(total / limit),
        )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """ 提取（质押、做市）挖矿的收益 """
        user_id = g.user.id
        activity_id = kwargs['activity_id']
        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id
        ).first()
        if not activity or not activity.valid:
            raise InvalidArgument
        if activity.mining_mode not in (MiningActivity.MiningMode.AMM, MiningActivity.MiningMode.PLEDGE):
            raise InvalidArgument
        if not is_mining_join_user(activity, user_id):
            raise InvalidArgument(message="You are not qualified for mining activity")

        return PledgeAmmMiningHelper.withdrawal_user_all_remain_reward(activity, user_id)


@ns.route('/mining/history')
@respond_with_code
class MiningActivityHistoryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            activity_id=fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ （质押、做市）挖矿的操作历史：质押、提取质押、提取收益 """
        activity_id = kwargs['activity_id']
        page, limit = kwargs['page'], kwargs['limit']
        user_id = g.user.id
        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument

        query = MiningUserPledgeAmmOperateHistory.query.filter(
            MiningUserPledgeAmmOperateHistory.mining_activity_id == activity_id,
            MiningUserPledgeAmmOperateHistory.user_id == user_id,
        ).order_by(MiningUserPledgeAmmOperateHistory.id.desc())
        records = query.paginate(page, limit, error_out=False)
        res = []
        for item in records.items:
            item: MiningUserPledgeAmmOperateHistory
            res.append(dict(
                status=item.status.name,
                time=item.created_at,
                operate_type=item.operate_type.name,
                amount=quantize_amount(item.amount, PrecisionEnum.COIN_PLACES),
                asset=item.asset
            ))

        total = query.count()
        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(res),
            data=res,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/mining/pledge')
@respond_with_code
class MiningActivityPledgeResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True),
        amount=PositiveDecimalField(
            places=PrecisionEnum.COIN_PLACES,
            rounding=decimal.ROUND_DOWN,
            required=True,
        )
    ))
    def post(cls, **kwargs):
        """ 质押挖矿-质押操作 """
        activity_id = kwargs['activity_id']
        amount = kwargs['amount']

        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument
        # 活动未开始是可以质押的, 不判断activity.valid
        if activity.active_status not in (MiningActivity.ActiveStatus.STARTED, MiningActivity.ActiveStatus.CREATED):
            raise InvalidArgument

        user_id = g.user.id
        if not is_mining_join_user(activity, user_id):
            # 首次质押自动报名
            is_qualified, _ = get_user_mining_qualify_info(activity, g.user)
            if not is_qualified:
                raise InvalidArgument(message="You are not qualified for mining activity")

            add_mining_join_user(activity, user_id)

        PledgeAmmMiningHelper.add_user_pledge(activity, user_id, amount)
        update_activity_detail_cache_task.delay(activity_id)

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            activity_id=fields.Integer(required=True),
            amount=PositiveDecimalField(
                places=PrecisionEnum.COIN_PLACES,
                rounding=decimal.ROUND_DOWN,
                required=True,
            ),
        )
    )
    def delete(cls, **kwargs):
        """ 质押挖矿-提取质押操作 """
        activity_id = kwargs['activity_id']
        amount = kwargs['amount']

        activity = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument

        user_id = g.user.id
        if not is_mining_join_user(activity, user_id):
            raise InvalidArgument

        PledgeAmmMiningHelper.withdrawal_user_pledge(activity, user_id, amount)
        update_activity_detail_cache_task.delay(activity_id)


class ActivityUserConditionMixin:
    @classmethod
    def _get_trade_before_start_user_list(cls, activity_id: int) -> Set[int]:
        raise NotImplementedError

    @classmethod
    def fmt_used_condition(cls, used_condition: str) -> [ActivityCondition.UsedType]:
        if not used_condition:
            return []
        return [ActivityCondition.UsedType[func_] for func_ in used_condition.split(",")]

    @classmethod
    @cached(60 * 10)
    def _get_user_balance_usd(cls, user_id):
        price_map = PriceManager.assets_to_usd()
        sub_user_ids = [i.user_id for i in SubAccount.query.filter(
            SubAccount.main_user_id == user_id, SubAccount.status == SubAccount.Status.VALID).all()]
        bm = BalanceManager(user_id, sub_user_ids, price_map)
        current_balance_usd = bm.get_current_balance_usd()
        return amount_to_str(current_balance_usd, 2)

    @classmethod
    def __get_all_trade_amount(cls, user_id, st: datetime, et: datetime):
        return get_user_all_trade_amount(user_id, st, et)

    @classmethod
    def __get_spot_trade_amount(cls, user_id, st: datetime, et: datetime):
        et_date = et.date() + timedelta(days=1)
        return get_user_spot_no_strategy_trade_amount(user_id, st.date(), et_date)

    @classmethod
    def __get_perpetual_trade_amount(cls, user_id, st: datetime, et: datetime):
        et_date = et.date() + timedelta(days=1)
        return get_user_perpetual_trade_amount(user_id, st.date(), et_date)

    @classmethod
    def __get_amm_trade_amount(cls, user_id, st: datetime, et: datetime):
        return get_user_amm_trade_amount(user_id, st, et)

    @classmethod
    def __get_exchange_trade_amount(cls, user_id, st: datetime, et: datetime):
        return get_one_user_exchange_data(user_id, st, et)

    @classmethod
    def __get_spot_grid_trade_amount(cls, user_id, st: datetime, et: datetime):
        return get_user_spot_grid_trade_amount(user_id, st, et)

    @classmethod
    def __get_auto_invest_trade_amount(cls, user_id, st: datetime, et: datetime):
        return get_user_auto_invest_trade_amount(user_id, st, et)

    @classmethod
    def get_trade_value(cls, user_id, days, trade_type_range):
        # 取进x天的数据（到今天最新一个小时）
        st = today_datetime() - timedelta(days=days - 1)
        et = now().replace(minute=0, second=0, microsecond=0)
        cache = UserActivityTradeValueHourCache(st, et, trade_type_range)
        if val := cache.read_by_user(user_id):
            return Decimal(val)

        _type = ActivityCondition.TradeType
        func_map = {
            _type.ALL.name: cls.__get_all_trade_amount,
            _type.SPOT.name: cls.__get_spot_trade_amount,
            _type.PERPETUAL.name: cls.__get_perpetual_trade_amount,
            _type.EXCHANGE.name: cls.__get_exchange_trade_amount,
            _type.AMM.name: cls.__get_amm_trade_amount,
            _type.SPOT_GRID.name: cls.__get_spot_grid_trade_amount,
            _type.AUTO_INVEST.name: cls.__get_auto_invest_trade_amount,
        }
        user_trade_value = Decimal()
        for trade_type in trade_type_range:
            if trade_type in func_map:
                user_trade_value += func_map[trade_type](user_id, st, et)
        cache.set_by_user(user_id, user_trade_value)
        return user_trade_value

    @classmethod
    def check_market_maker_value(cls, value) -> bool:
        match value:
            case ActivityCondition.MarketMakerTypeKeys.NOT_LIMITED.value:
                return True
            case ActivityCondition.MarketMakerTypeKeys.NOT_MARKET_MAKER.value:
                return g.user.user_type == User.UserType.NORMAL
            case ActivityCondition.MarketMakerTypeKeys.MARKET_MAKER.value:
                return g.user.user_type in [
                    User.UserType.INTERNAL_MAKER,
                    User.UserType.EXTERNAL_MAKER,
                    User.UserType.EXTERNAL_SPOT_MAKER,
                    User.UserType.EXTERNAL_CONTRACT_MAKER,
                ]
            case ActivityCondition.MarketMakerTypeKeys.SPOT_MARKET_MAKER.value:
                return g.user.user_type in [
                    User.UserType.EXTERNAL_MAKER,
                    User.UserType.EXTERNAL_SPOT_MAKER,
                ]
            case ActivityCondition.MarketMakerTypeKeys.PERPETUAL_MARKET_MAKER.value:
                return g.user.user_type in [
                    User.UserType.EXTERNAL_MAKER,
                    User.UserType.EXTERNAL_CONTRACT_MAKER,
                ]
            case _:
                return False

    @classmethod
    def check_registered_value(cls, user_id: int, registered_condition: str) -> bool:
        user: User = User.query.get(user_id)
        if not user:
            return False
        if not registered_condition:
            return True
        user_registered_at = user.created_at
        json_condition = json_loads(registered_condition)
        threshold_time = json_condition[
            ActivityCondition.RegisteredTimeKey.REGISTERED_VALUE.value]
        option_type = json_condition[
            ActivityCondition.RegisteredTimeKey.REGISTERED_OP_TYPE.value]
        threshold_datetime = timestamp_to_datetime(int(threshold_time) / 1000)
        match option_type:
            case ActivityCondition.RegisteredTimeType.NOT_LIMITED.value:
                return True
            case ActivityCondition.RegisteredTimeType.GREATER.value:
                return user_registered_at > threshold_datetime
            case ActivityCondition.RegisteredTimeType.LESS.value:
                return user_registered_at < threshold_datetime
            case _:
                return False

    @classmethod
    def check_used_func(cls, user_id: int, used_funcs: [ActivityCondition.UsedType]) -> dict[str: bool]:
        """检查用户 是否使用过某个功能(最多选择三个标签)，不考虑状态"""
        mapper = {}
        if not used_funcs:
            return mapper
        for func_type in used_funcs:
            is_used = False
            match func_type:
                case ActivityCondition.UsedType.SPOT:
                    result = ServerClient().user_finished_orders(
                        user_id=user_id,
                        market='',
                        start_time=0,
                        end_time=0,
                        stop_order_id=None,
                        side=0,
                        page=1,
                        limit=1
                    )
                    if len(result) > 0:
                        is_used = True
                case ActivityCondition.UsedType.PERPETUAL:
                    result = PerpetualServerClient().order_finished(
                        user_id=user_id,
                        market='',
                        start_time=0,
                        end_time=0,
                        page=1,
                        limit=1,
                        side=0,
                        stop_order_id=0,
                    )
                    if len(result) > 0:
                        is_used = True
                case ActivityCondition.UsedType.EXCHANGE:
                    if AssetExchangeOrder.query.filter(
                            AssetExchangeOrder.user_id == user_id).first():
                        is_used = True
                case ActivityCondition.UsedType.MARGIN:
                    result = ServerClient().market_user_deals(
                        user_id=user_id,
                        market='',
                        start_time=0,
                        end_time=0,
                        page=1,
                        limit=1,
                        side=0,
                        account_id=ALL_MARGIN_ACCOUNT_ID
                    )
                    if len(result) > 0:
                        is_used = True
                case ActivityCondition.UsedType.FIAT:
                    if FiatOrder.query.filter(FiatOrder.user_id == user_id).first():
                        is_used = True
                case ActivityCondition.UsedType.INVESTMENT:
                    if InvestmentTransferHistory.query.filter(
                            InvestmentTransferHistory.user_id == user_id).first():
                        is_used = True
                case ActivityCondition.UsedType.AMM:
                    if LiquidityHistory.query.filter(LiquidityHistory.user_id == user_id).first():
                        is_used = True
                case ActivityCondition.UsedType.AUTO_INVEST:
                    if AutoInvestPlan.query.filter(AutoInvestPlan.user_id == user_id).first():
                        is_used = True
                case ActivityCondition.UsedType.SPOT_GRID:
                    if UserStrategy.query.filter(UserStrategy.user_id == user_id,
                                                 UserStrategy.type == UserStrategy.Type.SPOT_GRID,
                                                 ).first():
                        is_used = True
                case ActivityCondition.UsedType.PLEDGE:
                    if PledgePosition.query.filter(PledgePosition.user_id == user_id).first():
                        is_used = True
            mapper[func_type.name] = is_used
        return mapper

    @classmethod
    def get_condition_info(cls, user_id, activity_id, conditions_query):

        vip = VipUser.query.filter(
            VipUser.user_id == user_id,
            VipUser.status == VipUser.StatusType.PASS,
        ).first()

        conditions = dict(
            vip=(vip.level if vip else 0) >= int(
                conditions_query[ActivityCondition.ConditionKeys.VIP.name]),
            kyc=g.user.kyc_status == User.KYCStatus.PASSED if bool(
                int(conditions_query[ActivityCondition.ConditionKeys.KYC.name])) else True,
            balance_usd=True,
            trade_usd=True,
            registered=cls.check_registered_value(
                user_id,
                conditions_query.get(ActivityCondition.ConditionKeys.REGISTERED_VALUE.name)
            ),
            used=cls.check_used_func(
                user_id,
                cls.fmt_used_condition(
                    conditions_query.get(ActivityCondition.ConditionKeys.USED_VALUE.name))
            ),
            market_maker=cls.check_market_maker_value(conditions_query.get(ActivityCondition.ConditionKeys.MARKET_MAKER.name))
        )

        balance_condition = json_loads(
            conditions_query[ActivityCondition.ConditionKeys.BALANCE_VALUE.name])
        balance_usd = Decimal(
            balance_condition[ActivityCondition.BalanceValueKey.BALANCE_VALUE.name])
        balance_op_type = balance_condition[
            ActivityCondition.BalanceValueKey.BALANCE_OP_TYPE.name]
        if balance_op_type != ActivityCondition.BalanceValueOperateType.NOT_LIMITED.value:
            current_balance_usd = Decimal(cls._get_user_balance_usd(user_id))
            if balance_op_type == ActivityCondition.BalanceValueOperateType.GREATER.value:
                balance_usd_cond = current_balance_usd >= balance_usd
            else:
                balance_usd_cond = current_balance_usd < balance_usd
            conditions['balance_usd'] = balance_usd_cond
        trade_condition = json_loads(conditions_query[ActivityCondition.ConditionKeys.TRADE_VALUE.name])
        trade_op_type = trade_condition[ActivityCondition.TradeValueKey.TRADE_OP_TYPE.name]
        trade_business_type = trade_condition[ActivityCondition.TradeValueKey.TRADE_BUSINESS_TYPE.name]
        if trade_op_type == ActivityCondition.TradeValueOperateType.NOT_LIMITED.value:
            pass
        elif trade_business_type == ActivityCondition.TradeBusinessTypeKey.REAL_TIME.value:
            value_key = ActivityCondition.TradeValueKey
            days = trade_condition[value_key.TRADE_DAY_RANGE.name]
            trade_type_range = trade_condition.get(value_key.TRADE_TYPE_RANGE.name)
            user_trade_value = Decimal(cls.get_trade_value(user_id, days, trade_type_range))
            trade_usd = Decimal(trade_condition[ActivityCondition.TradeValueKey.TRADE_VALUE.name])
            if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                conditions['trade_usd'] = user_trade_value >= trade_usd
            else:
                conditions['trade_usd'] = user_trade_value < trade_usd
        else:
            if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                conditions['trade_usd'] = user_id in cls._get_trade_before_start_user_list(activity_id)
            else:
                conditions['trade_usd'] = user_id not in cls._get_trade_before_start_user_list(activity_id)

        holding_condition = json_loads(
            conditions_query[ActivityCondition.ConditionKeys.HOLDING.name])
        need_holding = holding_condition[ActivityCondition.HoldingKeys.NEED_HOLDING.name]
        if need_holding == ActivityCondition.NeedHoldingType.NOT_LIMITED.value:
            conditions['holding'] = True
        else:
            asset_holding = holding_condition[
                ActivityCondition.HoldingKeys.ASSET_HOLDING.name]
            history = ServerClient().get_user_balance_history(user_id, asset_holding, limit=1)
            if need_holding == ActivityCondition.NeedHoldingType.HOLD.value:
                conditions['holding'] = True if history else False
            if need_holding == ActivityCondition.NeedHoldingType.NOT_HOLD.value:
                conditions['holding'] = False if history else True

        return conditions

    @classmethod
    def check_condition(cls, user_id, activity_id, conditions):
        for key_ in ActivityCondition.CONDITION_LIST:
            if key_ == ActivityCondition.ConditionKeys.KYC.name:
                user = User.query.get(user_id)
                kyc = (user and user.kyc_status == User.KYCStatus.PASSED) if bool(
                    int(conditions[key_])) else True
                if not kyc:
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            if key_ == ActivityCondition.ConditionKeys.MARKET_MAKER.name:
                if not conditions.get(key_):  # 未配置兼容处理
                    continue
                if not cls.check_market_maker_value(conditions.get(key_)):
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            elif key_ == ActivityCondition.ConditionKeys.VIP.name:
                if int(conditions[key_]) == 0:
                    continue
                vip_data = VipUser.query.filter(
                    VipUser.user_id == user_id,
                    VipUser.status == VipUser.StatusType.PASS,
                ).first()
                vip = (vip_data.level if vip_data else 0) >= int(conditions[key_])
                if not vip:
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            elif key_ == ActivityCondition.ConditionKeys.HOLDING.name:
                holding = True
                holding_condition = json_loads(conditions[key_])
                need_holding = holding_condition[
                    ActivityCondition.HoldingKeys.NEED_HOLDING.name]
                if need_holding != ActivityCondition.NeedHoldingType.NOT_LIMITED.value:
                    asset_holding = holding_condition[
                        ActivityCondition.HoldingKeys.ASSET_HOLDING.name]
                    history = ServerClient().get_user_balance_history(user_id,
                                                                      asset_holding,
                                                                      limit=1)
                    if need_holding == ActivityCondition.NeedHoldingType.HOLD.value:
                        holding = True if history else False
                    if need_holding == ActivityCondition.NeedHoldingType.NOT_HOLD.value:
                        holding = False if history else True
                if not holding:
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            elif key_ == ActivityCondition.ConditionKeys.BALANCE_VALUE.name:
                balance_usd_cond = True
                balance_condition = json_loads(conditions[key_])
                balance_usd = Decimal(
                    balance_condition[ActivityCondition.BalanceValueKey.BALANCE_VALUE.name])
                balance_op_type = balance_condition[
                    ActivityCondition.BalanceValueKey.BALANCE_OP_TYPE.name]
                if balance_op_type != ActivityCondition.BalanceValueOperateType.NOT_LIMITED.value:
                    current_balance_usd = Decimal(
                        cls._get_user_balance_usd(user_id))
                    if balance_op_type == ActivityCondition.BalanceValueOperateType.GREATER.value:
                        balance_usd_cond = current_balance_usd >= balance_usd
                    else:
                        balance_usd_cond = current_balance_usd < balance_usd
                if not balance_usd_cond:
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            elif key_ == ActivityCondition.ConditionKeys.TRADE_VALUE.name:
                value_key = ActivityCondition.TradeValueKey
                trade_usd_cond = True
                trade_condition = json_loads(conditions[key_])
                trade_op_type = trade_condition[
                    value_key.TRADE_OP_TYPE.name]
                trade_business_type = trade_condition[
                    value_key.TRADE_BUSINESS_TYPE.name]
                if trade_op_type == ActivityCondition.TradeValueOperateType.NOT_LIMITED.value:
                    pass
                elif trade_business_type == ActivityCondition.TradeBusinessTypeKey.REAL_TIME.value:
                    value_key = ActivityCondition.TradeValueKey
                    days = trade_condition[value_key.TRADE_DAY_RANGE.name]
                    trade_type_range = trade_condition.get(value_key.TRADE_TYPE_RANGE.name)
                    user_trade_value = Decimal(cls.get_trade_value(user_id, days, trade_type_range))
                    trade_usd = Decimal(trade_condition[ActivityCondition.TradeValueKey.TRADE_VALUE.name])
                    if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                        trade_usd_cond = user_trade_value >= trade_usd
                    else:
                        trade_usd_cond = user_trade_value < trade_usd
                else:
                    if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                        trade_usd_cond = user_id in cls._get_trade_before_start_user_list(
                            activity_id)
                    else:
                        trade_usd_cond = user_id not in cls._get_trade_before_start_user_list(
                            activity_id)
                if not trade_usd_cond:
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))

            elif key_ == ActivityCondition.ConditionKeys.REGISTERED_VALUE.name:
                if key_ in conditions and not cls.check_registered_value(
                        user_id, conditions[key_]):
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))
            elif key_ == ActivityCondition.ConditionKeys.USED_VALUE.name:
                used_mapper = cls.check_used_func(
                    user_id,
                    cls.fmt_used_condition(conditions[key_])
                )
                if key_ in conditions and used_mapper and not all(used_mapper.values()):
                    raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))


class DiscountActivityConditionMixin(ActivityUserConditionMixin):
    @classmethod
    @cached(60 * 10)
    def _get_trade_before_start_user_list(cls, activity_id):
        statistic = DiscountActivityStatistic.query.filter(
            DiscountActivityStatistic.discount_activity_id == activity_id,
            DiscountActivityStatistic.business_type == DiscountActivityStatistic.BusinessType.TRADE_BEFORE_START,
        ).first()
        if not statistic:
            return set()
        return set(statistic.get_user_ids())


class AirdropActivityConditionMixin(ActivityUserConditionMixin):
    @classmethod
    @cached(60 * 10)
    def _get_trade_before_start_user_list(cls, activity_id):
        statistic = AirdropActivityStatistic.query.filter(
            AirdropActivityStatistic.airdrop_activity_id == activity_id,
            AirdropActivityStatistic.business_type == AirdropActivityStatistic.BusinessType.TRADE_BEFORE_START,
        ).first()
        if not statistic:
            return set()
        return set(statistic.get_user_ids())


@ns.route('/airdrop')
@respond_with_code
class AirdropActivityListResource(Resource):

    @classmethod
    def sorter(cls, act):
        now_ = current_timestamp(to_int=True)
        if now_ < act['start_time']:
            return (2, act['end_time'], -act['id'])
        elif act['start_time'] <= now_ < act['end_time']:
            return (0, -act['end_time'], -act['id'])
        else:
            if act['airdrop_mode'] == AirdropActivity.AirdropMode.RANDOM.name and \
                    act['status'] == AirdropActivity.StatusType.ONLINE.name:
                return (1, -act['end_time'], -act['id'])
            else:
                return (3, -act['end_time'], -act['id'])

    @classmethod
    def get_active_status(cls, act):
        now_ = current_timestamp(to_int=True)
        start_time = act['start_time']
        end_time = act['end_time']
        status = act['status']
        airdrop_mode = act['airdrop_mode']
        if now_ < start_time:
            return AirdropActivity.ActiveStatus.CREATED.name
        if now_ < end_time and status == AirdropActivity.StatusType.ONLINE.name:
            return AirdropActivity.ActiveStatus.STARTED.name
        if (end_time <= now_ and status == AirdropActivity.StatusType.ONLINE.name and
                airdrop_mode == AirdropActivity.AirdropMode.RANDOM.name):
            return AirdropActivity.ActiveStatus.PROCESSED.name
        return AirdropActivity.ActiveStatus.FINISHED.name

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=5),
        page=PageField(missing=1),
    ))
    def get(cls, **kwargs):
        """
        空投-活动列表
        """
        page = kwargs['page']
        limit = kwargs['limit']
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AirdropActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        result = cls._get(lang.value)
        data = result[(page - 1) * limit: page * limit]
        total = len(result)
        end = page * limit
        return dict(
            has_next=end < total,
            curr_page=page,
            count=len(data),
            data=data,
            total=total,
            total_page=math.ceil(total / limit),
        )

    @classmethod
    @mem_cached(120)
    def _get(cls, lang):
        if data := AirdropActivityCache(lang).read():
            res = json_loads(data)
            for row in res:
                row['active_status'] = cls.get_active_status(row)
            res.sort(key=cls.sorter)
            return res
        return []


@ns.route('/airdrop/<int:activity_id>')
@respond_with_code
class AirdropActivityDetailResource(Resource):

    @classmethod
    def get_assets_data(cls, str_assets: str):
        if not str_assets:
            return []
        assets = str_assets.split(",")
        result = []
        change_rate_dict = dict(AssetRecentChangeRateCache().hmget_with_keys(assets))
        for asset in assets:
            result.append({
                "asset": asset,
                "change_rate": change_rate_dict.get(asset, "0"),
            })
        return result

    @classmethod
    def _get_received_count(cls, activity_id: int) -> int:
        received_count = int(AirdropActivityLotteryCache().hget(str(activity_id)) or 0)
        if received_count:
            return received_count
        return AirdropActivityLotteryHistory.query.filter(
            AirdropActivityLotteryHistory.airdrop_activity_id == activity_id
        ).with_entities(
            func.count(AirdropActivityLotteryHistory.id)
        ).scalar() or 0

    @classmethod
    def _format_equity_rewards(cls, equity_rewards: list[dict]) -> list[dict]:
        result = []
        for equity_reward in equity_rewards:
            new_equity_reward = copy.deepcopy(equity_reward)
            new_equity_reward["type"] = _(EquityType(equity_reward["type"]).value)
            result.append(new_equity_reward)
        return result

    @classmethod
    def get(cls, activity_id):
        """ 空投活动详情 """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AirdropActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        detail = AirdropActivityDetailCache.get(activity_id, lang)
        if not detail:
            raise InvalidArgument

        res = json_loads(detail)
        if res['airdrop_mode'] == AirdropActivity.AirdropMode.REALTIME.name:
            cur_count = min(int(AirdropActivityReceivedCountCache().hget(str(activity_id)) or 0), res['total_count'])
            res['received_amount'] = cur_count * Decimal(res['amount'])
        else:
            res['received_count'] = cls._get_received_count(activity_id)
        res['active_status'] = AirdropActivityListResource.get_active_status(res)
        res['assets'] = cls.get_assets_data(res['assets'])
        res['current_timestamp'] = current_timestamp(to_int=True)
        res['amount'] = AirdropActivityCache.get_amount_by_coupon_rewards(
            res['coupon_rewards'], res['amount']
        )
        res['equity_rewards'] = cls._format_equity_rewards(res['equity_rewards'])

        return res

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, activity_id):
        """ 空投活动-用户报名 """
        user_id = g.user.id
        activity = AirdropActivity.query.filter(
            AirdropActivity.status == AirdropActivity.StatusType.ONLINE,
            AirdropActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument
        if not activity.active_status == AirdropActivity.ActiveStatus.STARTED:
            raise InvalidArgument(message=_("你未满足条件或不在活动时间内，请刷新后重试"))

        if activity.airdrop_mode == AirdropActivity.AirdropMode.REALTIME:
            if int(AirdropActivityReceivedCountCache().hget(str(activity_id)) or 0) >= activity.total_count:
                raise InvalidArgument(message=_('领取失败，数量已被瓜分完'))

        row = AirdropActivityUserRecord.query.filter(
            AirdropActivityUserRecord.airdrop_activity_id == activity_id,
            AirdropActivityUserRecord.user_id == user_id,
        ).first()
        if row:
            raise InvalidArgument(message=_('你已经领取过'))

        is_answer = AirdropActivityUserHasAnsweredCache(activity_id).has_user(user_id)
        if not is_answer:
            raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))

        with CacheLock(LockKeys.airdrop_activity(activity_id, user_id), wait=False):
            db.session.rollback()
            if activity.airdrop_mode == AirdropActivity.AirdropMode.REALTIME:
                return cls.apply_realtime(user_id, activity)
            else:
                return cls.apply_random(user_id, activity)

    @classmethod
    def apply_realtime(cls, user_id: int, activity: AirdropActivity) -> dict:
        _check = UserVisitPermissionCache().check_user_permission(
            user_id,
            [UserVisitPermissionCache.FORBIDDEN_VALUE]
        )
        if _check:
            raise InvalidArgument(message=_("不符合领奖要求，领取失败"))
        if UserRepository.is_abnormal_user(user_id):
            raise ActivityAbnormalUserError

        activity_id = activity.id
        cache = AirdropActivityReceivedCountCache()
        if cache.hincrby(activity_id, 1) > activity.total_count:
            raise InvalidArgument(message=_('领取失败，数量已被瓜分完'))
        reword_count = AirdropActivityUserRecord.query.filter(
            AirdropActivityUserRecord.airdrop_activity_id == activity_id,
        ).with_entities(
            AirdropActivityUserRecord.airdrop_activity_id,
            func.count(AirdropActivityUserRecord.user_id).label('count'),
        ).first().count
        if reword_count > activity.total_count:  # 避免缓存丢失
            raise InvalidArgument

        dt = now()
        lock_time = timedelta(days=activity.lock_day)
        user_record = AirdropActivityUserRecord(
            user_id=user_id,
            airdrop_activity_id=activity_id,
            airdrop_mode=AirdropActivity.AirdropMode.REALTIME,
            status=AirdropActivityUserRecord.Status.CREATED,
        )
        db.session_add_and_flush(user_record)

        rewards = AirdropActivityReward.query.filter(
            AirdropActivityReward.airdrop_activity_id == activity_id
        ).order_by(AirdropActivityReward.id.asc()).all()
        coupon_rewards = []
        asset_rewards = []
        equity_rewards = []
        for row in rewards:
            if row.type == AirdropActivityReward.Type.COUPON:
                coupon_rewards.append(row)
            elif row.type == AirdropActivityReward.Type.ASSET:
                asset_rewards.append(row)
            elif row.type == AirdropActivityReward.Type.EQUITY:
                equity_rewards.append(row)

        apply_id_reward_row_map = {i.coupon_apply_id: i for i in coupon_rewards}
        coupon_apply_ids = list(apply_id_reward_row_map)
        receive_result = send_airdrop_coupons(user_id, coupon_apply_ids)
        new_rows = []
        all_receive_success, all_receive_failed = True, True
        for item in receive_result:
            row = apply_id_reward_row_map[item['apply_id']]
            if item['is_success']:
                all_receive_failed = False
                r_status = AirdropActivityRewardHistory.Status.FINISHED
            else:
                all_receive_success = False
                r_status = AirdropActivityRewardHistory.Status.FAILED
            new_rows.append(AirdropActivityRewardHistory(
                user_id=user_id,
                airdrop_activity_id=activity_id,
                airdrop_reward_id=row.id,
                user_record_id=user_record.id,
                amount=Decimal(1),
                coupon_apply_id=item['apply_id'],
                status=r_status,
                reason=item.get('message')
            ))

        for row in asset_rewards:
            all_receive_failed = False
            new_rows.append(AirdropActivityRewardHistory(
                user_id=user_id,
                airdrop_activity_id=activity_id,
                airdrop_reward_id=row.id,
                user_record_id=user_record.id,
                asset=row.asset,
                amount=row.amount,
                unlocked_at=dt + lock_time,
                status=AirdropActivityRewardHistory.Status.FINISHED
            ))
            new_rows.append(GiftHistory(
                user_id=user_id,
                activity_id=activity.activity_id,
                asset=row.asset,
                amount=row.amount,
                lock_time=activity.lock_day * 86400,
                remark='airdrop_gift',
                status=GiftHistory.Status.CREATED
            ))
        db.session.add_all(new_rows)
        db.session.flush()

        eq_reward_params = []
        for row in equity_rewards:
            all_receive_failed = False
            eq_reward_his = AirdropActivityRewardHistory(
                user_id=user_id,
                airdrop_activity_id=activity_id,
                airdrop_reward_id=row.id,
                user_record_id=user_record.id,
                amount=Decimal(1),
                status=AirdropActivityRewardHistory.Status.FINISHED
            )
            db.session.add(eq_reward_his)
            eq_reward_params.append((row, eq_reward_his))
        if eq_reward_params:
            db.session.flush()
            batch_send_airdrop_equity_rewards(eq_reward_params)

        user_record_status = AirdropActivityUserRecord.Status.PARTIAL
        if all_receive_success:
            user_record_status = AirdropActivityUserRecord.Status.SUCCEED
        if all_receive_failed:
            user_record_status = AirdropActivityUserRecord.Status.FAILED
        user_record.status = user_record_status
        db.session.commit()

        update_gift_history_task.delay(
            activity.activity_id,
            BalanceBusiness.GIFT.value,
            pay_from_admin_user_id=AIRDROP_ADMIN_USER_ID,
            wait=False
        )
        VipUpgradeCouponStatus.activated_update_task(user_id)
        return dict(unlock_time=dt + lock_time)

    @classmethod
    def apply_random(cls, user_id: int, activity: AirdropActivity) -> dict:
        activity_id = activity.id
        user_record = AirdropActivityUserRecord(
            user_id=user_id,
            airdrop_activity_id=activity_id,
            airdrop_mode=AirdropActivity.AirdropMode.RANDOM,
            status=AirdropActivityUserRecord.Status.CREATED
        )
        db.session_add_and_flush(user_record)
        lottery_number = AirdropActivityLotteryCache().hincrby(activity_id, 1)
        lottery_history = AirdropActivityLotteryHistory(
            user_id=user_id,
            airdrop_activity_id=activity_id,
            user_record_id=user_record.id,
            lottery_number=lottery_number
        )
        db.session_add_and_flush(lottery_history)

        rewards = AirdropActivityReward.query.filter(
            AirdropActivityReward.airdrop_activity_id == activity_id
        ).order_by(AirdropActivityReward.id.asc()).all()
        new_rows = []
        for r in rewards:
            new_rows.append(AirdropActivityRewardHistory(
                user_id=user_id,
                airdrop_activity_id=activity_id,
                airdrop_reward_id=r.id,
                user_record_id=user_record.id,
                airdrop_mode=AirdropActivityRewardHistory.AirdropMode.RANDOM,
                status=AirdropActivityRewardHistory.Status.CREATED,
                asset=r.asset,
                amount=r.amount,
                coupon_apply_id=r.coupon_apply_id,
            ))
        db.session.add_all(new_rows)
        db.session.commit()
        return dict(
            lottery_number=lottery_history.lottery_number_zfill,
            airdrop_mode=activity.airdrop_mode.name,
            created_at=lottery_history.created_at,
        )


@ns.route("/airdrop/answer/<int:activity_id>/single")
@respond_with_code
class AirdropActivityAnswerSingleResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        number=fields.Integer(required=True),
        answer=fields.String(required=True)
    ))
    def post(cls, activity_id, **kwargs):
        number, user_answer = kwargs['number'], kwargs['answer']
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AirdropActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        question_json = AirdropActivityQuestionBankCache.get(activity_id, lang)
        if not question_json:
            raise InvalidArgument
        questions = json_loads(question_json)
        answers = {
            v['number']: v
            for v in questions
        }
        correct_answer = answers[number]['answer']
        return dict(
            number=number,
            correct_answer=correct_answer,
            is_pass=bool(correct_answer == user_answer),
            answer_analysis=answers[number]['answer_analysis']
        )


@ns.route('/airdrop/answer/<int:activity_id>')
@respond_with_code
class AirdropActivityAnswerResource(AirdropActivityConditionMixin, Resource):

    @classmethod
    def get_questions(cls, activity_id, number):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AirdropActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        question_json = AirdropActivityQuestionBankCache.get(activity_id, lang)
        if not question_json:
            raise InvalidArgument

        questions = json_loads(question_json)
        all_numbers = set([v['number'] for v in questions])
        question_numbers = random.sample(list(all_numbers), number)
        result = list(
            filter(lambda x: x['number'] in question_numbers, questions))
        return [
            dict(
                number=v['number'],
                question=v['question'],
                options=json_loads(v['options'])
            )
            for v in result
        ]

    @classmethod
    def get(cls, activity_id):
        """ 空投活动答题信息 """
        if (user:= get_request_user()):
            if UserRepository.is_abnormal_user(user.id):
                raise ActivityAbnormalUserError(message=_("你的账户暂被限制参与活动，如有疑问请联系客服。"))
        return cls.get_questions(activity_id, 3)

    class DetailSchema(Schema):
        number = fields.Integer(required=True)
        answer = fields.String(required=True)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        answers=fields.Nested(DetailSchema, many=True, required=True)
    ))
    def post(cls, activity_id, **kwargs):
        """ 空投活动回答 """
        user_id = g.user.id
        result = cls.check_answers(kwargs['answers'], activity_id, 3)
        if result['pass'] is False:
            # 答题未通过
            raise AnswerNotCorrect(data=result)

        conditions = {
            i.key: i.value for i in AirdropActivityCondition.query.filter(
                AirdropActivityCondition.airdrop_activity_id == activity_id).all()
        }
        cls.check_condition(user_id, activity_id, conditions)

        AirdropActivityUserHasAnsweredCache(activity_id).add_user(user_id)

    @classmethod
    def check_answers(cls, result, activity_id, number):

        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AirdropActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        question_json = AirdropActivityQuestionBankCache.get(activity_id, lang)
        if not question_json:
            raise InvalidArgument

        questions = json_loads(question_json)
        numbers = set([v['number'] for v in result])
        if len(numbers) < number:
            raise InvalidArgument
        answers = {
            v['number']: v['answer']
            for v in questions
        }
        check_result = {'pass': True, "failed": []}
        for r in result:
            if answers[r['number']] != r['answer']:
                check_result['failed'].append({r['number']: answers[r['number']]})
                check_result['pass'] = False
        return check_result


@ns.route('/airdrop/user-info')
@respond_with_code
class AirdropActivityUserInfoResource(AirdropActivityConditionMixin, Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """ 用户的空投活动的信息 """
        activity_id = kwargs['activity_id']
        user_id = g.user.id
        activity = AirdropActivity.query.get(activity_id)
        if not activity:
            raise InvalidArgument
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        cache_detail = AirdropActivityDetailCache.get(activity_id, lang)
        if not cache_detail:
            raise InvalidArgument
        detail = json_loads(cache_detail)
        cache_activity_status = detail['status']

        user_record = AirdropActivityUserRecord.query.filter(
            AirdropActivityUserRecord.airdrop_activity_id == activity_id,
            AirdropActivityUserRecord.user_id == user_id,
        ).first()

        result = dict(
            airdrop_mode=activity.airdrop_mode.name,
            joined=True if user_record else False,
            is_answer=AirdropActivityUserHasAnsweredCache(
                activity_id).has_user(user_id),
            unlocked_at=0,
            lottery_number='',
            lottery_time=activity.end_time + timedelta(days=AirdropActivity.LOTTERY_DAY),
            amount=activity.amount,
            asset=activity.asset,
            status='',
            state='',
        )
        if activity.airdrop_mode == AirdropActivity.AirdropMode.RANDOM:
            lottery = AirdropActivityLotteryHistory.query.filter(
                AirdropActivityLotteryHistory.airdrop_activity_id == activity_id,
                AirdropActivityLotteryHistory.user_id == user_id,
            ).first()
            if lottery:
                result['lottery_number'] = lottery.lottery_number_zfill
                result['state'] = lottery.state.name
                result['status'] = AirdropActivityLotteryHistory.StatusType.CREATED.name
                if cache_activity_status == AirdropActivity.StatusType.FINISHED.name:
                    result['status'] = lottery.status.name

        reward_histories = AirdropActivityRewardHistory.query.filter(
            AirdropActivityRewardHistory.airdrop_activity_id == activity_id,
            AirdropActivityRewardHistory.user_id == user_id,
            AirdropActivityRewardHistory.status.in_([
                AirdropActivityRewardHistory.Status.FINISHED,
                AirdropActivityRewardHistory.Status.FAILED
            ])
        ).all()
        coupon_apply_ids = {row.coupon_apply_id for row in reward_histories if row.coupon_apply_id}
        airdrop_reward_ids = {row.airdrop_reward_id for row in reward_histories}
        airdrop_reward_equity_mapper = get_airdrop_equity_details(airdrop_reward_ids)
        coupon_mapping = get_airdrop_coupon_details(coupon_apply_ids)
        asset_rewards, coupon_rewards, equity_rewards = [], [], []
        unlocked_at = 0
        for r in reward_histories:
            if r.asset:
                asset_rewards.append(dict(asset=r.asset, amount=r.amount))
                unlocked_at = r.unlocked_at
            elif r.coupon_apply_id:
                coupon_detail = coupon_mapping[r.coupon_apply_id]
                coupon_reward = {
                    'status': r.status.name,
                    **coupon_detail
                }
                coupon_rewards.append(coupon_reward)
            if equity_detail := airdrop_reward_equity_mapper.get(r.airdrop_reward_id):
                equity_rewards.append(equity_detail)

        result['asset_rewards'] = asset_rewards
        result['coupon_rewards'] = coupon_rewards
        result['equity_rewards'] = equity_rewards
        result['unlocked_at'] = unlocked_at

        conditions_query = {
            i.key: i.value for i in AirdropActivityCondition.query.filter(
                AirdropActivityCondition.airdrop_activity_id == activity_id
            ).all()
        }
        conditions = cls.get_condition_info(user_id, activity_id, conditions_query)
        result['conditions'] = conditions

        return result


@ns.route('/airdrop/reword')
@respond_with_code
class AirdropActivityRewordResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        airdrop_mode=EnumField(AirdropActivity.AirdropMode, allow_none=True),
        page=fields.Integer(required=True, default=1),
        limit=fields.Integer(required=True, default=10),
    ))
    def get(cls, **kwargs):
        """ 用户的空投参与记录 """
        user_id = g.user.id
        if kwargs.get('airdrop_mode'):
            airdrop_mode = kwargs['airdrop_mode']
        else:
            if get_request_platform().is_mobile():
                # app同时展示2种空投参与记录
                airdrop_mode = None
            else:
                airdrop_mode = AirdropActivity.AirdropMode.RANDOM
        page = kwargs['page']
        limit = kwargs['limit']

        q = AirdropActivityUserRecord.query.filter(
            AirdropActivityUserRecord.user_id == user_id,
        ).order_by(
            AirdropActivityUserRecord.id.desc()
        )
        if airdrop_mode:
            q = q.filter(AirdropActivityUserRecord.airdrop_mode == airdrop_mode)
        paginate = q.paginate(page, limit, error_out=False)
        items = list(paginate.items)
        if not items:
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                total=0,
                total_page=1,
            )

        activity_ids = [i.airdrop_activity_id for i in items]
        activities = AirdropActivity.query.filter(AirdropActivity.id.in_(activity_ids)).all()
        activity_data_map = {i.id: i for i in activities}
        lang = get_request_language()
        lang = lang if lang in [i for i in AirdropActivityDetail.AVAILABLE_LANGS] else Language.EN_US
        details = AirdropActivityDetail.query.filter(
            AirdropActivityDetail.airdrop_activity_id.in_(activity_ids),
            AirdropActivityDetail.lang == lang
        ).all()
        detail_map = {i.airdrop_activity_id: i for i in details}
        lottery_data_map = {}
        random_activity_ids = [i.airdrop_activity_id for i in items if i.airdrop_mode == AirdropActivity.AirdropMode.RANDOM]
        if random_activity_ids:
            lottery_histories = AirdropActivityLotteryHistory.query.filter(
                AirdropActivityLotteryHistory.user_id == user_id,
                AirdropActivityLotteryHistory.airdrop_activity_id.in_(random_activity_ids),
            ).all()
            lottery_data_map = {i.airdrop_activity_id: i for i in lottery_histories}

        asset_list = {i.asset for i in activities}
        coin_info_map = {i.code: i for i in CoinInformation.query.filter(
            CoinInformation.code.in_(asset_list)
        )}

        records = []
        for item in items:
            tmp = item.to_dict()
            air_id = tmp['airdrop_activity_id']
            activity = activity_data_map[air_id]
            activity_detail = detail_map[air_id]
            tmp['status'] = tmp['status'].name
            tmp['airdrop_mode'] = tmp['airdrop_mode'].name
            tmp['activity_status'] = activity.status.name
            tmp['label_type'] = activity.label_type.name
            tmp['asset'] = activity.asset
            tmp['name'] = coin_info_map[activity.asset].name
            tmp['title'] = activity_detail.title if activity_detail else ''
            tmp['lottery_status'] = lottery_data_map[air_id].status.name if lottery_data_map.get(air_id) else ''
            asset_rewards, coupon_rewards, equity_rewards = get_airdrop_activity_rewards(air_id, lang)
            tmp['asset_rewards'] = asset_rewards
            tmp['coupon_rewards'] = coupon_rewards
            tmp['equity_rewards'] = equity_rewards
            records.append(tmp)

        return dict(
            has_next=paginate.has_next,
            curr_page=paginate.page,
            count=len(records),
            data=records,
            total=paginate.total,
            total_page=paginate.pages,
        )


@ns.route('/dibs')
@respond_with_code
class DibsActivityListResource(Resource):

    @classmethod
    def sorter(cls, act):
        now_ = current_timestamp(to_int=True)
        if now_ < act['start_time']:
            return (2, -act['end_time'], -act['id'])
        elif act['start_time'] <= now_ < act['end_time']:
            return (0, -act['end_time'], -act['id'])
        else:
            if act['status'] == DiscountActivity.StatusType.ONLINE.name:
                return (1, -act['end_time'], -act['id'])
            else:
                return (3, -act['end_time'], -act['id'])

    @classmethod
    def get_active_status(cls, act):
        now_ = current_timestamp(to_int=True)
        start_time = act['start_time']
        end_time = act['end_time']
        status = act['status']
        if now_ < start_time:
            return DiscountActivity.ActiveStatus.CREATED.name
        if now_ < end_time and status == DiscountActivity.StatusType.ONLINE.name:
            return DiscountActivity.ActiveStatus.STARTED.name
        if end_time <= now_ and status == DiscountActivity.StatusType.ONLINE.name:
            return DiscountActivity.ActiveStatus.PROCESSED.name
        return DiscountActivity.ActiveStatus.FINISHED.name

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=5),
        page=PageField(missing=1),
    ))
    def get(cls, **kwargs):
        """
        Dibs-活动列表
        """
        page = kwargs['page']
        limit = kwargs['limit']
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in DiscountActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        result = cls._get(lang.value)
        data = result[(page - 1) * limit: page * limit]
        return dict(
            data=data,
            count=len(data),
            total=len(result),
        )

    @classmethod
    def _get(cls, lang):
        if data := DiscountActivityCache(lang).read():
            res = json_loads(data)
            for row in res:
                row['active_status'] = cls.get_active_status(row)
            res.sort(key=cls.sorter)
            return res
        return []


@ns.route('/dibs/<int:activity_id>')
@respond_with_code
class DibsActivityDetailResource(DiscountActivityConditionMixin, Resource):

    @classmethod
    def _get_received_count(cls, activity_id: int) -> int:
        received_count = int(DiscountActivityLotteryCache().hget(str(activity_id)) or 0)
        if received_count:
            return received_count
        return DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.discount_activity_id == activity_id
        ).with_entities(
            func.count(DiscountActivityLotteryHistory.id)
        ).scalar() or 0

    @classmethod
    def get(cls, activity_id):
        """ Dibs活动详情 """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in DiscountActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        detail = DiscountActivityDetailCache.get(activity_id, lang)
        if not detail:
            raise InvalidArgument
        res = json_loads(detail)
        res['active_status'] = DibsActivityListResource.get_active_status(res)
        res['received_count'] = cls._get_received_count(activity_id)
        res['current_timestamp'] = current_timestamp(to_int=True)
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, activity_id):
        """ Dibs活动-用户报名 """
        user_id = g.user.id
        activity = DiscountActivity.query.filter(
            DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
            DiscountActivity.id == activity_id,
        ).first()
        if not activity:
            raise RecordNotFound
        if not activity.active_status == DiscountActivity.ActiveStatus.STARTED:
            raise InvalidArgument(message=_("你未满足条件或不在活动时间内，请刷新后重试"))
        if not activity.discount_price:
            raise InvalidArgument(message='price error')
        row = DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.discount_activity_id == activity_id,
            DiscountActivityLotteryHistory.user_id == user_id,
        ).first()
        if row:
            raise InvalidArgument(message=_('你已领取过抽签号'))
        pay_asset = DiscountActivity.PAY_ASSET
        balance = ServerClient().get_user_balances(user_id, pay_asset)[pay_asset]
        if balance["available"] < activity.pay_amount:
            raise InsufficientBalance
        conditions = {
            i.key: i.value for i in DiscountActivityCondition.query.filter(
                DiscountActivityCondition.discount_activity_id == activity_id).all()
        }
        cls.check_condition(user_id, activity_id, conditions)
        with CacheLock(LockKeys.discount_activity(activity_id, user_id), wait=False):
            db.session.rollback()
            row = DiscountActivityLotteryHistory.query.filter(
                DiscountActivityLotteryHistory.discount_activity_id == activity_id,
                DiscountActivityLotteryHistory.user_id == user_id,
            ).first()
            if row:
                raise InvalidArgument(message=_('你已领取过抽签号'))

            order = DiscountActivityOrder.get_or_create(
                discount_activity_id=activity.id, user_id=user_id)
            db.session.add(order)
            db.session.commit()
            lottery_number = process_discount_activity_order(order.id)
            return dict(
                lottery_number=lottery_number,
            )


@ns.route('/dibs/user-info')
@respond_with_code
class DibsActivityUserInfoResource(DiscountActivityConditionMixin, Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """ 用户的Dibs活动的信息 """
        activity_id = kwargs['activity_id']
        user_id = g.user.id
        activity = DiscountActivity.query.get(activity_id)
        if not activity:
            raise RecordNotFound
        lottery = DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.discount_activity_id == activity_id,
            DiscountActivityLotteryHistory.user_id == user_id,
        ).first()
        reword = dict()
        if lottery:
            reword['lottery_number'] = lottery.lottery_number_zfill
            reword['state'] = lottery.state
            # 活动没完成发奖前展示默认字段
            reword['status'] = DiscountActivityLotteryHistory.StatusType.CREATED
            if activity.status == DiscountActivity.StatusType.FINISHED:
                reword['status'] = lottery.status
        result = dict(
            joined=True if reword else False,
            lottery_number=reword.get('lottery_number', ''),
            lottery_time=activity.end_time + timedelta(days=AirdropActivity.LOTTERY_DAY),
            amount=activity.amount,
            asset=activity.asset,
            status=reword['status'].name if 'status' in reword else '',
            state=reword['state'].name if 'state' in reword else '',
        )
        conditions_query = {
            i.key: i.value for i in DiscountActivityCondition.query.filter(
                DiscountActivityCondition.discount_activity_id == activity_id
            ).all()
        }
        conditions = cls.get_condition_info(user_id, activity_id, conditions_query)
        result['conditions'] = conditions
        return result


@ns.route('/dibs/record')
@respond_with_code
class DibsActivityRewordResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=fields.Integer(required=True, default=1),
        limit=fields.Integer(required=True, default=10),
    ))
    def get(cls, **kwargs):
        """ 用户的Dibs参与记录 """
        user_id = g.user.id
        page = kwargs['page']
        limit = kwargs['limit']
        records = []
        paginate = DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.user_id == user_id
        ).order_by(
            DiscountActivityLotteryHistory.id.desc()
        ).paginate(page, limit, error_out=False)
        items = list(paginate.items)
        activity_ids = [i.discount_activity_id for i in items]
        activity_data_map = {i.id: i for i in DiscountActivity.query.filter(
            DiscountActivity.id.in_(activity_ids)).all()}
        for item in items:
            tmp = dict()
            air_id = item.discount_activity_id
            if activity_data_map[air_id].status == DiscountActivity.StatusType.FINISHED:
                actual_status = item.actual_status.name
            else:
                actual_status = DiscountActivityLotteryHistory.StatusType.CREATED.name
            tmp['asset'] = activity_data_map[air_id].asset
            tmp['pay_asset'] = DiscountActivity.PAY_ASSET
            tmp['pay_amount'] = activity_data_map[air_id].pay_amount
            tmp['amount'] = activity_data_map[air_id].amount
            tmp['actual_status'] = actual_status
            tmp['status'] = item.status.name
            tmp['state'] = item.state.name
            tmp['created_at'] = item.created_at
            tmp['lottery_number'] = item.lottery_number_zfill
            tmp['id'] = item.id
            tmp['discount_activity_id'] = item.discount_activity_id
            tmp['activity_status'] = activity_data_map[air_id].status.name
            records.append(tmp)
        return dict(data=records, total=paginate.total)


@ns.route('/ieo')
@respond_with_code
class IeoActivityListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=5),
        page=PageField(missing=1),
    ))
    def get(cls, **kwargs):
        """
        投资-活动列表
        """
        page = kwargs['page']
        limit = kwargs['limit']
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in IeoActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        result = cls._get(lang.value)
        for act in result:
            _act = IeoActivity(
                start_time=timestamp_to_datetime(act['start_time']),
                end_time=timestamp_to_datetime(act['end_time']),
                status=IeoActivity.StatusType.__members__[act['status']],
            )
            if act['active_status'] != _act.active_status.name:
                update_ieo_activity_schedule.delay()
                break

        data = result[(page - 1) * limit: page * limit]

        return dict(
            data=data,
            count=len(data),
            total=len(result),
        )

    @classmethod
    def _get(cls, lang):
        if data := IeoActivityCache(lang).read():
            return json_loads(data)
        return []


@ns.route('/ieo/<int:activity_id>')
@respond_with_code
class IeoActivityDetailResource(Resource, ActivityUserConditionMixin):

    @classmethod
    def get(cls, activity_id):
        """ 投资活动详情 """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in IeoActivityDetail.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        detail = IeoActivityDetailCache.get(activity_id, lang)
        if not detail:
            raise InvalidArgument
        res = json_loads(detail)
        ieo_white_list = config.get('IEO_WHITE_LIST', [])
        user = get_request_user()
        if user and user.id in ieo_white_list:
            # 白名单申购次数为1000000
            res['max_subscribe_count'] = 1000000
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        subscribe_count=fields.Integer(required=True),
    ))
    def post(cls, activity_id, **kwargs):
        """ 投资活动-用户申购 """
        user_id = g.user.id
        subscribe_count = kwargs['subscribe_count']
        activity = IeoActivity.query.filter(
            IeoActivity.status == IeoActivity.StatusType.ONLINE,
            IeoActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument
        if not activity.active_status == IeoActivity.ActiveStatus.STARTED:
            raise InvalidArgument(message=_("很遗憾！申购已结束"))

        conditions = {
            i.key: i.value for i in IeoActivityCondition.query.filter(
                IeoActivityCondition.ieo_activity_id == activity_id).all()
        }
        ieo_white_list = config.get('IEO_WHITE_LIST', [])
        if user_id not in ieo_white_list:
            cls.check_condition(user_id, activity_id, conditions)
        white_list_query = ActivityWhiteList.query.filter(
            ActivityWhiteList.activity_id == activity.activity_id,
            ActivityWhiteList.status == ActivityWhiteList.Status.PASSED,
        ).all()
        white_list = {i.user_id for i in white_list_query}
        white_list.update(ieo_white_list)
        if g.user.kyc_country in json_loads(activity.black_country_list):
            if user_id not in white_list:
                raise InvalidArgument(message=_("你的KYC国家不支持申购"))

        if not IeoActivityUserSignedContractCache(activity_id).has_user(user_id):
            raise InvalidArgument(message=_("你未满足条件或者未到活动时间"))

        pay_asset = activity.pay_asset
        total_pay_amount = subscribe_count * activity.total_pay_amount
        pledge_asset = activity.pledge_asset
        total_pledge_amount = subscribe_count * activity.pledge_amount

        with CacheLock(LockKeys.ieo_activity(activity_id, user_id), wait=False):
            db.session.rollback()
            # 是否超过最大申购数
            order_list = IeoActivityOrder.query.filter(
                IeoActivityOrder.ieo_activity_id == activity_id,
                IeoActivityOrder.user_id == user_id,
                IeoActivityOrder.status != IeoActivityOrder.StatusType.CANCELLED,
            ).all()
            has_subscribe_count = sum([i.subscribe_count for i in order_list])
            if user_id not in ieo_white_list:
                if has_subscribe_count + subscribe_count > activity.max_subscribe_count:
                    raise InvalidArgument(message=_('累计申购超出最大上限'))
            client = ServerClient(current_app.logger)
            balances = client.get_user_balances(user_id)
            if balances[pay_asset]['available'] < total_pay_amount:
                raise InsufficientBalance
            if balances[pledge_asset]['available'] < total_pledge_amount:
                raise InsufficientBalance
            order = IeoActivityOrder(ieo_activity_id=activity.id, user_id=user_id, subscribe_count=subscribe_count)
            db.session_add_and_commit(order)
            process_ieo_activity_order(order.id)
        update_ieo_activity_schedule.delay()
        return order.id


@ns.route('/ieo/orders/<int:activity_id>/<int:order_id>')
@respond_with_code
class IeoActivityOrdersDetailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, activity_id, order_id):
        """ 投资活动详情 """
        user_id = g.user.id
        activity = IeoActivity.query.filter(
            IeoActivity.id == activity_id,
        ).first()

        if not activity:
            raise InvalidArgument

        asset_info = IeoAssetInformation.query.filter(
            IeoAssetInformation.ieo_activity_id == activity_id,
        ).first()

        order = IeoActivityOrder.query.filter(
            IeoActivityOrder.id == order_id,
            IeoActivityOrder.user_id == user_id,
            IeoActivityOrder.ieo_activity_id == activity_id,
        ).first()

        if not order:
            raise InvalidArgument

        lottery_list = IeoActivityLotteryHistory.query.filter(
            IeoActivityLotteryHistory.order_id == order_id
        ).all()

        result = dict(
            id=order.id,
            ieo_activity_id=order.ieo_activity_id,
            subscribe_asset=activity.subscribe_asset,
            subscribe_amount=activity.subscribe_amount * order.subscribe_count,
            subscribe_count=order.subscribe_count,
            pay_asset=activity.pay_asset,
            pay_amount=activity.total_pay_amount * order.subscribe_count,
            pledge_asset=activity.pledge_asset,
            pledge_amount=activity.pledge_amount * order.subscribe_count,
            activity_name=activity.name,
            rule_url=asset_info.rule_url,
            status=order.get_order_status(activity.end_time),
            unlock_at=order.unlocked_at,
            created_at=order.created_at,
            lottery_count=order.lottery_count,
            real_pay_amount=activity.total_pay_amount * order.lottery_count,
            lottery_list=[i.lottery_number_zfill for i in lottery_list],
            lottery_success_list=[i.lottery_number_zfill for i in lottery_list
                                  if i.status == IeoActivityLotteryHistory.StatusType.SUCCEED],
        )

        return result

    @classmethod
    @require_login(allow_sub_account=False)
    def delete(cls, activity_id, order_id):
        """ 投资活动详情 """
        user_id = g.user.id
        activity = IeoActivity.query.filter(
            IeoActivity.status == IeoActivity.StatusType.ONLINE,
            IeoActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument(message='activity not exist')

        if not activity.is_active:
            raise InvalidArgument(message=_("申购已结束，不允许撤销"))

        with CacheLock(LockKeys.ieo_activity(activity_id, user_id), wait=False):
            db.session.rollback()
            cancel_order_list = IeoActivityOrder.query.filter(
                IeoActivityOrder.user_id == user_id,
                IeoActivityOrder.ieo_activity_id == activity_id,
                IeoActivityOrder.status.in_(
                    [IeoActivityOrder.StatusType.CANCELLED,
                     IeoActivityOrder.StatusType.CANCELLING])
            ).all()
            if len(cancel_order_list) >= IeoActivity.MAX_CANCEL_COUNT:
                raise InvalidArgument(message=_("撤销失败，已达最大撤销上限"))
            order = IeoActivityOrder.query.filter(
                IeoActivityOrder.id == order_id,
                IeoActivityOrder.user_id == user_id,
                IeoActivityOrder.ieo_activity_id == activity_id,
                IeoActivityOrder.status == IeoActivityOrder.StatusType.VALID,
            ).first()
            if not order:
                raise InvalidArgument(message='order not exist')
            order.status = IeoActivityOrder.StatusType.CANCELLING
            db.session.commit()
            cancel_ieo_activity_order(order.id)
        update_ieo_activity_schedule.delay()


@ns.route('/ieo/user-info')
@respond_with_code
class IeoActivityUserInfoResource(Resource, ActivityUserConditionMixin):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """ 用户的投资活动的信息 """
        activity_id = kwargs['activity_id']
        user_id = g.user.id

        activity = IeoActivity.query.filter(
            IeoActivity.status != IeoActivity.StatusType.OFFLINE,
            IeoActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument(message='activity not exist')

        order_list = IeoActivityOrder.query.filter(
            IeoActivityOrder.ieo_activity_id == activity_id,
            IeoActivityOrder.user_id == user_id,
        ).all()

        has_subscribe_count = sum([i.subscribe_count for i in order_list
                                   if i.status != IeoActivityOrder.StatusType.CANCELLED])
        has_cancel_count = sum([i.subscribe_count for i in order_list
                                if i.status in [IeoActivityOrder.StatusType.CANCELLED,
                                                IeoActivityOrder.StatusType.CANCELLING]])

        whitelist = ActivityWhiteList.query.filter(
            ActivityWhiteList.activity_id == activity.activity_id,
            ActivityWhiteList.user_id == user_id,
            ActivityWhiteList.status == ActivityWhiteList.Status.PASSED
        ).first()
        ieo_white_list = config.get('IEO_WHITE_LIST', [])
        result = dict(
            is_sign=IeoActivityUserSignedContractCache(
                activity_id).has_user(user_id),
            has_subscribe_count=has_subscribe_count,
            has_cancel_count=has_cancel_count,
            cancel_count=IeoActivity.MAX_CANCEL_COUNT,
            in_whitelist=True if (whitelist or user_id in ieo_white_list) else False,
            kyc_country=g.user.kyc_country,
        )
        conditions_query = {
            i.key: i.value for i in IeoActivityCondition.query.filter(
                IeoActivityCondition.ieo_activity_id == activity_id
            ).all()
        }
        conditions = cls.get_condition_info(user_id, activity_id, conditions_query)
        if user_id in ieo_white_list:
            conditions['vip'] = 5
            conditions['kyc'] = True
        result['conditions'] = conditions
        return result


@ns.route('/ieo/orders')
@respond_with_code
class IeoActivityOrdersResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 投资活动订单列表 """
        user_id = g.user.id
        page = kwargs['page']
        limit = kwargs['limit']
        order_query = IeoActivityOrder.query.filter(
            IeoActivityOrder.user_id == user_id,
        ).order_by(IeoActivityOrder.id.desc())
        order_list = order_query.all()
        activity_ids = {i.ieo_activity_id for i in order_list}
        activity_list = IeoActivity.query.filter(IeoActivity.id.in_(activity_ids)).all()
        activity_map = {i.id: i for i in activity_list}
        pledge_asset_balance = defaultdict(Decimal)
        activity_info = {i.id: dict(
            max_cancel_count=IeoActivity.MAX_CANCEL_COUNT,
            has_cancel_count=int(),
        ) for i in activity_list}
        result = []
        asset_info_map = {i.ieo_activity_id: i.name for i in IeoAssetInformation.query.filter(
            IeoAssetInformation.ieo_activity_id.in_(activity_ids)).with_entities(
            IeoAssetInformation.name,
            IeoAssetInformation.ieo_activity_id,
        ).all()}
        for order in order_list:
            activity = activity_map[order.ieo_activity_id]
            record = dict(
                id=order.id,
                ieo_activity_id=order.ieo_activity_id,
                subscribe_asset=activity.subscribe_asset,
                asset_name=asset_info_map.get(order.ieo_activity_id, ''),
                subscribe_amount=activity.subscribe_amount * order.subscribe_count,
                subscribe_count=order.subscribe_count,
                activity_name=activity.name,
                status=order.get_order_status(activity.end_time),
                created_at=order.created_at,
            )
            result.append(record)
            if order.status == IeoActivityOrder.StatusType.VALID:
                pledge_asset_balance[activity.pledge_asset] += activity.pledge_amount * order.subscribe_count
            if order.status in [IeoActivityOrder.StatusType.CANCELLED, IeoActivityOrder.StatusType.CANCELLING]:
                # 计算撤销次数
                activity_info[order.ieo_activity_id]['has_cancel_count'] += 1

        return dict(
            pledge_asset_balance=pledge_asset_balance,
            activity_info=activity_info,
            orders=result[(page - 1) * limit: page * limit],
            total=len(result),
        )


@ns.route('/ieo/sign/<int:activity_id>')
@respond_with_code
class IeoActivitySignContractResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, activity_id):
        """ 投资活动签署协议 """
        user_id = g.user.id
        activity = IeoActivity.query.filter(
            IeoActivity.status == IeoActivity.StatusType.ONLINE,
            IeoActivity.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument(message='activity not exist')
        IeoActivityUserSignedContractCache(activity_id).add_user(user_id)


@ns.route("/user/coupon")
@respond_with_code
class UserCouponsResource(Resource):
    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        coupon_type=EnumField(Coupon.CouponType, missing=Coupon.CouponType.EXPERIENCE_FEE)
    ))
    def get(cls, **kwargs):
        """旧版卡券弹框接口, APP兼容保留"""
        user_id = g.user.id
        coupon_type = kwargs["coupon_type"]

        # 拥有使用中的同类型卡券返回空
        has_same_coupon = UserCoupon.query.join(Coupon, isouter=True).filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_((
                UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED,
            )),
            Coupon.coupon_type == coupon_type,
        ).first()
        if has_same_coupon:
            return None

        pool_dict = get_user_available_pool(user_id)
        pools = [p for p in pool_dict.get(CouponApply.Source.SYSTEM.name, []) if p["coupon_type"] == coupon_type.name]
        if not pools:
            return None
        pool = min(pools, key=lambda x: x["expired_at"])

        name = Coupon.CouponType[pool['coupon_type']].value
        return dict(
            id=pool["id"],
            name=_(name),
            asset=pool['value_type'],
            expired_at=pool['expired_at'],
            amount=pool['value'],
            source=pool["apply_source"],
            active_use_days=pool["active_use_days"],
            qualified_trade_amount=pool["extra"]["qualified_trade_amount"],
        )


@ns.route("/coupons/<int:pool_id>")
@respond_with_code
class UserReceiveCouponResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=10, interval=600)
    @ns.use_kwargs(dict(
        device_id=DeviceIdField(required=False)
    ))
    def post(cls, pool_id, **kwargs):
        """用户领取卡劵"""
        user_id = g.user.id
        pool: CouponPool = CouponPool.query.filter(
            CouponPool.id == pool_id
        ).first()
        if not pool:
            raise InvalidArgument
        if pool.status == CouponPool.Status.EXPIRED:
            raise CouponPoolExpired
        coupon = Coupon.query.get(pool.coupon_id)
        if not coupon:
            raise InvalidArgument

        if UserRepository.is_abnormal_user(user_id):
            raise ActivityAbnormalUserError

        activity_server = get_coupon_activity_server(pool)
        if activity_server:
            with CacheLock(LockKeys.add_activity_user_pool(pool.id), wait=False):
                db.session.rollback()
                activity_server.check_activity_condition(pool, user_id)
                activity_server.add_user_to_pool(pool, user_id)
                db.session.commit()

        check_user_active_coupon(pool, coupon, user_id)

        service = get_coupon_service(coupon.coupon_type)
        ip = get_request_ip()
        user_coupon = service.receive(
            coupon,
            pool,
            user_id,
            kwargs.get('device_id'),
            ip
        )
        extra = {}
        model = service.coupon_detail
        if coupon.coupon_type == Coupon.CouponType.TRADING_GIFT:
            coupon_detail = model.query.filter(
                model.coupon_id == coupon.id
            ).first()
            extra["amount_type"] = coupon_detail.amount_type.name
            extra["qualified_trade_amount"] = coupon_detail.qualified_trade_amount
            extra["trade_type"] = coupon_detail.trade_type.name
        elif coupon.coupon_type == Coupon.CouponType.CASHBACK_FEE:
            coupon_detail = model.query.filter(
                model.coupon_id == coupon.id
            ).first()
            extra["trade_type"] = coupon_detail.trade_type.name

        return dict(
            id=user_coupon.id,
            value_type=_(user_coupon.coupon_value_type),
            value=CouponTool.value_display(coupon.coupon_type, user_coupon.coupon_value),
            expired_at=user_coupon.usable_expired_at,
            coupon_type=coupon.coupon_type.name,
            active_use_days=coupon.usable_days,
            expire_at=user_coupon.usable_expired_at,  # APP 版本兼容字段
            activation_expired_at=datetime_to_time(
                user_coupon.activation_expired_at) if user_coupon.activation_expired_at else None,
            extra=extra
        )


@ns.route("/coupon")
@respond_with_code
class UserExchangeCouponResource(Resource):

    @staticmethod
    def _get_failed_messages(messages: list) -> list:
        res = []
        special_keys = ('REGISTRATION_TIME', 'USER_SOURCE', 'REFERRER', 'DESIGNATION', 'NO_DEAL')
        for m in messages:
            if m['key'] in special_keys:
                return [m['value']]
            res.append(m['value'])
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        code=mm_fields.String(required=True),
    ))
    @limit_user_frequency(count=10, interval=600)
    def post(cls, **kwargs):
        """兑换卡券"""
        platform = get_request_platform()
        if not platform.is_mobile():
            require_geetest(lambda: None)()

        code = kwargs['code']
        user_id = g.user.id
        if UserRepository.is_abnormal_user(user_id):
            raise ActivityAbnormalUserError
        code_pool = CouponCodePool.query.filter(
            CouponCodePool.code == code,
            CouponCodePool.status == CouponCodePool.Status.VALID
        ).first()
        if not code_pool:
            apply = CouponApply.query.filter(
                CouponApply.code == code,
                CouponApply.status == CouponApply.Status.PASSED
            ).first()
            if apply:
                raise CouponCodeOutOfTimeRange
            raise CouponCodeNotExist
        coupon_pool = CouponPool.query.filter(
            CouponPool.apply_coupon_id == code_pool.apply_coupon_id,
        ).first()
        coupon = Coupon.query.get(coupon_pool.coupon_id)
        if not coupon_pool or not coupon:
            raise CouponCodeNotExist

        if user_id in coupon_pool.get_send_user_ids():
            raise CouponCodeUsed

        if code_pool.send_count >= code_pool.total_count:
            raise CouponHasRunOut
        if code_pool.expired_at < now():
            raise CouponCodeExpired
        service = get_coupon_service(coupon.coupon_type)
        user_coupon, failed_items = service.exchange(code_pool_id=code_pool.id, user_id=user_id)

        if not user_coupon:
            return dict(
                success=False,
                error_messages=cls._get_failed_messages(failed_items)
            )

        coupon_type_mapper = {coupon.coupon_type: [coupon.id]}
        coupon_detail_mapper = CouponTool.dump_coupon_data(coupon_type_mapper)
        extra = coupon_detail_mapper.get(coupon.id, {})

        return dict(
            success=bool(user_coupon),
            id=user_coupon.id,
            name=_(coupon.coupon_type.value),
            value=CouponTool.value_display(coupon.coupon_type, user_coupon.coupon_value),
            value_type=_(user_coupon.coupon_value_type),
            coupon_type=coupon.coupon_type.name,
            active_use_days=coupon.usable_days,
            expired_at=datetime_to_time(user_coupon.usable_expired_at) if user_coupon.usable_expired_at else None,
            activation_expired_at=datetime_to_time(
                user_coupon.activation_expired_at) if user_coupon.activation_expired_at else None,
            extra=extra,
            error_messages=[]
        )


@ns.route("/coupons/exchange-history")
@respond_with_code
class UserExchangeCouponHistoryResource(Resource):
    marshal_fields = {
        'created_at': TimestampMarshalField,
        'amount': AmountField,
        'asset': fx_fields.String,
        'type': fx_fields.String(attribute=lambda x: _(x.type.value)),
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        query = CouponExchangeHistory.query.filter(
            CouponExchangeHistory.user_id == g.user.id,
            CouponExchangeHistory.created_at >= now() - timedelta(days=365)
        ).order_by(CouponExchangeHistory.id.desc())

        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route("/coupons/public")
@respond_with_code
class PublicCouponResponse(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        coupon_type=EnumField(enum=Coupon.CouponType),
        coupon_status=EnumField(enum=[
            CouponPoolStatus.AVAILABLE.value,
            CouponPoolStatus.ENDING.value
        ], missing=""),
    ))
    def get(cls, **kwargs):
        """卡劵中心-公共卡劵"""
        user_id = g.user.id
        pools = get_user_coupon_pools(user_id)
        active_total = len([p for p in pools if p["status"] == "available"])
        items = []
        coupon_type = kwargs.get("coupon_type")
        coupon_status = kwargs.get("coupon_status")
        for pool in pools:
            if coupon_type and pool["coupon_type"] != coupon_type.name:
                continue
            if coupon_status and pool["status"] != coupon_status:
                continue
            _coupon_type = pool['coupon_type']  # avoid gettext
            pool["name"] = _(Coupon.CouponType[_coupon_type].value)
            pool["value_type"] = _(pool["value_type"])
            items.append(pool)

        return dict(
            active_total=active_total,
            items=items,
        )


@ns.route("/coupons/myself")
@respond_with_code
class MyselfCouponResponse(Resource):

    @classmethod
    def _get_user_trade_amount(cls, user_coupons):
        result = {}
        for model in [ExperienceFeeUserCoupon, TradingGiftUserCoupon, CopyTradingExperienceFeeUserCoupon]:
            result.update({
                user_coupon_id: user_trade_amount for user_coupon_id, user_trade_amount in
                model.query.filter(
                    model.user_coupon_id.in_((i.id for i in user_coupons))
                ).with_entities(
                    model.user_coupon_id,
                    model.user_trade_amount
                ).all()
            })
        return result

    @classmethod
    def _get_user_cashback_amount(cls, user_coupons):
        return {_id: amount for _id, amount in CashBackFeeUserCoupon.query.filter(
            CashBackFeeUserCoupon.user_coupon_id.in_((i.id for i in user_coupons))
        ).with_entities(
            CashBackFeeUserCoupon.user_coupon_id,
            CashBackFeeUserCoupon.back_amount
        )}

    @classmethod
    def get_active_coupon_total(cls, user_id: int):
        return UserCoupon.query.filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_((
                UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED
            ))
        ).with_entities(
            func.count(UserCoupon.id)
        ).scalar() or 0

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        coupon_type=EnumField(enum=Coupon.CouponType),
        coupon_status=EnumField(enum=UserCouponStatus, missing="", enum_by_value=True),
        page=fields.Integer,
        limit=fields.Integer,
    ))
    def get(cls, **kwargs):
        """卡劵中心-个人卡劵"""
        user_id = g.user.id
        active_total = cls.get_active_coupon_total(user_id)
        page, limit = kwargs.get("page") or 1, kwargs.get("limit") or 10
        query = UserCoupon.query.join(Coupon, isouter=True).filter(
            UserCoupon.user_id == user_id
        )
        if coupon_type := kwargs.get("coupon_type"):
            query = query.filter(
                Coupon.coupon_type == coupon_type
            )
        if coupon_status := kwargs.get("coupon_status"):
            if coupon_status == UserCouponStatus.USING:
                user_coupon_status = CouponTool.get_coupon_type_using_status(coupon_type)
            else:
                user_coupon_status = [UserCoupon.Status(coupon_status.value)]
            query = query.filter(
                UserCoupon.status.in_(user_coupon_status)
            )

        query = query.with_entities(
            UserCoupon.id,
            Coupon.coupon_type,
            Coupon.usable_days,
            Coupon.receivable_days,
            Coupon.activation_days,
            UserCoupon.coupon_value_type,
            UserCoupon.coupon_value,
            UserCoupon.coupon_id,
            UserCoupon.status,
            UserCoupon.used_at,
            UserCoupon.usable_expired_at,
            UserCoupon.activation_expired_at,
            UserCoupon.created_at,
        )

        user_coupons = list(query.all())  # 只能在内存中排序，查全部
        total = len(user_coupons)

        def _sort(_r):
            # 优先排未使用和使用中
            _status = CouponTool.dump_user_coupon_status(_r.coupon_type, _r.status).value
            activation_expired_time = datetime_to_time(_r.activation_expired_at) if _r.activation_expired_at else 0
            usable_expired_time = datetime_to_time(_r.usable_expired_at) if _r.usable_expired_at else 0
            if _status == "created":
                return 0, activation_expired_time, _r.id
            if _status == "using":
                return 1, usable_expired_time, _r.id
            return 2, -(usable_expired_time or activation_expired_time), _r.id

        user_coupons = sorted(user_coupons, key=_sort)
        user_coupons = user_coupons[(page - 1) * limit: page * limit]

        coupon_type_mapper = defaultdict(list)
        ex_user_coupons = []
        cashback_user_coupons = []
        for item in user_coupons:
            coupon_type_mapper[item.coupon_type].append(item.coupon_id)
            if item.coupon_type in [
                Coupon.CouponType.EXPERIENCE_FEE,
                Coupon.CouponType.TRADING_GIFT,
                Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE,
            ]:
                ex_user_coupons.append(item)
            if item.coupon_type in [Coupon.CouponType.CASHBACK_FEE]:
                cashback_user_coupons.append(item)
        coupon_detail_mapper = CouponTool.dump_coupon_data(coupon_type_mapper)
        trade_amount_mapper = cls._get_user_trade_amount(ex_user_coupons)
        cashback_amount_mapper = cls._get_user_cashback_amount(cashback_user_coupons)
        items = []
        for user_coupon in user_coupons:
            if user_coupon.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE:
                usable_expired_at = user_coupon.used_at + timedelta(
                    days=user_coupon.usable_days) if user_coupon.used_at else None
            else:
                usable_expired_at = user_coupon.usable_expired_at

            items.append(dict(
                id=user_coupon.id,
                name=_(user_coupon.coupon_type.value),
                value=CouponTool.value_display(user_coupon.coupon_type, user_coupon.coupon_value),
                value_type=_(user_coupon.coupon_value_type),
                coupon_type=user_coupon.coupon_type.name,
                status=CouponTool.dump_user_coupon_status(user_coupon.coupon_type, user_coupon.status).value,
                active_use_days=user_coupon.usable_days,
                activation_days=user_coupon.activation_days,
                receivable_days=user_coupon.receivable_days,
                expired_at=datetime_to_time(usable_expired_at) if usable_expired_at else None,
                activation_expired_at=datetime_to_time(
                    user_coupon.activation_expired_at) if user_coupon.activation_expired_at else None,
                created_at=datetime_to_time(user_coupon.created_at),
                user_trade_amount=amount_to_str(trade_amount_mapper.get(user_coupon.id) or Decimal(), 2),
                cashback_amount=amount_to_str(cashback_amount_mapper.get(user_coupon.id) or Decimal(), 2),
                extra=coupon_detail_mapper.get(user_coupon.coupon_id, {})
            ))

        return dict(
            total=total,
            active_total=active_total,
            page=page,
            items=items,
        )


@ns.route("/coupons/statuses")
@respond_with_code
class CouponStatuesResponse(Resource):
    ALL = {
        "created": _("待激活"),
        "using": _("使用中"),
        "used": _("已使用"),
        "expired": _("已过期"),
        "invalid": _("已失效"),
    }

    EXPERIENCE_FEE = TRADING_GIFT = VIP_UPGRADE = {
        "using": _("使用中"),
        "used": _("已使用"),
        "expired": _("已过期"),
        "invalid": _("已失效"),
    }

    INVESTMENT_INCREASE_RATE = {
        "created": _("待激活"),
        "using": _("加息中"),
        "used": _("已使用"),
        "expired": _("已过期"),
        "invalid": _("已失效"),
    }

    PERPETUAL_SUBSIDY = COPY_TRADING_EXPERIENCE_FEE = {
        "created": _("待激活"),
        "using": _("使用中"),
        "used": _("已使用"),
        "expired": _("已过期"),
        "invalid": _("已失效"),
    }

    @classmethod
    def get(cls):
        """ 卡券状态定义 """
        return {
            'ALL': [{'key': k, 'name': _(v)} for k, v in cls.ALL.items()],
            'EXPERIENCE_FEE': [{'key': k, 'name': _(v)} for k, v in cls.EXPERIENCE_FEE.items()],
            'TRADING_GIFT': [{'key': k, 'name': _(v)} for k, v in cls.TRADING_GIFT.items()],
            'INVESTMENT_INCREASE_RATE': [{'key': k, 'name': _(v)} for k, v in cls.INVESTMENT_INCREASE_RATE.items()],
            'CASHBACK_FEE': [{'key': k, 'name': _(v)} for k, v in cls.ALL.items()],
            'PERPETUAL_SUBSIDY': [{'key': k, 'name': _(v)} for k, v in cls.PERPETUAL_SUBSIDY.items()],
            'VIP_UPGRADE': [{'key': k, 'name': _(v)} for k, v in cls.VIP_UPGRADE.items()],
            'COPY_TRADING_EXPERIENCE_FEE': [{'key': k, 'name': _(v)} for k, v in cls.COPY_TRADING_EXPERIENCE_FEE.items()],
        }


@ns.route("/coupons/myself/<int:user_coupon_id>")
@respond_with_code
class UseMyselfCouponResponse(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        asset=AssetField(required=False),
    ))
    @limit_user_frequency(count=10, interval=300)
    def post(cls, user_coupon_id, **kwargs):
        """ 用户手动使用卡券 """
        user_id = g.user.id
        user_coupon: UserCoupon = UserCoupon.query.filter(
            UserCoupon.id == user_coupon_id,
            UserCoupon.user_id == user_id,
        ).first()
        if not user_coupon:
            raise InvalidArgument
        if user_coupon.status == UserCoupon.Status.EXPIRED:
            raise UsingCouponExpired
        if user_coupon.status == UserCoupon.Status.INVALID:
            raise UsingCouponInvalid
        if user_coupon.status != UserCoupon.Status.CREATED:
            raise UsingCouponStatusInvalid

        coupon: Coupon = Coupon.query.filter(Coupon.id == user_coupon.coupon_id).first()
        if not coupon:
            raise InvalidArgument
        if coupon.coupon_type in CouponTool.auto_use_coupon_types():
            # 目前只有理财加息券可以手动使用
            raise InvalidArgument
        if coupon.coupon_type == Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE:
            # 合约跟单体验金 在 跟单那边使用
            raise InvalidArgument

        if not (asset := kwargs.get("asset")) and coupon.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE:
            raise InvalidArgument
        service = get_coupon_service(coupon.coupon_type)
        user_coupon = service.use(user_coupon=user_coupon, coupon=coupon, asset=asset, auto_commit=True)
        # 更新下缓存,  用户激活就不应该弹窗了。
        CouponPopupReadCache(user_coupon.pool_id).add_user(user_id)
        extra = {}
        model = service.coupon_detail
        if coupon.coupon_type == Coupon.CouponType.CASHBACK_FEE:
            coupon_detail = model.query.filter(
                model.coupon_id == coupon.id
            ).first()
            extra["trade_type"] = coupon_detail.trade_type.name
        return dict(
            id=user_coupon.id,
            value_type=user_coupon.coupon_value_type,
            value=user_coupon.coupon_value,
            expired_at=user_coupon.usable_expired_at,
            extra=extra
        )


@ns.route("/coupons/investment")
@respond_with_code
class InvestmentCouponsResponse(Resource):

    @classmethod
    @require_login
    def get(cls):
        """ 未使用和使用中的理财加息券，用于理财账户页 """
        user_id = g.user.id
        inv_coupon_type = Coupon.CouponType.INVESTMENT_INCREASE_RATE
        rows = UserCoupon.query.join(Coupon, isouter=True).filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_((UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE)),
            Coupon.coupon_type == inv_coupon_type,
        ).with_entities(
            UserCoupon.id,
            UserCoupon.coupon_id,
            UserCoupon.coupon_value,
            UserCoupon.coupon_value_type,
            UserCoupon.status,
        ).order_by(UserCoupon.id.asc()).all()
        coupon_detail_map = CouponTool.dump_coupon_data({inv_coupon_type: [i.coupon_id for i in rows]})

        user_coupon_details = InvestmentIncRateUserCoupon.query.filter(
            InvestmentIncRateUserCoupon.user_coupon_id.in_([i.id for i in rows if i.status == UserCoupon.Status.ACTIVE])
        ).with_entities(
            InvestmentIncRateUserCoupon.user_coupon_id,
            InvestmentIncRateUserCoupon.investment_asset,
        ).all()
        user_coupon_inv_asset_map = dict(user_coupon_details)

        asset_unused_coupon_map = {}
        asset_using_coupon_map = {}
        for row in rows:
            detail = coupon_detail_map.get(row.coupon_id)
            if not detail:
                continue
            if row.status == UserCoupon.Status.CREATED:
                for asset in detail["assets"]:
                    if asset in asset_unused_coupon_map:
                        # 未使用的卡券，展示最早领取的
                        continue
                    asset_unused_coupon_map[asset] = {
                        "id": row.id,
                        "coupon_id": row.coupon_id,
                        "coupon_value": row.coupon_value,
                        "coupon_value_type": row.coupon_value_type,
                    }
            elif row.status == UserCoupon.Status.ACTIVE:
                investment_asset = user_coupon_inv_asset_map[row.id]
                asset_using_coupon_map[investment_asset] = {
                    "id": row.id,
                    "coupon_id": row.coupon_id,
                    "coupon_value": row.coupon_value,
                    "coupon_value_type": row.coupon_value_type,
                }

        # 转成币种维度数组
        asset_coupons = []
        assets = set(asset_unused_coupon_map) | set(asset_using_coupon_map)
        for asset in assets:
            asset_coupons.append(
                {
                    "asset": asset,
                    "unused_coupon": asset_unused_coupon_map.get(asset, {}),
                    "using_coupon": asset_using_coupon_map.get(asset, {}),
                }
            )
        return asset_coupons


@ns.route("/coupons/popup")
@respond_with_code
class CouponPopupResponse(Resource):

    @classmethod
    @require_login
    def get(cls):
        """一键领取弹框"""
        pools = get_user_available_pool(g.user.id)
        return pools


@ns.route("/coupons/receive/all")
@respond_with_code
class CouponReceiveAllResponse(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        pool_ids=fields.List(fields.Integer, required=True),
        device_id=DeviceIdField(required=False)
    ))
    @limit_user_frequency(count=10, interval=600)
    def post(cls, **kwargs):
        """卡劵弹框 一键领取"""
        pool_ids = kwargs["pool_ids"]
        if len(pool_ids) > 20:
            pool_ids = pool_ids[:20]

        pools = CouponPool.query.filter(
            CouponPool.id.in_(pool_ids)
        ).order_by(CouponPool.expired_at).all()
        user_id = g.user.id
        ip = get_request_ip()
        receive_result = []
        for pool in pools:
            coupon = Coupon.query.get(pool.coupon_id)
            service = get_coupon_service(coupon.coupon_type)
            is_success = True
            message = None
            try:
                # 基础检查
                activity_server = get_coupon_activity_server(pool)
                if activity_server:
                    with CacheLock(LockKeys.add_activity_user_pool(pool.id), wait=False):
                        db.session.rollback()
                        activity_server.check_activity_condition(pool, user_id)
                        activity_server.add_user_to_pool(pool, user_id)
                        db.session.commit()

                check_user_active_coupon(pool, coupon, user_id)
                user_coupon = service.receive(
                    coupon=coupon,
                    pool=pool,
                    user_id=user_id,
                    device_id=kwargs.get('device_id'),
                    ip=ip
                )
            # 其他类型卡劵 有其他的异常需要添加到这里
            except (InvalidArgument, CouponSendEnd, CouponPoolExpired,
                    UsingCouponObtained, UsingCouponLimit, NoviceValidateInvalid, NoviceRiskControl) as e:
                is_success = False
                user_coupon = None
                message = e.message or e.message_template
            receive_result.append({
                "pool_id": pool.id,
                "value": CouponTool.value_display(coupon.coupon_type, user_coupon.coupon_value) if user_coupon else None,
                "value_type": _(user_coupon.coupon_value_type) if user_coupon else None,
                "expired_at": user_coupon.usable_expired_at or user_coupon.activation_expired_at if user_coupon else None,
                "is_success": is_success,
                "message": message
            })
        return receive_result


@ns.route("/coupons/popup/report")
@respond_with_code
class CouponPopupReportResponse(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        pool_ids=fields.List(fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """直接发放的弹框查看上报 确保只弹出一次"""
        user_id = g.user.id
        pools = CouponPool.query.filter(
            CouponPool.id.in_(kwargs["pool_ids"])
        ).all()
        for pool in pools:
            if user_id not in pool.get_send_user_ids():
                continue
            CouponPopupReadCache(pool.id).add_user(user_id)
        push_cache = PushAvailablePoolCache()
        push_cache.delete_one(user_id)


@ns.route("/coupons/usable/total")
@respond_with_code
class CouponUsableTotalResponse(Resource):

    @classmethod
    @require_login
    def get(cls):
        """APP 个人中心 活跃卡劵个数"""
        user_id = g.user.id
        active_coupon_total = MyselfCouponResponse.get_active_coupon_total(user_id)
        pools = get_user_coupon_pools(user_id)
        active_pools_total = len([p for p in pools if p["status"] == "available"])
        return {
            "total": active_coupon_total + active_pools_total,
            "active_total_pools": active_pools_total,
            "using_total_coupons": active_coupon_total
        }


@ns.route("/fifth-anniversary/summary")
@respond_with_code
class FifthAnniversarySummaryResource(Resource):
    _activity_map = {
        "AIRDROP": _("空投活动"),
        "TRADE": _("交易排位赛"),
    }

    @classmethod
    @require_login
    def get(cls):
        """五周年活动账单"""
        user_id = g.user.id
        summary = FifthAnniversarySummary.query.filter(
            FifthAnniversarySummary.user_id == user_id
        ).first()
        if not summary:
            # 新注册用户没有数据，从已有数据获取公有数据
            tmp_summary = FifthAnniversarySummary.query.first()
            return dict(user_id=user_id,
                        user_created_at=g.user.created_at,
                        spot_btc_max_price=tmp_summary.spot_btc_max_price,
                        spot_btc_max_price_time=tmp_summary.spot_btc_max_price_time,
                        system_activity_reward_usd=tmp_summary.system_activity_reward_usd,
                        is_new_user=True,
                        key_words=dict(high=['crypto_columbus']),
                        )
        result = summary.to_dict()
        result.pop("challenge_data", None)
        if result['spot_max_btc_deal_side']:
            result['spot_max_btc_deal_side'] = result['spot_max_btc_deal_side'].name
            spot_btc_deal_price = (
                result['spot_btc_deal_price'] / result['spot_max_btc_deal_amount'] if result[
                    'spot_max_btc_deal_amount'] else 0
            )
            result['spot_btc_deal_price'] = amount_to_str(spot_btc_deal_price, 2)
        activities = json.loads(result['joined_activities'])
        result['joined_activities'] = []
        for a in activities:
            result['joined_activities'].append(_(cls._activity_map[a]))
        result['spot_total_deal_assets'] = json.loads(result['spot_total_deal_assets'])[:3]
        if result['key_words']:
            result['key_words'] = json.loads(result['key_words'])
        else:
            result['key_words'] = dict(top=[], high=[], mid=[], low=[])

        amm_market_count = len(AmmMarketCache.list_amm_markets())
        result['amm_market_count'] = amm_market_count
        stat_cache = FifthAnniversaryBillStatisticsCache()
        if not (stat := stat_cache.read()):
            stat_cache.reload()
            stat = stat_cache.read()
        result.update(json_loads(stat))

        result['is_new_user'] = False
        # 加入查看账单用户缓存
        cache = ActivateReportCache()
        if not cache.hexists(user_id):
            _now = str(current_timestamp(to_int=True))
            cache.hset(user_id, _now)

        return result


@ns.route("/fifth-anniversary/high-five")
@respond_with_code
class FifthAnniversaryHiveFiveResource(Resource):

    @classmethod
    def get(cls):
        cache = FifthAnniversaryHighFiveCache()
        count = cache.read() or 0
        return dict(count=int(count))

    @classmethod
    def post(cls):
        cache = FifthAnniversaryHighFiveCache()
        cache.incr()


@ns.route("/fifth-anniversary/time")
@respond_with_code
class FifthAnniversaryTimeResource(Resource):
    CHALLENGE_START_DATE = date(2022, 12, 24)
    CHALLENGE_END_DATE = date(2023, 1, 8)

    BLIND_BOX_START_DATE = date(2023, 1, 1)
    BLIND_BOX_END_DATE = date(2023, 1, 8)

    @classmethod
    def get(cls):
        """ 5周年-挑战活动、盲盒活动时间 """
        return {
            "start_date": cls.CHALLENGE_START_DATE,
            "end_date": cls.CHALLENGE_END_DATE,
            "blind_box_start_date": cls.BLIND_BOX_START_DATE,
            "blind_box_end_date": cls.BLIND_BOX_END_DATE,
        }

    @classmethod
    def check_challenge_time(cls):
        now_ = now()
        if date_to_datetime(cls.CHALLENGE_START_DATE) > now_:
            raise InvalidArgument(message=_("活动未开始"))
        if now_ > date_to_datetime(cls.CHALLENGE_END_DATE):
            raise InvalidArgument(message=_("活动已结束"))

    @classmethod
    def check_blind_box_time(cls):
        now_ = now()
        if date_to_datetime(cls.BLIND_BOX_START_DATE) > now_:
            raise InvalidArgument(message=_("活动未开始"))
        if now_ > date_to_datetime(cls.BLIND_BOX_END_DATE):
            raise InvalidArgument(message=_("活动已结束"))


@ns.route("/fifth-anniversary/thank-letter")
@respond_with_code
class FifthAnniversaryThankLetterResource(Resource):
    """ 5周年-感谢信 """

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        cache = FifthAnniversaryThankLetterCache()
        has_viewed = bool(cache.get_bit(user_id))
        return {"viewed": has_viewed}

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        user_id = g.user.id
        cache = FifthAnniversaryThankLetterCache()
        cache.set_bit(user_id, True)
        return {}


@ns.route("/fifth-anniversary/forbidden")
@respond_with_code
class FifthAnniversaryForbiddenResource(Resource):
    @classmethod
    def user_and_location_is_forbidden(cls, user: User) -> bool:
        from app.api.frontend.system import IPResource

        only_withdraw_status = UserVisitPermissionCache().get_user_permission(user.id)
        if only_withdraw_status == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
            # 白名单用户
            return False
        elif only_withdraw_status in (
                UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE,
                UserVisitPermissionCache.FORBIDDEN_VALUE):
            return True

        ip_info = GeoIP(get_request_ip())
        if IPResource.is_ip_forbidden(ip_info) or user.location_code in ["CHN"]:
            return True

        return False

    @classmethod
    def get(cls):
        """ 5周年-是否禁用 """
        from app.api.frontend.system import IPResource

        user = get_request_user()
        if user:
            is_forbidden = cls.user_and_location_is_forbidden(user)
        else:
            ip_info = GeoIP(get_request_ip())
            is_forbidden = IPResource.is_ip_forbidden(ip_info)
        return {"is_forbidden": is_forbidden}


@ns.route("/fifth-anniversary/ticket")
@respond_with_code
class FifthAnniversaryChallengeTicketResource(Resource):

    @classmethod
    def check_user_and_location(cls):
        # 检查用户和地区
        if FifthAnniversaryForbiddenResource.user_and_location_is_forbidden(g.user):
            raise InvalidArgument(message="forbidden")

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 5周年-大挑战门票 """
        user_id = g.user.id
        row = FifthAnniversaryChallenge.query.filter(
            FifthAnniversaryChallenge.user_id == user_id,
        ).with_entities(
            FifthAnniversaryChallenge.id,
        ).first()
        return {"has_ticket": bool(row)}

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        """ 5周年-领取大挑战门票 """
        from app.api.frontend.user import UserProfileResource

        cls.check_user_and_location()
        user_id = g.user.id

        # 领大门票: 写入挑战信息表
        row: FifthAnniversaryChallenge = FifthAnniversaryChallenge.get_or_create(user_id=user_id)
        if row.id:
            return

        summary: FifthAnniversarySummary = FifthAnniversarySummary.query.filter(
            FifthAnniversarySummary.user_id == user_id,
        ).with_entities(
            FifthAnniversarySummary.challenge_data,
        ).first()
        challenge_data: dict = json.loads(summary.challenge_data) if summary and summary.challenge_data else {}

        row.on_chain_deposit_usd = Decimal(challenge_data.get("on_chain_deposit_usd", 0))
        row.spot_deal_usd = Decimal(challenge_data.get("spot_deal_usd", 0))
        row.perpetual_deal_usd = Decimal(challenge_data.get("perpetual_deal_usd", 0))
        row.has_exchange_trade = bool(challenge_data.get("has_exchange_trade"))
        row.has_investment = bool(challenge_data.get("has_investment"))
        row.trade_fee_usd = Decimal(challenge_data.get("trade_fee_usd", 0))
        now_ = now()
        if not row.box1_finished_at and row.check_box1_task_finished:
            row.box1_finished_at = now_
        if not row.finished_at and row.check_all_task_finished:
            row.finished_at = now_

        if not UserProfileResource.had_future_trade(user_id):
            DynamicUserCouponCache(
                CouponApply.DynamicUser.FIVE_ACTIVITY.name,
                Coupon.CouponType.EXPERIENCE_FEE.name,
            ).add_users([user_id])
        if not UserProfileResource.had_spot_trade(user_id):
            DynamicUserCouponCache(
                CouponApply.DynamicUser.FIVE_ACTIVITY.name,
                Coupon.CouponType.TRADING_GIFT.name
            ).add_users([user_id])

        db.session.add(row)
        db.session.commit()


@ns.route("/fifth-anniversary/broadcast/<type_>")
@respond_with_code
class FifthAnniversaryBroadcastResource(Resource):

    @classmethod
    def get(cls, type_):
        """ 5周年-盲盒开箱机会、盲盒中奖 轮播 """
        if type_ not in {"qualify", "reward"}:
            raise InvalidArgument

        lang = g.lang or Language.DEFAULT.value
        if lang == Language.ZH_HANS_CN.value:
            lang = Language.ZH_HANT_HK.value
        return cls._get(type_, lang)

    @classmethod
    @mem_cached(120)
    def _get(cls, type_: str, lang: str):
        cache = FifthBroadcastCache(type_, lang)
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/fifth-anniversary/challenge")
@respond_with_code
class FifthAnniversaryChallengeStatusResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 5周年-大挑战任务状态 """
        user_id = g.user.id
        row: FifthAnniversaryChallenge = FifthAnniversaryChallenge.query.filter(
            FifthAnniversaryChallenge.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument(message=_("请先解锁周年账单，领取门票。"))

        result = {
            "on_chain_deposit_usd": row.on_chain_deposit_usd,
            "spot_deal_usd": row.spot_deal_usd,
            "has_exchange_trade": row.has_exchange_trade,
            "has_investment": row.has_investment,
            "perpetual_deal_usd": row.perpetual_deal_usd,
        }
        return result


@ns.route("/fifth-anniversary/box")
@respond_with_code
class FifthAnniversaryBoxResource(Resource):

    @classmethod
    def get(cls):
        """ 5周年-盲盒数目信息 """
        ann_remain = FifthBoxRemainNumCache(FifthAnniversaryBox.BoxType.ANNIVERSARY.name).read()
        ann_remain = max(int(ann_remain), 0)
        ult_remain = FifthBoxRemainNumCache(FifthAnniversaryBox.BoxType.ULTIMATE.name).read()
        ult_remain = max(int(ult_remain), 0)
        return {
            "anniversary_box_total": ANNIVERSARY_BOX_NUM,
            "anniversary_box_receive_num": ANNIVERSARY_BOX_NUM - ann_remain,
            "ultimate_box_total": ULTIMATE_BOX_NUM,
            "ultimate_box_receive_num": ULTIMATE_BOX_NUM - ult_remain,
        }

    @classmethod
    def _validate_device_id(cls, device_id: str) -> bool:
        if not (8 <= len(device_id) <= 64 and re.fullmatch(r'^[\w\d-]+$', device_id)):
            return False
        return True

    @classmethod
    @cached(60 * 10)
    def _get_user_balance_usd(cls, user_id_):
        """ 获取用户总资产 """
        client = ServerClient()
        p_client = PerpetualServerClient()
        res = client.get_user_accounts_balances(user_id_)
        p_res = p_client.get_user_balances(user_id_)
        price_map = PriceManager.assets_to_usd()
        usd = Decimal()
        for _acc, account_v in res.items():
            for ass, bal in account_v.items():
                n = bal['available'] + bal['frozen']
                usd += n * price_map.get(ass, Decimal())
        for ass, bal in p_res.items():
            n = bal['available'] + bal['frozen']
            usd += n * price_map.get(ass, Decimal())
        return quantize_amount(usd, 2)

    @classmethod
    def is_need_risk_check(cls, user: User) -> bool:
        new_user_dt = date(2022, 12, 22)
        user_id = user.id
        if user.created_at.date() >= new_user_dt:
            # 注册时间在2022年12月22日 0点(UTC)之后，同一设备最多2个账号，触发风控
            return True
        else:
            # 注册时间在2022年12月22日 0点(UTC)之前，同一设备最多2个账号，
            # 若超过2个账号开盲盒且若手续费<=3U，触发风控
            fee_usd = FifthUserFeeCache().get_user_fee(user_id)
            return fee_usd <= Decimal("3")

    @classmethod
    @require_login(allow_sub_account=False)
    @require_geetest
    @ns.use_kwargs(dict(
        box_type=EnumField(FifthAnniversaryBox.BoxType, required=True),
        device_id=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 5周年-开盲盒 """
        device_id = kwargs["device_id"]
        if not cls._validate_device_id(device_id):
            raise InvalidArgument
        FifthAnniversaryTimeResource.check_blind_box_time()
        FifthAnniversaryChallengeTicketResource.check_user_and_location()

        user: User = g.user
        user_id = user.id
        row: FifthAnniversaryChallenge = FifthAnniversaryChallenge.query.filter(
            FifthAnniversaryChallenge.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument(message=_("请先解锁周年账单，领取门票。"))

        box_type = kwargs["box_type"]
        if box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY:
            is_qualified = row.check_box1_task_finished
        else:
            is_qualified = row.check_all_task_finished
        if not is_qualified:
            # 任务未完成
            raise InvalidArgument(message=_("未完成对应任务，暂无法开启盲盒"))

        exist_box_type, exist_reward = FifthUserBoxRewardCache().get_user_reward(user_id)
        if exist_box_type or exist_reward:
            # 已开过盲盒
            if exist_box_type == box_type.name:
                raise UserAlreadyOpenBox
            else:
                raise UserAlreadyOpenOtherBox

        box_remain_cache = FifthBoxRemainNumCache(box_type.name)
        if int(box_remain_cache.read()) <= 0:
            # 盲盒已抢光
            raise BoxGrabEnd

        device_cache = FifthBoxOpenDeviceCache()
        if device_cache.get_count(device_id) >= 2:
            is_need_risk_check = cls.is_need_risk_check(user)
            current_app.logger.warning(
                f"OPEN_BOX_DEVICE_RISK user:{user_id} device:{device_id} is_need_risk_check:{is_need_risk_check}")
            if is_need_risk_check:
                report_device_box_risk_event(device_id, user_id)
                raise BoxTriggerRiskControl

        req_ip = get_request_ip()
        ip_cache = FifthBoxOpenIpCache()
        ip_box_limit = BusinessSettings.fifth_box_ip_max_open_num
        if req_ip and ip_cache.get_count(req_ip) >= ip_box_limit:
            current_app.logger.warning(f"OPEN_BOX_IP_RISK user:{user_id} req_ip:{req_ip}")
            report_ip_box_risk_event(req_ip, user_id)
            raise BoxTriggerRiskControl

        if _risk_reason := FifthRiskUserCache().hget(str(user_id)):
            current_app.logger.warning(f"OPEN_BOX_USER_RISK user:{user_id} risk_reason:{_risk_reason}")
            raise BoxTriggerRiskControl

        try:
            box_manager = FifthBoxManager(box_type)
            user_profile = box_manager.build_user_profile(user, row)
            reward = box_manager.open_box(user_id, user_profile)
        except Locked:
            raise InvalidArgument(message=_("请求繁忙，请稍后再试"))

        device_cache.hincrby(device_id, 1)
        ip_cache.hincrby(req_ip, 1)
        return {"reward": reward}


@ns.route("/fifth-anniversary/box/reward")
@respond_with_code
class FifthAnniversaryBoxRewardResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 5周年-用户盲盒奖品信息 """
        user_id = g.user.id
        box_type, reward = FifthUserBoxRewardCache().get_user_reward(user_id)
        reward_row: FifthAnniversaryBox = FifthAnniversaryBox.query.filter(
            FifthAnniversaryBox.user_id == user_id,
        ).first()
        return {
            "box_type": box_type,
            "reward": reward,
            "receipted": bool(reward_row and reward_row.send_at),
        }


@ns.route("/fifth-anniversary/coupons")
@respond_with_code
class FiveActivityCoupons(Resource):

    @classmethod
    @require_login
    def get(cls):
        """五周年-大挑战-卡券接口"""
        return {}


@ns.route("/fifth-anniversary/coupons/<int:pool_id>")
@respond_with_code
class FiveActivityReceiveCoupon(Resource):

    @classmethod
    @require_login
    @limit_user_frequency(count=10, interval=600)
    def post(cls, pool_id):
        return {}


@ns.route("/sixth-anniversary/info")
@respond_with_code
class SixthAnniversaryInfoResource(Resource):

    @classmethod
    def get(cls):
        """ 6周年-活动信息 """

        user_count = SixthRewardManager.user_completed_count()

        return {
            "pending_start_date": SixthConfigManager.PENDING_START_DATE,
            "pending_end_date": SixthConfigManager.PENDING_END_DATE,
            "running_start_date": SixthConfigManager.RUNNING_START_DATE,
            "running_end_date": SixthConfigManager.RUNNING_END_DATE,
            "user_count": user_count,
            "video_url": SixthConfigManager.VIDEO_URL,
        }

    @classmethod
    def check_activity_time(cls):
        now_ = now()
        if SixthConfigManager.RUNNING_START_DATE > now_:
            raise InvalidArgument(message=_("活动未开始"))
        if now_ > SixthConfigManager.RUNNING_END_DATE:
            raise InvalidArgument(message=_("活动已结束"))


@ns.route("/sixth-anniversary/game1024/qrcode")
@respond_with_code
class SixthAnniversaryGameQrcodeResource(Resource):

    @classmethod
    def get(cls):
        """ 6周年-1024游戏二维码生成 """

        qrcode = SixthQrcodeManager.get_game_qrcode()

        return dict(
            qrcode=qrcode
        )


@ns.route("/sixth-anniversary/activity/qrcode")
@respond_with_code
class SixthAnniversaryActivityQrcodeResource(Resource):

    @classmethod
    def get(cls):
        """ 6周年-活动海报二维码生成 """

        qrcode = SixthQrcodeManager.get_activity_qrcode()

        return dict(
            qrcode=qrcode
        )


@ns.route("/sixth-anniversary/answer/complete")
@respond_with_code
class SixthAnniversaryAnswerCompleteResource(Resource):
    """ 6周年-闯关完成 """

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        has_completed = SixthRewardManager.has_user_completed(user_id)
        return {"completed": has_completed}

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):

        SixthAnniversaryInfoResource.check_activity_time()

        user_id = g.user.id
        has_completed = SixthRewardManager.has_user_completed(user_id)
        if has_completed:
            raise InvalidArgument
        with CacheLock(LockKeys.sixth_activity(user_id), wait=False):
            db.session.rollback()
            row: SixthAnniversaryUser = SixthRewardManager.get_or_create(user_id)
            if row.status and row.status != SixthAnniversaryUser.Status.CREATED:
                raise InvalidArgument
            SixthRewardManager(row).user_complete()


@ns.route("/sixth-anniversary/reward/receive")
@respond_with_code
class SixthAnniversaryRewardReceiveResource(Resource):
    """ 6周年-礼物领取 """

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        row: SixthAnniversaryUser = SixthRewardManager.get_by_user_id(user_id)
        actual_reward = None
        if row and row.status == SixthAnniversaryUser.Status.FINISHED:
            actual_reward = row.actual_reward.name
        return dict(reward=actual_reward)

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        SixthAnniversaryInfoResource.check_activity_time()
        user_id = g.user.id
        has_completed = SixthRewardManager.has_user_completed(user_id)
        if not has_completed:
            raise InvalidArgument('complete error')
        row: SixthAnniversaryUser = SixthRewardManager.get_by_user_id(user_id)
        if not row:
            raise InvalidArgument('record not found')
        if row.status != SixthAnniversaryUser.Status.PASSED:
            raise InvalidArgument('status error')
        with CacheLock(LockKeys.sixth_activity(user_id), wait=False):
            db.session.rollback()
            row: SixthAnniversaryUser = SixthRewardManager.get_by_user_id(user_id)
            if not row:
                raise InvalidArgument('record not found')
            if row.status != SixthAnniversaryUser.Status.PASSED:
                raise InvalidArgument('status error')
            if not row.actual_reward:
                # 存在actual_reward已经记录但是领取失败的情况
                SixthRewardManager(row).set_actual_reward()
            SixthUserRewardSender.send_reward(row)
        return dict(reward=row.actual_reward.name)


@ns.route("/sixth-anniversary/questions")
@respond_with_code
class SixthAnniversaryQuestionResource(Resource):
    """ 6周年-题目 """

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        return dict(questions=SixthQuestionManager.get_question_by_lang(lang))


@ns.route('/seventh-anniversary-user-bill')
@respond_with_code
class SeventhAnniversaryUserBillResource(Resource):
    TOP_TRADE_USER_COUNT_TAG_IDS = [38, 8, 7, 5, 48]  # 可能不准，跑出数据后需要再修改

    @classmethod
    def get_rand_multiple(cls, user_id: int)-> int:
        # 随机展示50～1000的倍数，10为步数。5~100，再乘10
        rand_multiple = (user_id + 111111) % 96 + 5
        future_expect_multiple = max(min(rand_multiple, 100), 5) * 10
        return future_expect_multiple

    @classmethod
    @cached(300)
    def get_site_top_tag_names(cls, lang: Language) -> list[str]:
        columns = ["name"]
        tag_name_map = TagInfoCache.get_tran_columns(cls.TOP_TRADE_USER_COUNT_TAG_IDS, lang, columns)
        res = {k: v['name'] or v['en_name'] for k, v in tag_name_map.items()}
        tag_names = [res[i] for i in cls.TOP_TRADE_USER_COUNT_TAG_IDS if res.get(i)]
        return tag_names

    @classmethod
    def get_tag_name_map(cls, tag_ids: list[int], lang: Language) -> dict[int, str]:
        columns = ["name"]
        tag_name_map = TagInfoCache.get_tran_columns(list(set(tag_ids)), lang, columns)
        res = {k: v['name'] or v['en_name'] for k, v in tag_name_map.items()}
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """七周年用户账单"""
        user = g.user
        user_id = user.id
        lang = Language(g.lang)

        bill: SeventhAnniversaryUserBill = SeventhAnniversaryUserBill.query.filter(
            SeventhAnniversaryUserBill.user_id == user_id,
        ).first()
        if not bill:
            tag_names = cls.get_site_top_tag_names(lang)
            user_bill = dict(
                # p1
                user_name=user.nickname,
                is_new_user=True,
                active_days=0,
                highest_asset_val=Decimal(0),
                largest_order_trade_date=None,
                largest_order_market='',
                # 现货
                largest_order_market_type='',
                largest_order_buy_asset='',
                largest_order_buy_amount=Decimal(),
                largest_order_sell_asset='',
                largest_order_sell_amount=Decimal(),
                # 合约
                largest_order_trade_volume=Decimal(),
                largest_order_display_asset='',
                largest_order_asset_highest_price=Decimal(),
                largest_order_asset_highest_price_at=None,
                # p3
                on_bill_top_trade_assets=[],
                # p4
                top_tags=tag_names,
                user_trade_tag_count=0,
                same_user_tag='',
                top_tag_same_user_count=0,
                # p5
                trade_top_trending_asset='',
                trade_top_trending_asset_change_rate=Decimal(),
                # p6
                use_staking=False,
                use_copy_trading=False,
                use_p2p_trading=False,
                use_pre_market=False,
                use_launch_pool=False,
                use_ai_analysis=True,
                # p7
                refer_count=0,
                refer_amount=Decimal(),
                trade_assets_count=0,
                yearly_title=SeventhAnniversaryUserBill.YearlyTitle.RISING_WHALE.name,
            )
        else:
            top_tag_ids = json.loads(bill.top_tags) if bill.top_tags else []
            if top_tag_ids:
                tag_name_map = cls.get_tag_name_map(top_tag_ids, lang)
                tag_names = [tag_name_map[i] for i in top_tag_ids if tag_name_map.get(i)]
                if bill.same_user_tag and bill.same_user_tag.isdigit():
                    same_user_tag_name = tag_name_map.get(int(bill.same_user_tag), '')
                else:
                    same_user_tag_name= ''
            else:
                tag_names = cls.get_site_top_tag_names(lang)
                same_user_tag_name = ''
            user_bill = dict(
                # p1
                user_name=user.nickname,
                is_new_user=bill.is_new_user,
                active_days=bill.active_days,
                highest_asset_val=bill.highest_asset_val,
                # p2
                largest_order_trade_date=bill.largest_order_trade_date.strftime("%Y-%m-%d") if bill.largest_order_trade_date else '',
                largest_order_market=bill.largest_order_market,
                # 现货
                largest_order_market_type=bill.largest_order_market_type.name if bill.largest_order_market_type else '',
                largest_order_buy_asset=bill.largest_order_buy_asset,
                largest_order_buy_amount=bill.largest_order_buy_amount,
                largest_order_sell_asset=bill.largest_order_sell_asset,
                largest_order_sell_amount=bill.largest_order_sell_amount,
                # 合约
                largest_order_trade_volume=bill.largest_order_trade_volume,
                largest_order_display_asset=bill.largest_order_display_asset,
                largest_order_asset_highest_price=bill.largest_order_asset_highest_price,
                largest_order_asset_highest_price_at=bill.largest_order_asset_highest_price_at.strftime("%Y-%m-%d") if
                bill.largest_order_asset_highest_price_at else '',
                # p3
                on_bill_top_trade_assets=json.loads(bill.on_bill_top_trade_assets) if bill.on_bill_top_trade_assets else [],
                # p4
                top_tags=tag_names,
                user_trade_tag_count=bill.user_trade_tag_count,
                same_user_tag=same_user_tag_name,
                top_tag_same_user_count=bill.top_tag_same_user_count,
                # p5
                trade_top_trending_asset=bill.trade_top_trending_asset,
                trade_top_trending_asset_change_rate=bill.trade_top_trending_asset_change_rate,
                # p6
                use_staking=bill.use_staking,
                use_copy_trading=bill.use_copy_trading,
                use_p2p_trading=bill.use_p2p_trading,
                use_pre_market=bill.use_pre_market,
                use_launch_pool=bill.use_launch_pool,
                use_ai_analysis=True,
                # p7
                refer_count=bill.refer_count,
                refer_amount=bill.refer_amount,
                # p8
                trade_assets_count=bill.trade_assets_count,
                # p9
                yearly_title=bill.yearly_title.name,
            )
        user_bill['future_expect_multiple'] = cls.get_rand_multiple(user_id)
        user_bill['top_trade_assets'] = SEVENTH_TOP_TRADE_ASSETS[:10]
        user_bill['top_trending_assets'] = SEVENTH_TOP_TRENDING_ASSETS[:10]
        return user_bill


@ns.route("/seventh-anniversary/info")
@respond_with_code
class SeventhAnniversaryInfoResource(Resource):

    @classmethod
    def get(cls):
        """ 7周年-活动信息 """
        return {
            'running_start_date': SeventhConfigManager.RUNNING_START_DATE,
            'running_end_date': SeventhConfigManager.RUNNING_END_DATE,
            'video_url': SeventhConfigManager.VIDEO_URL,
        }

    @classmethod
    def check_activity_time(cls):
        now_ = now()
        if SeventhConfigManager.RUNNING_START_DATE > now_:
            raise InvalidArgument(message=_('活动未开始'))
        if now_ > SeventhConfigManager.RUNNING_END_DATE:
            raise InvalidArgument(message=_('活动已结束'))


@ns.route("/seventh-anniversary/activity/qrcode")
@respond_with_code
class SeventhAnniversaryActivityQrcodeResource(Resource):

    @classmethod
    def get(cls):
        """ 7周年-活动海报二维码生成 """

        qrcode = SeventhQrcodeManager.get_activity_qrcode()

        return dict(
            qrcode=qrcode
        )


@ns.route("/seventh-anniversary/asset/logo")
@respond_with_code
class SeventhAnniversaryAssetLogoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        assets=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """ 7周年-币种Logo批量获取 """

        assets = set([asset.upper() for asset in kwargs['assets'].split(',')][:20])
        return {
            item['short_name']: item['logo']
            for item in AssetInformationCache().get_assets_data(assets)
        }


@ns.route("/seventh-anniversary/reward/receive")
@respond_with_code
class SeventhAnniversaryRewardReceiveResource(Resource):
    """ 7周年-礼物领取 """

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        # 新注册用户在这里创建奖励记录
        row: SeventhAnniversaryUser = SeventhRewardManager.get_or_create(user_id)
        actual_reward = None
        if row.status == SeventhAnniversaryUser.Status.FINISHED:
            actual_reward = row.actual_reward.name
        return dict(
            reward=actual_reward,
            is_clear_user=LaunchMiningOp.is_clear_user(user_id),
        )

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        SeventhAnniversaryInfoResource.check_activity_time()
        user_id = g.user.id
        if LaunchMiningOp.is_clear_user(user_id):
            # 判断是否清退用户
            raise InvalidArgument('status error')
        row: SeventhAnniversaryUser = SeventhRewardManager.get_by_user_id(user_id)
        if not row:
            raise InvalidArgument('record not found')
        if row.status != SeventhAnniversaryUser.Status.CREATED:
            raise InvalidArgument('status error')
        with CacheLock(LockKeys.seventh_activity(user_id), wait=False):
            db.session.rollback()
            row: SeventhAnniversaryUser = SeventhRewardManager.get_by_user_id(user_id)
            if not row:
                raise InvalidArgument('record not found')
            if row.status != SeventhAnniversaryUser.Status.CREATED:
                raise InvalidArgument('status error')
            if not row.actual_reward:
                # 存在actual_reward已经记录但是领取失败的情况
                SeventhRewardManager(row).set_actual_reward()
            SeventhUserRewardSender.send_reward(row)
        return dict(reward=row.actual_reward.name)


@ns.route("/perpetual-special/index")
@respond_with_code
class PerpetualSpecialIndexResource(Resource):

    @classmethod
    @mem_cached(180)
    def get_data(cls):
        data_cache = PerpetualSpecialSummaryDataCache()
        return data_cache.read()

    @classmethod
    def get(cls):
        data = cls.get_data()
        if not data:
            return {}
        return json_string_success(data)


@ns.route("/perpetual-special/activities")
@respond_with_code
class PerpetualSpecialActivitiesResource(Resource):

    @classmethod
    @mem_cached(180)
    def get_banner_cache_data(cls, lang: str):
        return PerpetualActivityBannerCache(Language[lang]).read()

    @classmethod
    @response_replace_host_url
    def get(cls):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        cache_data = cls.get_banner_cache_data(lang.name)
        if not cache_data:
            return []
        return json_string_success(cache_data)


@ns.route("/perpetual-special/activities/<int:activity_id>")
@respond_with_code
class PerpetualSpecialActivityReportResource(Resource):

    @classmethod
    def post(cls, activity_id):
        """活动访问次数"""
        cache = PerpetualSpecialVisitCache(today(), activity_id)
        cache.incr()


@ns.route("/perpetual-special/tasks")
@respond_with_code
class PerpetualSpecialTasksResource(Resource):

    @classmethod
    def _get_online_benefit(cls):
        benefit = PerpetualNoviceBenefits.query.filter(
            PerpetualNoviceBenefits.status == PerpetualNoviceBenefits.Status.ONLINE
        ).first()
        return benefit

    @classmethod
    def _get_winning(cls, pool_id, user_id):
        if not pool_id:
            return False
        return bool(UserCoupon.query.filter(
            UserCoupon.user_id == user_id,
            UserCoupon.pool_id == pool_id
        ).first())

    @classmethod
    def _check_all_task_finished(cls, task_finish_mapper):
        finished = True
        for task_ in UserPerpetualTask.TaskType:
            finished &= task_finish_mapper.get(task_, False)
        return finished

    @classmethod
    def _query_user_trade_record(cls, user_id, start_at):
        c = PerpetualServerClient()
        result = c.user_deals(
            user_id=user_id,
            market='',
            start_time=datetime_to_time(start_at),
            side=0,
            limit=1
        )
        return len(result) > 0

    @classmethod
    def _query_user_transfer_record(cls, user_id, start_at):
        return bool(PerpetualBalanceTransfer.query.filter(
            PerpetualBalanceTransfer.transfer_type == PerpetualBalanceTransfer.TransferType.TRANSFER_IN,
            PerpetualBalanceTransfer.user_id == user_id,
            PerpetualBalanceTransfer.finished_at >= start_at
        ).order_by(
            PerpetualBalanceTransfer.id.desc()
        ).first())

    @classmethod
    def _check_task_and_update_record(cls, benefit_id, user_id, start_at, task_type):
        is_finished = False
        if task_type == UserPerpetualTask.TaskType.PERPETUAL_TRADED:
            is_finished = cls._query_user_trade_record(user_id, start_at)
        elif task_type == UserPerpetualTask.TaskType.FUNDS_TRANSFER:
            is_finished = cls._query_user_transfer_record(user_id, start_at)
        if is_finished:
            update_perpetual_special_task_status_task.delay(
                benefit_id,
                user_id,
                task_type.name
            )
        return is_finished

    @classmethod
    def _get_pool_data(cls, pool_id):
        pools = CouponPoolCache().get_cache_pools()
        pool_data = [p for p in pools if p['id'] == pool_id]
        if not pool_data:
            return {}
        pool = pool_data[0]
        _coupon_type = pool['coupon_type']  # avoid gettext
        pool["name"] = _(Coupon.CouponType[_coupon_type].value)
        return pool

    @classmethod
    @require_login
    def get(cls):
        user_id = g.user.id
        benefit = cls._get_online_benefit()
        if not benefit:
            return None
        if not PerpetualSpecialUsersCache(benefit.id).has_user(user_id):
            return None
        start_at = benefit.start_at
        task_query = UserPerpetualTask.query.filter(
            UserPerpetualTask.user_id == g.user.id,
            UserPerpetualTask.benefit_id == benefit.id
        ).with_entities(
            UserPerpetualTask.key,
            UserPerpetualTask.value
        ).all()
        key_value_mapper = {k: v for k, v in task_query}
        if not key_value_mapper.get(UserPerpetualTask.TaskType.PERPETUAL_TRADED, False):
            is_trade = cls._check_task_and_update_record(
                benefit.id,
                user_id,
                start_at,
                UserPerpetualTask.TaskType.PERPETUAL_TRADED
            )
            key_value_mapper[UserPerpetualTask.TaskType.PERPETUAL_TRADED] = is_trade

        if not key_value_mapper.get(UserPerpetualTask.TaskType.FUNDS_TRANSFER, False):
            is_transfer = cls._check_task_and_update_record(
                benefit.id,
                user_id,
                start_at,
                UserPerpetualTask.TaskType.FUNDS_TRANSFER
            )
            key_value_mapper[UserPerpetualTask.TaskType.FUNDS_TRANSFER] = is_transfer
        pool_id = None
        pool_data = {}
        if cls._check_all_task_finished(key_value_mapper):
            if pool := CouponPool.query.filter(
                    CouponPool.apply_coupon_id == benefit.coupon_apply_id
            ).first():
                pool_id = pool.id
                pool_data = cls._get_pool_data(pool_id)

        return {
            "task_1_finished": key_value_mapper.get(UserPerpetualTask.TaskType.WATCH_VIDEO, False),
            "task_2_finished": key_value_mapper.get(UserPerpetualTask.TaskType.NOVICE_TEST, False),
            "task_3_finished": key_value_mapper.get(UserPerpetualTask.TaskType.FUNDS_TRANSFER, False),
            "task_4_finished": key_value_mapper.get(UserPerpetualTask.TaskType.PERPETUAL_TRADED, False),
            "pool_id": pool_id,
            "pool_data": pool_data,
            "is_lottery": LuckyDrawDateCache(benefit.id, None).has_user(user_id),
            "is_winning": cls._get_winning(pool_id, user_id)
        }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        task_type=EnumField(UserPerpetualTask.TaskType, required=True),
    ))
    def post(cls, **kwargs):
        user_id = g.user.id
        benefit = cls._get_online_benefit()
        task_type = kwargs["task_type"]
        if not benefit:
            return
        task = UserPerpetualTask.get_or_create(
            user_id=g.user.id,
            benefit_id=benefit.id,
            key=task_type
        )
        task.value = True
        db.session.add(task)
        db.session.commit()
        check_task_finished_and_update_pool(benefit, user_id)


@ns.route("/newbie-zone/index")
@respond_with_code
class NovicePrefectureResource(Resource):

    @classmethod
    def get_user_deposit(cls, user_id, deposit_types, start_time=None):
        deposit = Deposit.query.filter(
            Deposit.user_id == user_id,
            Deposit.status.in_((Deposit.Status.FINISHED, Deposit.Status.CONFIRMING)),
            Deposit.type.in_(deposit_types)
        ).first()
        if not start_time and deposit:
            return True
        if start_time and deposit and deposit.confirmed_at > start_time:
            return True
        fiat_order = FiatOrder.query.filter(
            FiatOrder.user_id == user_id,
            FiatOrder.status == FiatOrder.StatusType.APPROVED,
            FiatOrder.order_type == FiatOrder.OrderType.BUY
        ).first()
        if not start_time and fiat_order:
            return True
        if start_time and fiat_order and fiat_order.created_at > start_time:
            return True
        return False

    @classmethod
    def _had_sport_trade(cls, user_id):
        c = ServerClient()
        user_ids = get_main_and_sub_ids(user_id)[:SUB_ACCOUNT_NUM_LIMIT]

        def _query_had_trade(_user_id):
            result = c.user_finished_orders(
                user_id=_user_id,
                market='',
                start_time=0,
                end_time=0,
                stop_order_id=None,
                side=0,
                page=1,
                limit=1
            )
            return len(result) > 0

        for _user_id in user_ids:
            if _query_had_trade(_user_id):
                return True
        return False

    @classmethod
    def _had_perpetual_trade(cls, user_id):
        c = PerpetualServerClient()
        user_ids = get_main_and_sub_ids(user_id)[:SUB_ACCOUNT_NUM_LIMIT]

        def _query_had_trade(_user_id):
            result = c.user_deals(
                user_id=_user_id,
                market='',
                start_time=0,
                end_time=0,
                side=0,
                limit=1
            )
            return len(result) > 0

        for _user_id in user_ids:
            if _query_had_trade(_user_id):
                return True
        return False

    @classmethod
    def _had_exchange_trade(cls, user_id):
        user_ids = get_main_and_sub_ids(user_id)

        q = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id.in_(user_ids),
            AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED
        ).first()
        return bool(q)

    @classmethod
    def get_user_had_trade(cls, user_id):
        model = NovicePrefectureActivity
        novice = model.query.filter(
            model.status == model.Status.ONLINE
        ).first()
        if novice:
            cache_data = NoviceUserTradeCache(novice.id).read_one_user(user_id)
            if Decimal(cache_data.get(NoviceTradeType.SPOT_PERPETUAL.name, 0)):
                return True
        if cls._had_exchange_trade(user_id):
            return True
        if cls._had_sport_trade(user_id):
            return True
        if cls._had_perpetual_trade(user_id):
            return True
        return False

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        cache = NoviceUserCache(user_id)
        json_data = cache.read()
        if not json_data:
            json_data = json.dumps(dict(
                had_registered=True,
                had_deposited=cls.get_user_deposit(
                    user_id=user_id,
                    deposit_types=[Deposit.Type.ON_CHAIN, Deposit.Type.LOCAL]
                ),
                had_traded=cls.get_user_had_trade(user_id)
            ))
            cache.set(json_data, ex=cache.TTL)
        return json_string_success(json_data)


@ns.route("/newbie-zone/activity")
@respond_with_code
class NovicePrefectureActivityResource(Resource):
    class ActivityStatus(Enum):
        PENDING = "pending"  # 待开始
        ONGOING = "ongoing"  # 进行中
        ENDED = "ended"  # 已结束

    @classmethod
    def _get_coupon_activity_data(cls, coupon_apply_id):
        user = get_request_user()
        pools = CouponPoolCache().get_cache_pools()
        pool_data = [p for p in pools if p['apply_coupon_id'] == coupon_apply_id]
        if not pool_data:
            return {}
        pool = pool_data[0]
        _coupon_type = pool['coupon_type']  # avoid gettext
        pool["name"] = _(Coupon.CouponType[_coupon_type].value)
        if user and (user_coupon := UserCoupon.query.filter(
                UserCoupon.pool_id == int(pool["id"]),
                UserCoupon.user_id == user.id
        ).first()):
            pool["status"] = UserCouponStatus.USING.value
            pool["user_value"] = CouponTool.value_display(_coupon_type, user_coupon.coupon_value)
        return pool

    @classmethod
    def get_cache_data(cls, lang: Language):
        return NoviceActivityCache(lang).read()

    @classmethod
    @ns.use_kwargs(dict(
        task_type=EnumField(NovicePrefectureActivity.TaskType),
    ))
    def get(cls, **kwargs):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        cache_data = cls.get_cache_data(lang)
        if not cache_data:
            return None
        novice_data = json.loads(cache_data)

        data = dict(
            id=novice_data["id"],
            title=novice_data["title"],
            start_at=novice_data["start_at"],
            end_at=novice_data["end_at"],
            activity_status=novice_data["status"],
            activity_type=novice_data["activity_type"],
            display_status=novice_data["display_status"],
        )

        user_group_condition, activity_data = [], {}
        model = NovicePrefectureActivity
        if novice_data["activity_type"] == model.ActivityType.COUPON.name:
            activity_data = cls._get_coupon_activity_data(novice_data["coupon_apply_id"])
            if not activity_data and novice_data["status"] == model.Status.PENDING.name:
                activity_data = novice_data["virtual_activity_data"]
            user_group_condition = build_novice_condition_info(novice_data["user_group_condition"])
        elif novice_data["activity_type"] == model.ActivityType.PACKAGE.name:
            user = get_request_user(allow_sub_account=False)
            user_id = user.id if user else None
            model = NovicePrefectureActivity
            novice = model.query.get(novice_data["id"])
            user_valid = check_novice_package_user_valid(user_id, novice) and check_new_device_user(user_id)
            # 产品要求登录态下，app端只对有参与资格的用户返回活动
            if user_id and not user_valid and get_request_platform().is_mobile():
                return None
            data["user_activity_valid"] = user_valid
            package_data = cls.build_package_data(
                user_id, novice, novice_data["task_group_condition"], user_valid, kwargs.get("task_type"))
            data.update(package_data)
            user_group_condition = json.loads(novice_data["user_group_condition"])
            # 前端要求时间戳格式一致
            values = user_group_condition[0]["value"]
            for i in range(1, 3):
                if isinstance(values[i], int):
                    # 兼容一下旧数据，防止上线报错，后续删除
                    values[i] = int(values[i] / 1000) + 3600 * 8
                else:
                    values[i] = int(str_to_datetime(values[i]).timestamp())
        else:
            activity_data = {
                "notice_url": novice_data["notice_url"]
            }

        data.update(dict(
            user_group_condition=user_group_condition,
            activity_data=activity_data,
        ))
        return data

    @classmethod
    def build_package_data(cls, user_id, novice, task_group_condition, user_valid, task_type=None):
        tmp_task = task_type.name if task_type else task_group_condition[0]["task_type"]
        new_task_type, received_amount = add_user_task_info(user_id, task_group_condition, tmp_task, novice, user_valid)
        return {
            "task_group_condition": task_group_condition,
            "display_task": new_task_type,
            "received_amount": received_amount
        }


class AmbassadorActivityMixin:
    class Status:
        PENDING = 'PENDING'
        ONGOING = 'ONGOING'
        ENDED = 'ENDED'

    class ApplyingStatus:
        ONGOING = 'ONGOING'
        ENDED = 'ENDED'

    @classmethod
    def get_display_statuses(cls, row: OperationAmbassadorActivity):
        now_ = now()
        if row.ended_at < now_:
            status = cls.Status.ENDED
        elif row.started_at > now_:
            status = cls.Status.PENDING
        else:
            status = cls.Status.ONGOING
        if row.applying_ended_at > now_:
            applying_status = cls.ApplyingStatus.ONGOING
        else:
            applying_status = cls.ApplyingStatus.ENDED
        return status, applying_status


@ns.route('/ambassador-activity/list')
@respond_with_code
class AmbassadorActivityListResource(AmbassadorActivityMixin, Resource):
    model = OperationAmbassadorActivity

    marshal_fields = {
        'id': fx_fields.Integer,
        'title': fx_fields.String,
        'cover_url': fx_fields.String,
        'gift_asset': fx_fields.String,
        'min_gift_amount': fx_fields.String,
        'max_gift_amount': fx_fields.String,
        'started_at': TimestampMarshalField,
        'ended_at': TimestampMarshalField,
        'applying_ended_at': TimestampMarshalField,
        'status': fx_fields.String,
        'applying_status': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        大使活动列表
        """
        model = cls.model
        records = model.query.filter(
            model.status.in_((model.Status.ONLINE, model.Status.FINISHED))
        ).order_by(model.ended_at.desc()).all()
        total = len(records)
        contents = cls._get_contents(ids=[x.id for x in records])
        now_ = now()
        pending_list, started_list, ended_list = [], [], []
        for item in records:
            if item.ended_at < now_:
                ended_list.append(item)
            elif item.started_at > now_:
                pending_list.append(item)
            else:
                started_list.append(item)
        res = []
        for item in chain(started_list, pending_list, ended_list):
            min_amount, max_amount = item.gift_amount_range
            cover_url = item.cover_url
            status, applying_status = cls.get_display_statuses(row=item)
            item = item.to_dict()
            item['status'] = status
            item['applying_status'] = applying_status
            item['title'] = contents.get(item['id'])
            item['min_gift_amount'] = min_amount
            item['max_gift_amount'] = max_amount
            item['cover_url'] = cover_url
            res.append(item)
        page, limit = kwargs['page'], kwargs['limit']
        res = res[(page - 1) * limit: page * limit]
        return dict(
            items=marshal(res, cls.marshal_fields),
            total=total
        )

    @classmethod
    def _get_contents(cls, ids):
        model = OperationAmbassadorActivityContent
        lang = Language(g.lang)
        details = model.query.with_entities(
            model.activity_id,
            model.title,
        ).filter(
            model.activity_id.in_(ids),
            model.lang == lang
        ).all()
        return dict(details)


@ns.route('/ambassador-activity/<int:id_>')
@respond_with_code
class AmbassadorActivityDetailResource(AmbassadorActivityMixin, Resource):
    model = OperationAmbassadorActivity

    class Disqualification(Enum):
        AMBASSADOR_INVALID = '大使身份失效'
        CHEAT = '作弊'

    class NoQualificationReason(Enum):
        NOT_AMBASSADOR = '不是大使'
        NOT_MATCH = '大使条件不匹配'  # 返佣比例不符合

    @classmethod
    def get_amb_qualification_info(cls, user_id: int, activity: OperationAmbassadorActivity):
        # return (can_join, reason)
        model = Ambassador
        row = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
            model.type == model.Type.NORMAL
        ).first()
        if row:
            nor_amb_rate = ReferralBusiness.AMBASSADOR_LEVEL_RATE_MAP[row.level]
            if activity.normal_amb_max_rate is not None and nor_amb_rate > activity.normal_amb_max_rate:
                return False, cls.NoQualificationReason.NOT_MATCH.name
            else:
                return True, ""
        bus_ref_rate = None
        bus_amb = BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True)
        if bus_amb:
            bus_ref_rate = bus_amb.total_ref_rate
        else:
            tree_amb = TreeAmbHelper.get_ambassador(user_id, need_valid=True)
            if tree_amb:
                bus_ref_rate = tree_amb.rate
        if bus_ref_rate is not None:
            if activity.business_amb_max_rate is not None and bus_ref_rate > activity.business_amb_max_rate:
                return False, cls.NoQualificationReason.NOT_MATCH.name
            else:
                return True, ""
        return False, cls.NoQualificationReason.NOT_AMBASSADOR.name

    @classmethod
    def get(cls, id_):
        """
        大使活动详情
        """
        model = cls.model
        activity = model.query.filter(
            model.status.in_((model.Status.ONLINE, model.Status.FINISHED)),
            model.id == id_
        ).first()
        if not activity:
            raise InvalidArgument
        lang = Language(g.lang)
        detail = OperationAmbassadorActivityContent.query.filter(
            OperationAmbassadorActivityContent.activity_id == id_,
            OperationAmbassadorActivityContent.lang == lang
        ).first()

        is_blacklist_user = is_join_user = False
        disqualification = None
        user_id = None
        if user := get_request_user():
            user_id = user.id
            blacklist_user = ActivityBlackList.query.filter(
                ActivityBlackList.user_id == user_id,
                ActivityBlackList.activity_id == activity.activity_id,
                ActivityBlackList.status == ActivityBlackList.Status.PASSED
            ).first()
            is_blacklist_user = bool(blacklist_user)
            amb_can_join, no_qualification_reason = cls.get_amb_qualification_info(user_id, activity)
            can_join = not is_blacklist_user and amb_can_join
            join_user = AmbassadorActivityApplyUser.query.filter(
                AmbassadorActivityApplyUser.user_id == user_id,
                AmbassadorActivityApplyUser.activity_id == activity.id
            ).first()
            is_join_user = bool(join_user)
            if is_join_user and blacklist_user:
                if blacklist_user.remark == ActivityBusiness.REMARK:
                    disqualification = cls.Disqualification.AMBASSADOR_INVALID.name
                else:
                    disqualification = cls.Disqualification.CHEAT.name
        else:
            can_join = False
            no_qualification_reason = cls.NoQualificationReason.NOT_AMBASSADOR.name

        status, applying_status = cls.get_display_statuses(row=activity)
        min_amount, max_amount = activity.gift_amount_range
        user_count = ActivityBusiness(activity.id).get_applying_user_count()
        gift_amount = activity.gift_amount(user_count)
        return dict(
            activity=dict(
                title=detail.title,
                type=activity.type.name,
                status=status,
                applying_status=applying_status,
                started_at=int(activity.started_at.timestamp()),
                ended_at=int(activity.ended_at.timestamp()),
                applying_ended_at=int(activity.applying_ended_at.timestamp()),
                gift_asset=activity.gift_asset,
                gift_amount=quantize_amount(gift_amount, 8),
                min_gift_amount=quantize_amount(min_amount, 8),
                max_gift_amount=quantize_amount(max_amount, 8),
                is_blacklist_user=is_blacklist_user,
                is_join_user=is_join_user,
                can_join=can_join,
                no_qualification_reason=no_qualification_reason,
                disqualification=disqualification,
            ),
            rank_info=cls._get_rank_info(activity.id, activity.type),
            my_info=cls._get_user_info(user_id, activity.id, activity.type),
        )

    @classmethod
    @mem_cached(300)
    def _get_user_info(cls, user_id, activity_id, activity_type):
        gift_amount, statistics_amount, ratio, rank = 0, 0, 0, None
        if user_id:
            model = AmbassadorActivityUserInfo
            user_info = model.query.filter(
                model.activity_id == activity_id,
                model.user_id == user_id,
            ).first()
            if user_info:
                gift_amount = user_info.gift_amount
                rank = user_info.rank
                if activity_type in [
                    OperationAmbassadorActivity.Type.PERP_TRADE_AMOUNT,
                    OperationAmbassadorActivity.Type.ALL_PERP_TRADE_AMOUNT,
                ]:
                    statistics_amount = user_info.all_perp_amount
                else:
                    statistics_amount = user_info.all_fee_amount
                ratio = user_info.ratio
        return dict(
            statistics_amount=amount_to_str(statistics_amount, 8),
            ratio=ratio,
            gift_amount=gift_amount,
            rank=rank,
        )

    @classmethod
    @mem_cached(300)
    def _get_rank_info(cls, activity_id, activity_type):
        model = AmbassadorActivityUserInfo
        last_record = model.query.filter(
            model.activity_id == activity_id,
        ).with_entities(model.report_at).first()
        if not last_record:
            return dict(ranks=[])
        else:
            report_at = last_record.report_at
        records = model.query.filter(
            model.activity_id == activity_id,
            model.report_at == report_at,
            model.rank.isnot(None)
        ).order_by(model.rank)

        ranks = []
        for item in records:
            u = User.query.get(item.user_id)
            if activity_type in [
                OperationAmbassadorActivity.Type.PERP_TRADE_AMOUNT,
                OperationAmbassadorActivity.Type.ALL_PERP_TRADE_AMOUNT,
            ]:
                amount = item.all_perp_amount
            else:
                amount = item.all_fee_amount
            if item.gift_amount == Decimal():
                continue
            ranks.append(
                [
                    item.rank,
                    hide_email(u.email, hide_domain=True),
                    quantize_amount(amount, 8),
                    item.ratio,
                    item.gift_amount,
                ]
            )
        data = dict(
            ranks=ranks,
        )
        return data


@ns.route('/ambassador-activity/<int:id_>/apply')
@respond_with_code
class AmbassadorActivityUserResource(AmbassadorActivityMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, id_):
        """
        大使活动报名
        """
        user = g.user
        if user.location_code \
                and get_country(user.location_code).iso_2 in get_forbidden_region_code():
            raise NotSupportedByCountryInOnlyWithdrawal
        if UserRepository.is_abnormal_user(user.id):
            raise ActivityAbnormalUserError(message=_("你的账户暂被限制参与活动，如有疑问请联系客服。"))
        model = OperationAmbassadorActivity
        activity = model.query.filter(
            model.id == id_,
            model.status == model.Status.ONLINE,
            model.ended_at > now()
        ).first()
        if not activity:
            raise ActivityNotExists
        join_user = AmbassadorActivityApplyUser.query.filter(
            AmbassadorActivityApplyUser.user_id == g.user.id,
            AmbassadorActivityApplyUser.activity_id == activity.id
        ).first()
        if join_user:
            raise InvalidArgument
        amb_can_join, _reason = AmbassadorActivityDetailResource.get_amb_qualification_info(g.user.id, activity)
        if not amb_can_join:
            raise InvalidArgument(message="Not qualified")

        blacklist_user = ActivityBlackList.query.filter(
            ActivityBlackList.user_id == g.user.id,
            ActivityBlackList.activity_id == activity.activity_id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED
        ).first()
        if blacklist_user:
            raise InvalidArgument(message="Black list user")
        ActivityBusiness(activity.id).add_user(g.user.id, commit=True)


@ns.route('/ambassador-activity/history')
@respond_with_code
class AmbassadorActivityJoinHistoryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        大使活动参与记录
        """
        model = AmbassadorActivityApplyUser
        query = model.query.filter(
            model.user_id == g.user.id
        ).order_by(model.id.desc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = pagination.items
        if not items:
            return dict(items=[], total=0)

        ids = [item.activity_id for item in items]
        lang = Language(g.lang)
        details = OperationAmbassadorActivityContent.query.with_entities(
            OperationAmbassadorActivityContent.activity_id,
            OperationAmbassadorActivityContent.title
        ).filter(
            OperationAmbassadorActivityContent.activity_id.in_(ids),
            OperationAmbassadorActivityContent.lang == lang
        ).all()
        detail_map = dict(details)
        activities = OperationAmbassadorActivity.query.filter(
            OperationAmbassadorActivity.id.in_(ids)
        ).all()
        activity_map = {item.id: item for item in activities}
        infos = AmbassadorActivityUserInfo.query.filter(
            AmbassadorActivityUserInfo.user_id == g.user.id,
            AmbassadorActivityUserInfo.activity_id.in_(ids)
        ).all()
        info_map = {item.activity_id: item for item in infos}
        res = []
        for item in items:
            activity = activity_map[item.activity_id]
            info = info_map.get(item.activity_id)
            res.append(dict(
                id=item.activity_id,
                status=activity.status.name,
                title=detail_map.get(item.activity_id),
                gift_amount=activity.gift_amount(ActivityBusiness(activity.id).get_applying_user_count()),
                gift_asset=activity.gift_asset,
                user_gift_amount=info.gift_amount if info else 0
            ))
        return dict(
            items=res,
            total=pagination.total
        )


@ns.route('/coin-halving/banner')
@respond_with_code
class CoinHalvingBannerResource(Resource):

    @classmethod
    def get(cls):
        """币种减半-活动banner列表"""
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        return cls._get(lang)

    @classmethod
    @mem_cached(120)
    def _get(cls, lang: Language):
        cache = CoinHalvingActivityCache(lang)
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route('/coin-halving/block-info')
@respond_with_code
class CoinHalvingBlockResource(Resource):

    @classmethod
    def get(cls):
        """BTC第4次减半区块信息"""
        asset = 'BTC'
        halving_block_height = 840000
        last_block = cls._get_last_block()
        last_block_ts = int(last_block['time'])
        last_height = int(last_block['height'])
        if last_height >= halving_block_height:
            if last_height == halving_block_height:
                halving_time = last_block_ts
            else:
                halving_time = int(cls._get_one_block(halving_block_height)['time'])
            halving_countdown = 0
        else:
            halving_time = None
            delta_block = halving_block_height - last_height
            guess_halving_ts = last_block_ts + delta_block * get_asset_chain_config(asset, asset).block_time
            cur_ts = current_timestamp(to_int=True)
            halving_countdown = max(guess_halving_ts - cur_ts, 0)
        return {
            "last_block_height": last_height,
            "halving_block_height": halving_block_height,
            "halving_countdown": halving_countdown,
            "halving_time": halving_time,
        }

    @classmethod
    @cached(30)
    def _get_last_block(cls) -> dict:
        client = RESTClient("https://explorer.coinex.com", timeout=10)
        resp = client.get('/res/btc/blocks', limit=1)
        return resp['data']['data'][0]

    @classmethod
    @cached(300)
    def _get_one_block(cls, height: int) -> dict:
        client = RESTClient("https://explorer.coinex.com", timeout=10)
        resp = client.get(f'/res/btc/blocks/{height}')
        return resp['data']['data'][0]


@ns.route('/coin-halving/kline')
@respond_with_code
class CoinHalvingKlineResource(Resource):

    @classmethod
    def get(cls):
        """币种减半-行情价格数据"""
        return cls._get()

    @classmethod
    @mem_cached(300)
    def _get(cls) -> list:
        """ 返回天维度的历史Kline数据 """
        asset = 'BTC'
        end_ts = current_timestamp(to_int=True)
        kline_data = list(aggregate_price_kline_data(asset, 0, end_ts, AggregatePeriodType.DAY).items())
        kline_data.sort(key=lambda x: x[0], reverse=True)
        return kline_data


@ns.route('/satoshi/bid')
@respond_with_code
class SatoshiBidResource(Resource):
    ASSET = 'BTC'
    START_BID = Decimal(1)
    START_TIME = **********
    END_TIME = **********

    @classmethod
    def get(cls):
        last = SatoshiBidHistory.query.filter(
            SatoshiBidHistory.status == SatoshiBidHistory.Status.BIDDEN
        ).order_by(SatoshiBidHistory.id.desc()).first()
        if not last:
            curr_bid = Decimal()
            next_min_bid = cls.START_BID
        else:
            curr_bid = last.amount
            next_min_bid = last.amount + cls.min_delta_bid(curr_bid)

        user = get_request_user(allow_sub_account=False)
        my_bid = {}
        if user:
            my = SatoshiBidHistory.query.filter(
                SatoshiBidHistory.user_id == user.id,
                SatoshiBidHistory.status.in_((SatoshiBidHistory.Status.BIDDEN,
                                              SatoshiBidHistory.Status.OUTBID,
                                              SatoshiBidHistory.Status.REFUNDED))
            ).order_by(SatoshiBidHistory.id.desc()).first()
            if my:
                my_bid = {
                    'amount': my.amount,
                    'time': my.created_at
                }

        return {
            'asset': cls.ASSET,
            'start_bid': cls.START_BID,
            'curr_bid': curr_bid,
            'curr_bid_time': last.created_at if last else 0,
            'next_min_bid': next_min_bid,
            'start_time': cls.START_TIME,
            'end_time': cls.END_TIME,
            'my_bid': my_bid
        }

    @classmethod
    def min_delta_bid(cls, curr_bid):
        delta = curr_bid * Decimal('0.05')
        v = '%.1g' % delta
        return Decimal(v)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        amount=PositiveDecimalField(required=True),
    ))
    def post(cls, **kwargs):
        from app.schedules.activity import satoshi_bid_refund_task

        asset = cls.ASSET
        amount = quantize_amount(kwargs['amount'], 8)
        user_id = g.user.id
        now_ts = current_timestamp()
        if now_ts < cls.START_TIME:
            raise InvalidArgument(message=_("活动未开始"))
        if now_ts > cls.END_TIME:
            raise InvalidArgument(message=_("活动已结束"))

        with CacheLock(LockKeys.satoshi_bid()):
            db.session.rollback()
            last = SatoshiBidHistory.query.filter(
                SatoshiBidHistory.status == SatoshiBidHistory.Status.BIDDEN
            ).order_by(SatoshiBidHistory.id.desc()).first()
            if last and last.user_id == user_id:
                raise InvalidArgument(message="You have already bid")
            if not last:
                next_min_bid = cls.START_BID
            else:
                next_min_bid = last.amount + cls.min_delta_bid(last.amount)
            if amount < next_min_bid:
                raise InvalidArgument(message=f"Bid amount should be greater than {amount_to_str(next_min_bid)}")

            client = ServerClient()
            balance = client.get_user_balances(user_id, asset)
            if amount > balance[asset]['available']:
                raise InsufficientBalance

            business_id = BalanceUpdateBusiness.new_id(user_id, asset, -amount)
            # 写竞价记录，不生效，用作检查扣款失败退回
            bid_history = SatoshiBidHistory(user_id=user_id,
                                            asset=asset,
                                            amount=amount,
                                            pay_business_id=business_id,
                                            status=SatoshiBidHistory.Status.CREATED)
            db.session.add(bid_history)
            db.session.commit()

            client.add_user_balance(
                user_id=user_id,
                asset=asset,
                amount=-amount,
                business=BalanceBusiness.SYSTEM,
                business_id=business_id,
                detail={'remark': 'satoshi bid'}
            )
            if last:
                last.status = SatoshiBidHistory.Status.OUTBID
            bid_history.status = SatoshiBidHistory.Status.BIDDEN
            db.session.commit()
            if last:
                satoshi_bid_refund_task.delay(last.id)
            return {}


@ns.route('/satoshi/bid-history')
@respond_with_code
class SatoshiBidHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        query = SatoshiBidHistory.query.filter(
            SatoshiBidHistory.status.in_((SatoshiBidHistory.Status.BIDDEN,
                                          SatoshiBidHistory.Status.OUTBID,
                                          SatoshiBidHistory.Status.REFUNDED))
        ).order_by(SatoshiBidHistory.id.desc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = pagination.items
        emails = User.query.filter(User.id.in_([x.user_id for x in items])).with_entities(User.id, User.email).all()
        emails = {x: cls.hide_email(y) for x, y in emails}
        items = [dict(
            user=emails[x.user_id],
            asset=x.asset,
            amount=x.amount,
            time=x.created_at,
        ) for x in items]

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages
        )

    @classmethod
    def hide_email(cls, email):
        if not email:
            return ''
        s = min(len(email) - 2, 12)
        return hide_text(email, 2, 0, s)


@ns.route('/satoshi/my-bid-history')
@respond_with_code
class SatoshiMyBidHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        query = SatoshiBidHistory.query.filter(
            SatoshiBidHistory.user_id == g.user.id,
            SatoshiBidHistory.status.in_((SatoshiBidHistory.Status.BIDDEN,
                                          SatoshiBidHistory.Status.OUTBID,
                                          SatoshiBidHistory.Status.REFUNDED))
        ).order_by(SatoshiBidHistory.id.desc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = pagination.items
        email = SatoshiBidHistoryResource.hide_email(g.user.email)
        items = [dict(
            user=email,
            asset=x.asset,
            amount=x.amount,
            time=x.created_at,
        ) for x in items]

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages
        )


@ns.route('/wallacy')
@respond_with_code
class WallacyActiveUserResource(Resource, WallacyActivityMixin):
    TRADE_VOLUMES = [Decimal(100), Decimal(300), Decimal(500), Decimal(1000), Decimal(2000), Decimal(5000)]

    DEPOSIT_VOLUMES = [Decimal(500), Decimal(700), Decimal(1000), Decimal(2000)]

    @classmethod
    def get_user_trade_level(cls, find_arr: list[Decimal], trade_volume: Decimal):
        # 使用二分法查询数字在数组中的位置
        left, right = 0, len(find_arr) - 1
        while left <= right:
            mid = (left + right) // 2
            if find_arr[mid] <= trade_volume:
                left = mid + 1
            else:
                right = mid - 1
        return left

    @classmethod
    def get_signature(cls, user_code):
        msg = 'I am a CoinEx user:' + user_code
        signature = rsa.sign(msg.encode(), cls.PRIVATE_KEY, 'SHA-256')
        return base64.urlsafe_b64encode(msg.encode() + b'\n' + signature)

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        """生成活动用户，回调 Wallacy 地址"""
        user_id = g.user.id
        wallacy_user = WallacyActivityUser.query.filter(
            WallacyActivityUser.user_id == user_id
        ).first()
        if not wallacy_user:
            user_code = WallacyActivityUser.generate_code()
            wallacy_user = WallacyActivityUser(
                user_id=user_id,
                code=user_code,
                signature=cls.get_signature(user_code),
            )
            db.session_add_and_commit(wallacy_user)
        return f"{cls.REDIRECT_URL}?sign={wallacy_user.signature}"

    @classmethod
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
        start_date=TimestampField(is_ms=False),
        end_date=TimestampField(is_ms=False),
    ))
    def get(cls, **kwargs):
        """活动用户详情"""
        code = kwargs['code']
        cls.check_wallacy_params(code)
        wallacy_user = WallacyActivityUser.query.filter(
            WallacyActivityUser.code == code,
        ).first()
        if not wallacy_user:
            raise RecordNotFound(message="Wallacy user not found")
        user: User = User.query.get(wallacy_user.user_id)
        query_start_datetime = kwargs.get('start_date', cls.START_TIME)
        query_end_datetime = kwargs.get('end_date', cls.END_TIME)
        # 限制查询时间范围
        query_start_datetime = max(query_start_datetime.replace(tzinfo=None), cls.START_TIME)
        query_end_datetime = min(query_end_datetime.replace(tzinfo=None), cls.END_TIME)
        trade_amount, start_date, end_date = cls.user_trade_info(
            user.id,
            query_start_datetime,
            query_end_datetime
        )
        deposit_amount, first_deposit_time, last_deposit_time = cls.user_deposit_info(
            user.id,
            query_start_datetime,
            query_end_datetime
        )
        return dict(
            code=code,
            is_registered=bool(user),
            is_kyc=user.kyc_status == User.KYCStatus.PASSED,
            is_deposit=deposit_amount >= cls.TARGET_DEPOSIT_AMOUNT,
            deposit_info=dict(
                level=cls.get_user_trade_level(cls.DEPOSIT_VOLUMES, deposit_amount),
                start_at=first_deposit_time,
                end_at=last_deposit_time
            ),
            is_traded=trade_amount >= cls.TARGET_TRADE_AMOUNT,
            trade_info=dict(
                level=cls.get_user_trade_level(cls.TRADE_VOLUMES, trade_amount),
                start_at=int(date_to_datetime(start_date).timestamp()) if start_date else 0,
                end_at=int(date_to_datetime(end_date).timestamp()) if end_date else 0
            )
        )


@ns.route('/wallacy/rewards')
@respond_with_code
class WallacyRewardsResource(Resource, WallacyActivityMixin):
    GiftTiming = WallacyActivityGiftHistory.GiftTiming
    GiftType = WallacyActivityGiftHistory.GiftType

    GIFT_TIMING_TYPE_MAPPER = {
        # 赛制, 奖品类型:  (排名， 数字货币数量/卡券面额, 币种/卡券面额类型)
        GiftTiming.GROUP_STAGE: {
            GiftType.CET: (range(18, 58), 55, "CET"),
            GiftType.COUPON: (range(58, 88), 5, "USDT"),
        },
        GiftTiming.ROUND_OF_16: {
            GiftType.CET: (range(21, 71), 55, "CET"),
            GiftType.COUPON: (range(71, 121), 5, "USDT"),
        },
        GiftTiming.QUARTER_FINALS: {
            GiftType.CET: (range(29, 99), 55, "CET"),
            GiftType.COUPON: (range(99, 204), 5, "USDT"),
        },
        GiftTiming.SEMI_FINALS: {
            GiftType.CET: (range(34, 106), 90, "CET"),
            GiftType.COUPON: (range(106, 206), 10, "USDT")
        },
        GiftTiming.FINAL: {
            GiftType.CET: (range(192, 492), 145, "CET"),
            GiftType.COUPON: (range(492, 652), 10, "USDT"),
        }
    }

    GIFT_TIMING_RANK_MAPPER = {
        (k, i): (g, t[1], t[2]) for k, v in GIFT_TIMING_TYPE_MAPPER.items() for g, t in v.items() for i in t[0]
    }

    TIMING_SEND_DATE_MAPPER = {
        GiftTiming.GROUP_STAGE: (date(2024, 6, 14), date(2024, 6, 26)),
        GiftTiming.ROUND_OF_16: (date(2024, 6, 29), date(2024, 7, 2)),
        GiftTiming.QUARTER_FINALS: (date(2024, 7, 5), date(2024, 7, 6)),
        GiftTiming.SEMI_FINALS: (date(2024, 7, 9), date(2024, 7, 10)),
        GiftTiming.FINAL: (date(2024, 7, 14), date(2024, 7, 14))
    }

    @classmethod
    def check_gift_timing_date(cls, gift_timing, gift_date):
        start_date, end_date = cls.TIMING_SEND_DATE_MAPPER.get(gift_timing)
        if gift_date > today():
            raise InvalidArgument(message=f"Invalid gift date: {gift_date.strftime('%Y-%m-%d')}")
        if not (start_date <= gift_date <= end_date):
            raise InvalidArgument(message=f"Invalid gift date: {gift_date.strftime('%Y-%m-%d')}")

    @classmethod
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
        gift_timing=EnumField(enum=WallacyActivityGiftHistory.GiftTiming, required=True),
        gift_date=TimestampField(is_ms=False, required=True, to_date=True),
        ranking=fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """派发奖励"""
        code, ranking = kwargs['code'], kwargs['ranking']
        gift_timing, gift_date = kwargs['gift_timing'], kwargs['gift_date']
        cls.check_wallacy_params(code)
        cls.check_gift_timing_date(gift_timing, gift_date)
        wallacy_user = WallacyActivityUser.query.filter(
            WallacyActivityUser.code == code,
        ).first()
        if not wallacy_user:
            raise RecordNotFound(message="Wallacy user not found")
        ranking_history = WallacyActivityGiftHistory.query.filter(
            WallacyActivityGiftHistory.gift_timing == gift_timing,
            WallacyActivityGiftHistory.gift_date == gift_date,
            WallacyActivityGiftHistory.ranking == ranking,
        ).first()

        if ranking_history:
            raise InvalidArgument(message="Gift has been sent")

        user_history = WallacyActivityGiftHistory.query.filter(
            WallacyActivityGiftHistory.user_id == wallacy_user.user_id,
            WallacyActivityGiftHistory.gift_date == gift_date,
            WallacyActivityGiftHistory.gift_timing == gift_timing,
        ).first()

        if user_history:
            raise InvalidArgument(message="User has received the gift")

        gift_data = cls.GIFT_TIMING_RANK_MAPPER.get((gift_timing, ranking), (None, None, None))
        if not all(gift_data):
            current_app.logger.error(f"git timing: {gift_timing}, ranking: {ranking}, gift_data: {gift_data}")
            raise InvalidArgument(message="Invalid gift timing or ranking")
        gift_type, amount, asset = gift_data
        new_history = WallacyActivityGiftHistory(
            user_id=wallacy_user.user_id,
            gift_timing=gift_timing,
            ranking=ranking,
            gift_type=gift_type,
            gift_date=gift_date,
            amount=amount,
            asset=asset,
        )
        db.session_add_and_commit(new_history)


@ns.route('/coinex-wallet/traffic')
@respond_with_code
class CoinexWalletTrafficResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        platform=EnumField(Traffic.Platform, required=True),
    ))
    def get(cls, **kwargs):
        lang = get_request_language()
        return TrafficCache().read_by_lang(lang, kwargs['platform'])


@ns.route('/vertus')
@respond_with_code
class VertusActivityResource(Resource, VertusActivityMixin):
    """ 供 vertus 第三方调用 """
    @classmethod
    @ns.use_kwargs(dict(
        code=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """活动用户详情"""
        code = kwargs['code']
        cls.check_params(code)
        activity_user = cls.get_valid_user(code)
        user_id = activity_user.user_id
        if not cls.check_user_new_ip_and_device(user_id):
            raise NoviceRiskControl(message="risk control")
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument(message="code invalid")
        return dict(
            code=code,
            is_kyc=user.kyc_status == User.KYCStatus.PASSED,
            is_deposit=cls.has_deposit(user_id),
            is_traded=cls.has_trade(user_id),
        )


class DepositBonusActivityConditionMixin(ActivityUserConditionMixin):

    @classmethod
    @cached(60 * 10)
    def _get_trade_before_start_user_list(cls, activity_id):
        model = DepositBonusActivityStatistic
        statistic = model.query.filter(
            model.activity_config_id == activity_id,
            model.business_type == model.BusinessType.TRADE_BEFORE_START,
        ).first()
        if not statistic:
            return set()
        return set(statistic.get_user_ids())

    @classmethod
    def format_equity_details(cls, equity_details: dict) -> dict:
        new_equity_details = {}
        for k, v in equity_details.items():
            equity_type = v['type']
            equity_type_enum = equity_type if isinstance(equity_type, Enum) else EquityType[v['type']]
            v['type'] = _(equity_type_enum.value)
            new_equity_details[k] = v
        return new_equity_details


@ns.route('/deposit-bonus')
@respond_with_code
class DepositBonusActivityListResource(DepositBonusActivityConditionMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=5),
        page=PageField(missing=1),
    ))
    def get(cls, **kwargs):
        """
        充值福利-活动列表
        """
        page = kwargs['page']
        limit = kwargs['limit']
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        result = cls._get(lang)
        data = result[(page - 1) * limit: page * limit]
        apply_ids = {coupon['apply_id'] for x in data for coupon in x['gift_coupons']}
        equity_ids = {equity['equity_id'] for x in data for equity in x['gift_equities']}
        coupon_mapping, equity_mapping = {}, {}
        if apply_ids:
            coupon_mapping = get_airdrop_coupon_details(apply_ids)
        if equity_ids:
            equity_mapping = EquityCenterService.batch_query_equity_basic_info(equity_ids)
        return dict(
            data=data,
            extra=dict(
                coupon_mapping=coupon_mapping,
                equity_mapping=cls.format_equity_details(equity_mapping)
            ),
            count=len(data),
            total=len(result),
        )

    @classmethod
    @mem_cached(120)
    def _get(cls, lang):
        if data := DepositBonusActivityCache(lang).read():
            res = json_loads(data)
            for row in res:
                row['active_status'] = cls.get_active_status(row)
            res.sort(key=cls.sorter)
            return res
        return []

    @classmethod
    def get_active_status(cls, act):
        start_time = timestamp_to_datetime(act['start_time'])
        end_time = timestamp_to_datetime(act['end_time'])
        return DepositBonusActivity(start_time=start_time, end_time=end_time).active_status.name

    @classmethod
    def sorter(cls, act):
        now_ = current_timestamp(to_int=True)
        if act['start_time'] <= now_ < act['end_time']:
            return 0, -act['end_time'], -act['id']
        else:
            return 1, -act['end_time'], -act['id']


@ns.route('/deposit-bonus/<int:activity_id>')
@respond_with_code
class DepositBonusDetailResource(DepositBonusActivityConditionMixin, Resource):

    @classmethod
    def get(cls, activity_id):
        """ 充值福利-活动详情 """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        detail = DepositBonusActivityDetailCache.get(activity_id, lang)
        if not detail:
            raise InvalidArgument
        res = json_loads(detail)
        res['equity_details'] = cls.format_equity_details(res['equity_details'])
        return res

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        config_id=fields.Integer(required=True),
    ))
    def post(cls, activity_id, **kwargs):
        """ 充值福利-用户报名 """
        user_id = g.user.id
        if UserRepository.is_abnormal_user(user_id):
            raise ActivityAbnormalUserError(message=_("你的账户暂被限制参与活动，如有疑问请联系客服。"))

        config_id = kwargs['config_id']
        model = DepositBonusActivity
        activity = model.query.filter(
            model.status == model.StatusType.ONLINE,
            model.id == activity_id,
        ).first()
        if not activity:
            raise InvalidArgument
        if not activity.active_status == model.ActiveStatus.STARTED:
            raise InvalidArgument(message=_("你未满足条件或不在活动时间内，请刷新后重试"))
        cfg = DepositBonusActivityConfig.query.get(config_id)
        if not cfg:
            raise InvalidArgument
        row = DepositBonusActivityApplyUser.query.filter(
            DepositBonusActivityApplyUser.deposit_bonus_id == activity_id,
            DepositBonusActivityApplyUser.activity_id == cfg.id,
            DepositBonusActivityApplyUser.user_id == user_id,
        ).first()
        if row:
            raise InvalidArgument
        conditions = {
            i.key: i.value for i in DepositBonusActivityCondition.query.filter(
                DepositBonusActivityCondition.activity_config_id == cfg.id).all()
        }
        cls.check_condition(user_id, cfg.id, conditions)

        with CacheLock(LockKeys.deposit_bonus_activity(activity.id, cfg.id, user_id), wait=False):
            db.session.rollback()
            row = DepositBonusActivityApplyUser(
                user_id=user_id,
                deposit_bonus_id=activity.id,
                activity_id=cfg.id
            )
            db.session.add(row)
            db.session.commit()


@ns.route('/deposit-bonus/user-info')
@respond_with_code
class DepositBonusActivityUserInfoResource(DepositBonusActivityConditionMixin, Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        activity_id=fields.Integer(required=True),
    ))
    def get(cls, **kwargs):
        """ 用户参与充值福利活动的信息 """
        activity_id = kwargs['activity_id']
        user_id = g.user.id
        activity = DepositBonusActivity.query.get(activity_id)
        if not activity:
            raise InvalidArgument
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        cache_detail = DepositBonusActivityDetailCache.get(activity_id, lang)
        if not cache_detail:
            raise InvalidArgument
        detail = json_loads(cache_detail)
        activity_configs = detail['activity_configs']
        apply_configs_users = cls._get_apply_configs_users(user_id, activity_id)
        ret = defaultdict(dict)
        for cfg in activity_configs:
            conditions_query = {
                i.key: i.value for i in DepositBonusActivityCondition.query.filter(
                    DepositBonusActivityCondition.activity_config_id == cfg['id']
                ).all()
            }
            conditions = cls.get_condition_info(user_id, activity_id, conditions_query)
            user_gifts = cls._get_user_gifts(user_id, cfg['id'], activity_id)
            ret[cfg['id']].update({
                'idx': cfg['idx'],
                'id': cfg['id'],
                'joined': bool(apply_configs_users.get(cfg['id'])),
                'conditions': conditions,
                **user_gifts
            })
        return ret

    @classmethod
    def _get_apply_configs_users(cls, user_id: int, activity_id: int) -> dict:
        model = DepositBonusActivityApplyUser
        rows = model.query.with_entities(
            model.activity_id,
            model.user_id,
        ).filter(
            model.deposit_bonus_id == activity_id,
            model.user_id == user_id,
        ).all()
        return dict(rows)

    @classmethod
    def _get_user_gifts(cls, user_id: int, cfg_id: int, activity_id: int) -> dict:
        model = DepositBonusActivityUserInfo
        row = model.query.filter(
            model.deposit_bonus_id == activity_id,
            model.activity_id == cfg_id,
            model.user_id == user_id,
        ).first()
        ret = {
            'rank': None,
            'amount': 0,
            'net_amount': 0,
            'gifts': [],
            'status': model.Status.VALID.name,
            'gift_status': model.GiftStatus.NONE.name,
            'invalid_type': model.InvalidType.NONE.name,
        }
        if not row:
            return ret
        ret.update({
            'rank': row.rank,
            'amount': amount_to_str(row.amount, PrecisionEnum.COIN_PLACES),
            'net_amount': amount_to_str(row.net_amount, PrecisionEnum.COIN_PLACES),
            'gifts': row.get_gifts(),
            'status': row.status.name,
            'gift_status': row.gift_status.name,
            'invalid_type': row.invalid_type.name,
        })
        return ret


@ns.route('/deposit-bonus/history')
@respond_with_code
class DepositBonusActivityRewordResource(DepositBonusActivityConditionMixin, Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=fields.Integer(required=True, default=1),
        limit=fields.Integer(required=True, default=5),
    ))
    def get(cls, **kwargs):
        """ 用户的充值福利参与记录 """
        user_id = g.user.id
        page = kwargs['page']
        limit = kwargs['limit']
        model = DepositBonusActivityApplyUser
        paginate = model.query.with_entities(
            model.deposit_bonus_id
        ).filter(
            model.user_id == user_id,
        ).group_by(
            model.deposit_bonus_id,
            model.user_id,
        ).order_by(
            model.id.desc()
        ).paginate(page, limit, error_out=False)
        items = list(paginate.items)
        if not items:
            return dict(data=[], extra={}, total=paginate.total)

        activity_ids = [i.deposit_bonus_id for i in items]
        lang = Language(g.lang)
        details = DepositBonusActivityContent.query.with_entities(
            DepositBonusActivityContent.deposit_bonus_id,
            DepositBonusActivityContent.title
        ).filter(
            DepositBonusActivityContent.deposit_bonus_id.in_(activity_ids),
            DepositBonusActivityContent.lang == lang
        ).all()
        detail_map = dict(details)
        sum_gift_assets, sum_gift_coupons, sum_gift_equities = DepositBonusActivityConfig.get_configs_by_sum_gift(activity_ids)
        my_gift_assets, my_gift_coupons, my_gift_equities = cls._get_my_gifts(user_id, activity_ids)
        apply_ids = {kk for v in sum_gift_coupons.values() for kk, _ in v.items()}
        equity_ids = {kk for v in sum_gift_equities.values() for kk, _ in v.items()}
        equity_mapping = EquityCenterService.batch_query_equity_basic_info(equity_ids)
        coupon_mapping = get_airdrop_coupon_details(apply_ids)
        ret = []
        for item in items:
            deposit_bonus_id = item.deposit_bonus_id
            gift_assets = []
            for asset, amount in sum_gift_assets[deposit_bonus_id].items():
                gift_assets.append(dict(
                    asset=asset,
                    amount=amount,
                ))
            gift_coupons = []
            for apply_id, amount in sum_gift_coupons[deposit_bonus_id].items():
                gift_coupons.append(dict(
                    apply_id=apply_id,
                    amount=amount,
                ))
            gift_equities = []
            for equity_id, amount in sum_gift_equities[deposit_bonus_id].items():
                gift_equities.append(dict(
                    equity_id=equity_id,
                    amount=amount,
                ))
            _my_gift_assets = []
            for asset, amount in my_gift_assets[deposit_bonus_id].items():
                _my_gift_assets.append(dict(
                    asset=asset,
                    amount=amount,
                ))
            _my_gift_coupons = []
            for apply_id, amount in my_gift_coupons[deposit_bonus_id].items():
                _my_gift_coupons.append(dict(
                    apply_id=apply_id,
                    amount=amount,
                ))
            _my_gift_equities = []
            for equity_id, amount in my_gift_equities[deposit_bonus_id].items():
                _my_gift_equities.append(dict(
                    equity_id=equity_id,
                    amount=amount,
                ))
            ret.append({
                'id': deposit_bonus_id,
                'title': detail_map.get(deposit_bonus_id),
                'gift_assets': gift_assets,
                'gift_coupons': gift_coupons,
                'gift_equities': gift_equities,
                'my_gifts': {
                    'gift_assets': _my_gift_assets,
                    'gift_coupons': _my_gift_coupons,
                    'gift_equities': _my_gift_equities,
                },
            })
        return dict(data=ret, extra=dict(
            equity_mapping=cls.format_equity_details(equity_mapping),
            coupon_mapping=coupon_mapping,
        ), total=paginate.total)

    @classmethod
    def _get_my_gifts(cls, user_id: int, activity_ids: list[int]) -> tuple[dict, dict, dict]:
        model = DepositBonusActivityUserInfo
        rows = model.query.filter(
            model.user_id == user_id,
            model.deposit_bonus_id.in_(activity_ids)
        ).all()
        gift_assets = defaultdict(lambda: defaultdict(Decimal))
        gift_coupons = defaultdict(lambda: defaultdict(int))
        gift_equities = defaultdict(lambda: defaultdict(int))
        for row in rows:
            if row.status is not model.Status.VALID:
                continue
            if row.gift_status != model.GiftStatus.FINISHED:
                continue
            for asset, amount in row.get_gift_assets().items():
                gift_assets[row.deposit_bonus_id][asset] += amount
            for apply_id, amount in row.get_gift_coupons().items():
                gift_coupons[row.deposit_bonus_id][apply_id] += amount
            for equity_id, amount in row.get_gift_equities().items():
                gift_equities[row.deposit_bonus_id][equity_id] += amount
        return gift_assets, gift_coupons, gift_equities


@ns.route('/deposit-bonus/banners')
@respond_with_code
class DepositBonusActivityBannersResource(Resource):

    @classmethod
    def get(cls):
        """
        充值福利-活动 banners
        """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        return cls._get(lang)

    @classmethod
    @mem_cached(120)
    def _get(cls, lang):
        if data := DepositBonusActivityBannerCache(lang).read():
            return json_string_success(data)
        return []


@ns.route("/deposit-bonus/opening-agreement")
@respond_with_code
class DepositBonusOpeningAgreementResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user = g.user
        result = {
            "opening_agreement": UserPreferences(user.id).opening_deposit_bonus,
            "is_abnormal_user": UserRepository.is_abnormal_user(user.id),
        }
        return result

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        user = g.user
        if UserRepository.is_abnormal_user(user.id):
            raise ActivityAbnormalUserError(message=_("你的账户暂被限制参与活动，如有疑问请联系客服。"))
        pref = UserPreferences(user.id)
        if not pref.opening_deposit_bonus:
            pref.opening_deposit_bonus = True


@ns.route("/perpetual-profit-real/rank")
@respond_with_code
class PerpetualProfitRealRankResource(Resource):
    """合约盈亏排行榜"""

    @classmethod
    @ns.use_kwargs(dict(
        cycle_type=EnumField(['month', 'year'], required=True),
    ))
    def get(cls, **kwargs):
        if user := get_request_user():
            user_id = user.id
        else:
            user_id = 0
        if kwargs['cycle_type'] == 'month':
            cache = DirectPerpetualProfitRealMonthRankCache
        else:
            cache = DirectPerpetualProfitRealYearRankCache
        result = []
        for item in json.loads(cache().read() or '[]'):
            item['is_mark'] = item['user_id'] == user_id
            del item['user_id']
            del item['real_username']
            del item['real_avatar']
            result.append(item)
        return result


@ns.route("/perpetual-profit-real/anonymous")
@respond_with_code
class PerpetualProfitRealAnonymousResource(Resource):
    """合约盈亏排行榜中是否开启匿名"""

    @classmethod
    @require_login(allow_sub_account=True)
    def get(cls):
        return dict(
            anonymous=UserPreferences(g.user.id).perpetual_profit_real_rank_anonymous,
        )

    @classmethod
    @require_login(allow_sub_account=True)
    def post(cls):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        new_flag = not pref.perpetual_profit_real_rank_anonymous
        pref.perpetual_profit_real_rank_anonymous = new_flag
        cls.update_cache(DirectPerpetualProfitRealMonthRankCache(), user_id, new_flag)
        cls.update_cache(DirectPerpetualProfitRealYearRankCache(), user_id, new_flag)
        return dict(
            anonymous=new_flag,
        )

    @classmethod
    def update_cache(cls, cache, user_id: int, anonymous: bool):
        set_flag = False
        data = json.loads(cache.read() or '[]')
        for item in data:
            if item['user_id'] != user_id:
                continue

            real_username = item['real_username']
            real_avatar = item['real_avatar']
            if anonymous:
                item['username'] = hide_text_default(real_username)
                item['avatar'] = UserRepository.get_system_default_avatar_url()
            else:
                item['username'] = real_username
                item['avatar'] = real_avatar

            set_flag = True
            break

        if set_flag:
            cache.set_data(data)
