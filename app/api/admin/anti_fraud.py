import datetime
from enum import Enum
from flask import g, request
from webargs import fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import <PERSON>Field, LimitField, EnumField
from app.business import AntiFraudClient, get_admin_user_name_map
from app.business.lock_asset import get_user_id_from_email
from app.business.utils import fetch_user_emails
from app.exceptions import InvalidArgument
from app.models import User
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectRisk
from app.utils import timestamp_to_datetime, export_xlsx, batch_iter
from app.utils.date_ import datetime_to_utc8_str
from app.utils.export import export_csv
from app.utils.importer import get_table_rows

ns = Namespace("Anti-fraud")


@ns.route("/risk-user")
@respond_with_code
class AntiFraudRiskUserListResource(Resource):

    export_headers = (
        {'field': 'user_id', Language.ZH_HANS_CN: '用户ID'},
        {'field': 'email', Language.ZH_HANS_CN: '邮箱'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'},
        {'field': 'edit_name', Language.ZH_HANS_CN: '操作人'},
        {'field': 'updated_at', Language.ZH_HANS_CN: '操作时间'},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """风控-羊毛党-黑名单列表"""
        client = AntiFraudClient()
        export = kwargs['export']
        data = client.get_risk_user(
            user_id=kwargs.get("user_id"),
            page=kwargs['page'],
            limit=kwargs['limit'],
            export=export
        )
        risk_users = data['items']
        user_ids = {i['user_id'] for i in risk_users}
        edit_user_ids = {i['edit_user_id'] for i in risk_users}
        admin_user_mapper = get_admin_user_name_map(list(edit_user_ids))
        email_mapper = fetch_user_emails(user_ids)
        result = []
        for user in risk_users:
            user_id = user['user_id']
            edit_user_id = user['edit_user_id']
            result.append(dict(
                user_id=user_id,
                email=email_mapper.get(user_id),
                edit_user_id=edit_user_id,
                edit_name=admin_user_mapper.get(edit_user_id, "系统"),
                updated_at=timestamp_to_datetime(user['updated_at']).strftime("%Y-%m-%d %H:%M:%S")
                if export else user['updated_at'],
                remark=user['reason']
            ))
        if export:
            return export_xlsx(
                filename="anti-fraud-risk-user",
                data_list=result,
                export_headers=cls.export_headers,
            )

        return dict(
            total=data['total'],
            items=result
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        remark=fields.String,
    ))
    def post(cls, **kwargs):
        """风控-羊毛党-增加黑名单"""
        client = AntiFraudClient()
        client.add_or_update_risk_user(
            user_id=kwargs['user_id'],
            edit_user_id=g.user.id,
            remark=kwargs.get("remark")
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudBlocklist,
            detail=kwargs,
            target_user_id=kwargs['user_id'],
        )


@ns.route("/risk-user/<int:user_id>")
@respond_with_code
class AntiFraudRiskUserDetailResource(Resource):

    @classmethod
    def delete(cls, user_id):
        """风控-羊毛党-删除黑名单"""
        client = AntiFraudClient()
        client.delete_risk_user(user_id, g.user.id)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudBlocklist,
            detail=dict(user_id=user_id),
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String
    ))
    def put(cls, user_id, **kwargs):
        """风控-羊毛党-修改黑名单"""
        client = AntiFraudClient()
        client.add_or_update_risk_user(
            user_id=user_id,
            edit_user_id=g.user.id,
            remark=kwargs.get("remark")
        )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudBlocklist,
            old_data=dict(remark=''),
            new_data=dict(remark=kwargs.get('remark')),
            target_user_id=user_id,
        )


@ns.route("/risk-user/template")
@respond_with_code
class AntiFraudRiskUserTemplateResource(Resource):

    export_headers = (
        {'field': 'email_userid', Language.ZH_HANS_CN: '邮箱/ID'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'}
    )

    @classmethod
    def get(cls):
        """风控-羊毛党-下载黑名单模板"""
        return export_xlsx(
            filename='risk-user-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route("/risk-user/import")
@respond_with_code
class AntiFraudRiskUserImportResource(Resource):

    @classmethod
    def post(cls):
        """风控-羊毛党-批量导入黑名单"""
        if not (file := request.files.get('file')):
            raise InvalidArgument
        client = AntiFraudClient()
        rows = get_table_rows(file, ['email_userid', "remark"])
        risk_users = []
        for row in rows:
            user_id = get_user_id_from_email(row['email_userid'])
            risk_users.append(dict(
                user_id=user_id,
                remark=row['remark']
            ))

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AntiFraudBlocklist,
                detail=dict(user_id=user_id, remark=row['remark']),
                target_user_id=user_id,
            )
        client.import_risk_users(g.user.id, risk_users)


@ns.route("/feature")
@respond_with_code
class FeatureResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String,
        feature_type=fields.String,
        identity_type=fields.String,
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表"""

        client = AntiFraudClient()
        data = client.get_feature_list(
            feature_name=kwargs.get("feature_name"),
            feature_type=kwargs.get("feature_type"),
            identity_type=kwargs.get("identity_type"),
            page=kwargs['page'],
            limit=kwargs['limit'],
        )

        return dict(
            total=data['total'],
            items=data['items'],
            extra=data['extra'],
        )

    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String(required=True),
        description=fields.String,
    ))
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->修改特征描述"""
        client = AntiFraudClient()
        client.update_feature(kwargs['feature_name'], kwargs.get('description'))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudFeature,
            old_data=dict(description=''),
            new_data=dict(description=kwargs.get('description')),
            special_data=dict(feature_name=kwargs['feature_name']),
        )


@ns.route("/feature/config")
@respond_with_code
class FeatureConfigResource(Resource):
    @classmethod
    def get_user_email_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            res.update({user.id: user.email for user in users})
        return res

    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String(required=True),
        config_name=fields.String(required=True),
        config_key=fields.String,
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->特征配置列表"""
        if kwargs.get('export'):
            kwargs.pop('config_key', "")
            kwargs.pop('page', "")
            kwargs.pop('limit', "")

        client = AntiFraudClient()
        data = client.get_feature_config_list(**kwargs)

        match data['extra'].get('config_key_type'):
            case "user":
                user_ids = []
                for config in data['items']:
                    user_ids.append(config['config_key'])
                user_email_dict = cls.get_user_email_dict(user_ids)
                print(user_email_dict)
                for config in data['items']:
                    if email := user_email_dict.get(int(config['config_key'])):
                        config['user_info'] = f'{config["config_key"]} - {email}'

            case _:
                pass

        edit_user_ids = [config['edit_user_id'] for config in data['items'] if config.get('edit_user_id')]
        edit_user_email_dict = cls.get_user_email_dict(edit_user_ids)

        for config in data['items']:
            if config.get('edit_user_id') and (email := edit_user_email_dict.get(int(config['edit_user_id']))):
                config['edit_user_email'] = email

            else:
                config['edit_user_email'] = ''

            if config.get('updated_at'):
                config['updated_at'] = datetime_to_utc8_str(timestamp_to_datetime(int(config['updated_at'])))
            else:
                config['updated_at'] = '-'

        if kwargs.get('export'):
            feature_name_cn = data['extra']['feature_name_cn']
            config_name_cn = data['extra']['config_name_cn']
            export_headers = (
                {'field': 'config_key', Language.ZH_HANS_CN: '配置key'},
                {'field': 'config_value', Language.ZH_HANS_CN: '配置value'},
                {'field': 'desc', Language.ZH_HANS_CN: '备注'},
                {'field': 'edit_user_email', Language.ZH_HANS_CN: '最后操作人'},
                {'field': 'updated_at', Language.ZH_HANS_CN: '最后操作时间'},
            )
            if data['extra'].get('only_config_key'):
                export_headers = (
                    {'field': 'config_key', Language.ZH_HANS_CN: '配置key'},
                    {'field': 'desc', Language.ZH_HANS_CN: '备注'},
                    {'field': 'edit_user_email', Language.ZH_HANS_CN: '最后操作人'},
                    {'field': 'updated_at', Language.ZH_HANS_CN: '最后操作时间'},
                )

            return export_xlsx(
                filename=f"anti-fraud-feature-config-{feature_name_cn}-{config_name_cn}",
                data_list=data['items'],
                export_headers=export_headers,
            )

        return dict(
            total=data['total'],
            items=data['items'],
            extra=data['extra'],
        )

    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String(required=True),
        config_name=fields.String(required=True),
        config_key=fields.String(required=True),
        config_value=fields.String(allow_none=True),
        desc=fields.String(allow_none=True)
    ))
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->新增/修改特征配置"""
        client = AntiFraudClient()
        kwargs['edit_user_id'] = g.user.id
        client.create_or_update_feature_config(**kwargs)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudFeature,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String(required=True),
        config_name=fields.String(required=True),
        config_key=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->删除特征配置"""
        client = AntiFraudClient()
        client.delete_feature_config(**kwargs)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudFeature,
            detail=kwargs,
        )


@ns.route("/feature/config/import")
@respond_with_code
class AntiFraudFeatureConfigImportResource(Resource):

    @classmethod
    def get_feature_config_info(cls, feature_name, config_name):
        client = AntiFraudClient()
        data = client.get_feature_config_list(
            feature_name=feature_name,
            config_name=config_name,
            limit=1
        )
        return data['extra']

    @classmethod
    @ns.use_kwargs(dict(
        feature_name=fields.String(required=True),
        config_name=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """风控-羊毛党-批量导入特征配置模版"""
        feature_config_info = cls.get_feature_config_info(**kwargs)

        config_key_name = feature_config_info.get('config_name_cn', 'config_key')
        if feature_config_info.get('config_key_type') == 'user':
            config_key_name += '(邮箱/id)'

        if feature_config_info.get('only_config_key'):
            export_headers = (
                {'field': 'config_key', Language.ZH_HANS_CN: config_key_name},
                {'field': 'desc', Language.ZH_HANS_CN: '备注'},
            )
        else:
            export_headers = (
                {'field': 'config_key', Language.ZH_HANS_CN: config_key_name},
                {'field': 'config_value', Language.ZH_HANS_CN: feature_config_info.get('config_value_cn', 'config_value')},
                {'field': 'desc', Language.ZH_HANS_CN: '备注'},
            )

        return export_xlsx(
            filename='risk-user-template',
            data_list=[],
            export_headers=export_headers
        )

    @classmethod
    def post(cls, **kwargs):
        """风控-羊毛党-批量导入特征配置"""
        if not (file := request.files.get('file')):
            raise InvalidArgument

        if not (feature_name := request.form.get('feature_name')):
            raise InvalidArgument

        if not (config_name := request.form.get('config_name')):
            raise InvalidArgument

        feature_config_info = cls.get_feature_config_info(feature_name, config_name)
        only_config_key = feature_config_info.get('only_config_key')
        config_key_type = feature_config_info.get('config_key_type')

        headers = ['config_key', 'config_value', 'desc']
        if only_config_key:
            headers = ['config_key', 'desc']

        client = AntiFraudClient()
        rows = get_table_rows(file, headers)
        feature_configs = {}
        edit_user_id = g.user.id
        for row in rows:
            config_key = row['config_key']
            config_value = '' if only_config_key else row['config_value']
            desc = row['desc']

            if config_key_type == 'user':
                config_key = get_user_id_from_email(config_key)

            config_data = {str(config_key): {
                'config_value': config_value,
                'desc': desc,
                'edit_user_id': edit_user_id,
            }}
            feature_configs.update(config_data)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AntiFraudFeature,
                detail=dict(
                    feature_name=feature_name,
                    config_name=config_name,
                    **config_data
                ),
            )
        client.import_feature_config(feature_name, config_name, feature_configs)


@ns.route("/risk/user")
@respond_with_code
class RiskUserResource(Resource):

    @classmethod
    def get_user_email_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            res.update({user.id: user.email for user in users})
        return res

    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        cal_time=fields.String(allow_none=True),
        user_id=fields.Integer(allow_none=True),
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->风险用户列表"""
        _kwargs = {
            "risk_model_name": kwargs["risk_model_name"],
            "cal_time": kwargs.get("cal_time"),
            "page": kwargs["page"],
            "limit": kwargs["limit"],
        }

        export = kwargs['export']
        if export:
            _kwargs['page'] = 1
            _kwargs['limit'] = 5000
            _kwargs['export'] = True
        else:
            if user_id := kwargs.get("user_id"):
                _kwargs['user_id'] = user_id

        items = []
        client = AntiFraudClient()
        data = client.get_risk_user_list(**_kwargs)
        extra = data['extra']
        total = data['total']

        while data['items']:
            user_ids = [item['user_id'] for item in data['items']]
            user_email_map = cls.get_user_email_dict(user_ids)

            for item in data['items']:
                items.append(dict(
                    user_id=item['user_id'],
                    user_email=user_email_map.get(item['user_id'], ''),
                    risk_model_name=extra['risk_model_name'],
                    risk_model_name_cn=extra['risk_model_name_cn'],
                    first_risk_time=item['first_risk_time'],
                    first_risk_score=item['first_risk_score'],
                    first_risk_reason=item.get('first_risk_reason', ""),
                    last_risk_time=item['last_risk_time'],
                    last_risk_score=item['last_risk_score'],
                    last_risk_reason=item.get('last_risk_reason', ""),
                    description=item['description'],
                ))

            if not export:
                break

            _kwargs['page'] += 1
            data = client.get_risk_user_list(**_kwargs)

        if kwargs.get('export'):
            risk_model_name_cn = extra['risk_model_name_cn']
            export_headers = (
                {'field': 'user_id', Language.ZH_HANS_CN: '用户ID'},
                {'field': 'user_email', Language.ZH_HANS_CN: '用户邮箱'},
                {'field': 'risk_model_name_cn', Language.ZH_HANS_CN: '风险模型'},
                {'field': 'first_risk_time', Language.ZH_HANS_CN: '首次风控时间'},
                {'field': 'first_risk_score', Language.ZH_HANS_CN: '首次风险得分'},
                {'field': 'first_risk_reason', Language.ZH_HANS_CN: '首次风险原因'},
                {'field': 'last_risk_time', Language.ZH_HANS_CN: '最近计算时间'},
                {'field': 'last_risk_score', Language.ZH_HANS_CN: '最近风险得分'},
                {'field': 'last_risk_reason', Language.ZH_HANS_CN: '最近风险原因'},
                {'field': 'description', Language.ZH_HANS_CN: '备注'},
            )

            return export_xlsx(
                filename=f"anti-fraud-risk-user-{risk_model_name_cn}",
                data_list=items,
                export_headers=export_headers,
            )

        return dict(
            total=total,
            items=items,
            extra=extra,
        )

    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        risk_user_id=fields.Integer(required=True),
        description=fields.String,
    ))
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->修改风险用户描述"""
        client = AntiFraudClient()
        client.update_risk_user(kwargs['risk_model_name'], kwargs['risk_user_id'], kwargs.get('description'))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudRiskUser,
            old_data=dict(description=''),
            new_data=dict(description=kwargs.get('description')),
            special_data=dict(risk_model_name=kwargs['risk_model_name']),
            target_user_id=kwargs['risk_user_id'],
        )


@ns.route("/user-risk-info")
@respond_with_code
class UserRiskInfoResource(Resource):

    class ManualAnnotation(Enum):  # 人工标注类型
        NONE = 'none'
        RISK = 'risk'
        NORMAL = 'normal'

    @classmethod
    def get_user_email_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            res.update({user.id: user.email for user in users})
        return res

    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        start_time=fields.String(allow_none=True),
        end_time=fields.String(allow_none=True),
        user_id=fields.Integer(allow_none=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->风险用户信息列表"""

        """
        用户id，用户名，用户首次风控时间，用户首次风控特征，用户最后一次风险计算时间，用户最后一次风险计算特征，人工标注，人工标注备注
        人工标注表导入表头：用户ID，标注时间，标注类型（正向/反向），标注备注
        交叉对比表导出表头：用户ID，被模型1标注时间，模型1标注对比人工标注覆盖率，被模型2标注时间，模型2标注对比人工标注覆盖率，，，被人工标注时间    
        """

        _kwargs = {
            "risk_model_name": kwargs["risk_model_name"],
            "user_id": kwargs.get("user_id"),
            "page": kwargs["page"],
            "limit": kwargs["limit"],
        }

        if kwargs.get("start_time"):
            _kwargs['start_time'] = kwargs['start_time']

        if kwargs.get("end_time"):
            _kwargs['end_time'] = kwargs['end_time']

        items = []
        client = AntiFraudClient()
        data = client.get_risk_user_info_list(**_kwargs)
        extra = data['extra']
        total = data['total']

        user_email_map = cls.get_user_email_dict([item['user_id'] for item in data['items']])
        for item in data['items']:
            items.append(dict(
                user_id=item['user_id'],
                user_email=user_email_map.get(item['user_id'], ''),
                first_risk_time=item['first_risk_time'],
                first_risk_score=item['first_risk_score'],
                first_risk_reason=item.get('first_risk_reason', ""),
                last_risk_time=item['last_risk_time'],
                last_risk_score=item['last_risk_score'],
                last_risk_reason=item.get('last_risk_reason', ""),
                manual_annotation=item.get('manual_annotation', ""),
                manual_annotation_at=item.get('manual_annotation_at'),
                manual_annotation_reason=item.get('manual_annotation_reason', ""),
                description=item['description'],
            ))

        return dict(
            total=total,
            items=items,
            extra=extra,
        )

    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        user_id=fields.Integer(required=True),
        manual_annotation=EnumField(ManualAnnotation, required=True),
        manual_annotation_at=fields.String(allow_none=True),
        manual_annotation_reason=fields.String(missing="")
    ))
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->人工标注"""
        client = AntiFraudClient()
        client.import_risk_user_info(kwargs['risk_model_name'], {
            kwargs['user_id']: [
                kwargs['manual_annotation'].name,
                kwargs['manual_annotation_at'],
                kwargs['manual_annotation_reason'],
            ]
        })


@ns.route("/user-risk-info/manual-annotation/import")
@respond_with_code
class UserRiskInfoManualAnnotationImportResource(Resource):

    export_headers = (
        {'field': 'user_id', Language.ZH_HANS_CN: '用户id'},
        {'field': 'manual_annotation_type', Language.ZH_HANS_CN: '标注类型(risk/normal)'},
        {'field': 'manual_annotation_at', Language.ZH_HANS_CN: '标注时间'},
        {'field': 'manual_annotation_at', Language.ZH_HANS_CN: '标注原因'},
    )

    @classmethod
    def get(cls):
        """风控-羊毛党-下载导入标注模板"""
        return export_xlsx(
            filename='user-risk-info-template',
            data_list=[],
            export_headers=cls.export_headers
        )

    @classmethod
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->导入标注"""
        risk_model_name = request.form.get('risk_model_name')
        if not risk_model_name:
            raise InvalidArgument

        if not (file := request.files.get('file')):
            raise InvalidArgument
        rows = get_table_rows(file, [
            'user_id', "manual_annotation_type", "manual_annotation_at", "manual_annotation_reason"
        ])
        manual_annotation_infos = {}
        client = AntiFraudClient()
        for row in rows:
            manual_annotation_at = row['manual_annotation_at']
            if isinstance(manual_annotation_at, datetime.datetime):
                manual_annotation_at = manual_annotation_at.strftime('%Y-%m-%d %H:%M:%S')
            manual_annotation_infos[row['user_id']] = [
                row['manual_annotation_type'],
                manual_annotation_at,
                row['manual_annotation_reason'],
            ]

            # description 比较大，分批导入
            if len(manual_annotation_infos) == 100:
                client.import_risk_user_info(risk_model_name, manual_annotation_infos)
                manual_annotation_infos = {}
        client.import_risk_user_info(risk_model_name, manual_annotation_infos)


@ns.route("/user-risk-info/export")
class UserInfoRiskExportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        start_time=fields.String(required=True),
        end_time=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->导出风险用户信息"""
        client = AntiFraudClient()
        data = client.export_risk_user_info(kwargs['risk_model_name'], kwargs['start_time'],kwargs['end_time'])
        return export_csv(data['data'], data['headers'], f"anti-fraud-risk-user-info-{kwargs['risk_model_name']}")


@ns.route("/risk/model/list")
@respond_with_code
class RiskModelListResource(Resource):
    @classmethod
    def get(cls, **kwargs):
        """ 风控->反欺诈系统->风险模型列表"""
        client = AntiFraudClient()
        return client.get_risk_model_list()


@ns.route("/risk/model")
@respond_with_code
class RiskModelResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        risk_model_name=fields.String(required=True),
        description=fields.String,
    ))
    def post(cls, **kwargs):
        """ 风控->反欺诈系统->特征列表->修改风险模型描述"""
        client = AntiFraudClient()
        client.update_risk_model(kwargs['risk_model_name'], kwargs.get('description'))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AntiFraudRiskModel,
            special_data=kwargs,
        )
