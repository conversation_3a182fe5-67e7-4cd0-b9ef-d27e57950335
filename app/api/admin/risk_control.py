#!/usr/bin/env python3
import re
from enum import Enum
import json
from collections import defaultdict
from decimal import Decimal
from itertools import chain
from typing import List, Dict, Tuple

from flask_api.exceptions import NotFound
from flask import g, request
from flask_restx import marshal
from flask_restx import fields as mash_fields
from sqlalchemy import or_, func
from webargs import fields

from app.business.auth import get_admin_user_name_map
from app.business.balance.helper import UserTotalBalanceHelper
from app.utils.importer import get_table_rows

from ..common.fields import LimitField, AssetField, PageField, PositiveDecimalField, \
    DecimalType, TimestampField, EnumField
from ..common.request import RequestPlatform
from ... import Language
from ...assets import list_all_assets, asset_to_chains
from ...assets import AssetChainConfig as AssetChainConfigControl
from ...business import TradeLogDB, PerpetualSysHistoryDB, CacheLock, LockKeys, UserPreferences, ExchangeLogDB
from ...business.margin.helper import Mar<PERSON><PERSON><PERSON>unt<PERSON>elper
from ...business.order import PriceVerifySettings
from ...business.risk_control.pledge import check_pledge_loan_flat_task
from ...business.user import UserSettings, get_viabtc_deposit_whitelist_users
from ...business.risk_control import (FailOption,
                                      check_investment_balance_task,
                                      check_margin_loan_flat_task,
                                      check_perpetual_balance_task,
                                      check_red_packet_remain_amount_task,
                                      check_p2p_balance_task,
                                      try_unblock_permissions,
                                      process_permissions_task)
from ...business.risk_control.base import (
    RiskControlGroupConfig, MarketVolatilityRiskConfig,
    WithdrawalFuseConfig, admin_block_permissions, WashSaleRiskConfig, DepositFuseConfig, get_user_setting_risk_query,
)
from ...business.risk_control.market import IndexPriceRiskControlHelper
from ...business.risk_control.pre_trading import check_pre_market_settle_task
from ...business.risk_control.withdrawal import WithdrawalAuditHelper
from ...business.prices import PriceManager
from ...caches import PerpetualCoinTypeCache, MarketCache, PerpetualMarketCache
from ...caches.p2p import P2pAssetConfigCache
from ...caches.statistics import WithdrawalFuseStatisticsCache, NewDepositFuseStatisticsCache
from ...caches.user import CountrySmsRiskCodeCache
from ...common import PrecisionEnum, ADMIN_EXPORT_LIMIT, OrderBusinessType, OrderType
from ...exceptions import InvalidArgument, RecordNotFound
from ...models import (
    AssetInvestmentConfig, InvestmentBalanceCheck,
    KycVerification, MarginAccount, MarginLoanFlatCheck,
    PerpetualBalanceCheck, RedPacketCheck, RiskUser, User, UserSetting,
    db, ViaBTCRiskControlConfig, DailyViaBTCPoolOrderSummary,
    ViaBTCPoolOrder, AssetPrice, RiskEventLog, Market,
    RiskUserSource, PerpetualMarket, OperationLog, AssetAccumulatedDepositConfig,
    SpecialAssetAccumulatedDepositConfig, P2pBalanceCheck, AssetChainConfig, CountrySmsRiskConfig, AdminUser,
)
from ...models.pledge import LoanAsset
from ...models.spot import SystemAssetLiability
from ...models.risk_control import (
    BusOnlineCoinRiskConfigByMarket,
    AssetAccumulatedWithdrawalConfig, SpecialAssetAccumulatedWithdrawalConfig,
    PreMarketSettleCheck, AssetAbnormalIssuanceConfig, AssetAccumulatedDepositConfigProportion,
    SpecialAssetAccumulatedDepositConfigProportion, RiskControlMobileNoticeConfig, PledgeLoanFlatCheck,
)
from ...models.pre_trading import PreTradingAssetConfig
from ...models.mongo.risk_control import UserPreventRiskControlConfigMySQL as UserPreventRiskControlConfig
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, \
    OPNamespaceObjectUser, OPNamespaceObjectRisk
from ...models.wallet import DeviceIDBlacklist, IPBlacklist
from ...utils import (
    timestamp_to_datetime, validate_email, now, export_xlsx,
    amount_to_str, format_percent, batch_iter, quantize_amount, group_by, GeoIP, validate_ip_address,
)
from ..common import (Namespace, Resource, ex_fields,
                      respond_with_code)
from ...utils.config_ import value_convert
from ...utils.date_ import datetime_to_utc8_str, today_datetime, datetime_to_str
from ...utils.helper import Struct
from ...utils.mobile import list_mobile_country_codes, cn_name_to_country_mobile_code, country_mobile_code_to_cn_name

ns = Namespace('RiskControl')


@ns.route('/risk-infos')
@respond_with_code
class RiskInfosResource(Resource):
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "风控时间"},
        {"field": "email", Language.ZH_HANS_CN: "用户邮箱"},
        {"field": "risk_count", Language.ZH_HANS_CN: "风控次数"},
        {"field": "reason", Language.ZH_HANS_CN: "风控类型"},
        {"field": "permissions", Language.ZH_HANS_CN: "权限"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "source", Language.ZH_HANS_CN: "来源"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "audited_by_name", Language.ZH_HANS_CN: "审核人"},
        {"field": "prevent_expired_at", Language.ZH_HANS_CN: "防干扰时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "detail", Language.ZH_HANS_CN: "风控详情"},
    )
    enums = {
        'enabled_reasons': [v.value for v in UserPreventRiskControlConfig.enabled_reasons()],
        'prevent_options': UserPreventRiskControlConfig.get_expired_at_options(),
        'statuses': {
            RiskUser.Status.AUDIT_REQUIRED.value: '待审核',
            RiskUser.Status.AUDITED.value: '已审核',
            RiskUser.Status.AUDIT_REJECTED.value: '审核不通过'},
        'reasons': {
            RiskUser.Reason.ABNORMAL_PROFIT.value: '异常盈利(实时)',
            RiskUser.Reason.PERIOD_ABNORMAL_PROFIT.value: '异常盈利(定期)',
            RiskUser.Reason.IMMEDIATELY_WITHDRAWAL.value: '即充即提',
            RiskUser.Reason.NEW_USER_IMMEDIATELY_WITHDRAWAL.value: '新注册即提现',
            RiskUser.Reason.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT.value: '提现无链上充值记录',
            # RiskUser.Reason.WITHDRAWAL_NO_DEPOSIT.value: '提现无充值记录',
            RiskUser.Reason.MARKET_VOLATILITY.value: '币币市场异常波动',
            RiskUser.Reason.PERPETUAL_MARKET_VOLATILITY.value: '合约市场异常波动',
            RiskUser.Reason.MARGIN_LIQUIDATION.value: '杠杆异常穿仓',
            RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK.value: '杠杆对账不平',
            RiskUser.Reason.INVESTMENT_BALANCE_CHECK.value: '理财对账不平',
            RiskUser.Reason.PERPETUAL_BALANCE_CHECK.value: '合约对账不平',
            RiskUser.Reason.PERPETUAL_LIQUIDATION.value: '合约异常穿仓',
            RiskUser.Reason.RED_PACKET_CHECK.value: '红包对账不平',
            RiskUser.Reason.P2P_BALANCE_CHECK.value: 'p2p对账不平',
            RiskUser.Reason.VIABTC_TRANS_BEYOND_THRESHOLD.value: '矿池入账超过阈值',
            RiskUser.Reason.SMS_MONITORING_AUTO.value: '短信监控-自动',
            RiskUser.Reason.SMS_MONITORING_MANUAL.value: '短信监控-人工',
            RiskUser.Reason.ORDER_DEPOSIT_BEYOND_THRESHOLD.value: '大额充值（币种）',
            RiskUser.Reason.USER_DEPOSIT_BEYOND_THRESHOLD.value: '大额充值（用户）',
            RiskUser.Reason.USER_ASSET_PENDING_ORDER_THRESHOLD.value: '单用户单个币种当前累计挂单监控',
            RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT.value: '币种累计充值监控（环比涨幅）',
            RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION.value: '币种累计充值监控（流通量占比）',
            RiskUser.Reason.USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION.value: '用户维度累计充值风控',
            RiskUser.Reason.ASSET_BALANCE_IN_DISABLED.value: '用户维度累计充值风控（新）',
            RiskUser.Reason.ACCUMULATED_ASSET_WITHDRAWAL.value: '币种累计提现监控',
            RiskUser.Reason.P2P_TRANS_ABNORMAL.value: 'P2P解冻异常',
            RiskUser.Reason.P2P_HIGH_RISK_USER.value: 'P2P高风险用户',
            RiskUser.Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY.value: '商务上币市场异常波动',
            RiskUser.Reason.BUS_ONLINE_COIN_DEPOSIT_ASSET_VOLATILITY.value: '商务上币充值风控（币种）',
            RiskUser.Reason.BUS_ONLINE_COIN_DEPOSIT_USER_VOLATILITY.value: '商务上币充值风控（用户）',
            RiskUser.Reason.WASH_DEAL.value: '现货防对敲监控',
            RiskUser.Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD.value: '借贷异常穿仓',
            RiskUser.Reason.PLEDGE_LOAN_FLAT_CHECK.value: '借贷对账不平',
            RiskUser.Reason.ABNORMAL_ISSUANCE.value: '币种异常增发',
            RiskUser.Reason.WITHDRAWAL_ADDRESS_BLACKLISTED.value: '提现黑名单',
            RiskUser.Reason.DEVICE_ID_BLACKLISTED.value: '设备ID黑名单',
            RiskUser.Reason.IP_BLACKLISTED.value: 'IP地址黑名单',
        },
        'permissions': {
            RiskUser.Permission.BALANCE_OUT_DISABLED.value: '提现受限',
            RiskUser.Permission.TRADING_DISABLED.value: '禁止交易',
            RiskUser.Permission.MARGIN_LOAN_DISABLED.value: '禁止借币',
            RiskUser.Permission.TRADING_LIMITED.value: '交易受限',
            RiskUser.Permission.TRANSFER_OUT_DISABLED.value: '禁止划出',
            RiskUser.Permission.ASSET_BALANCE_IN_DISABLED.value: '单币种充值受限',
        },
        'audit_options': {
            RiskUser.Status.AUDITED.value: '审核通过',
            RiskUser.Status.AUDIT_REJECTED.value: '审核不通过',
            RiskUser.Status.AUDIT_REQUIRED.value: '回到待审核',
        }
    }

    @classmethod
    def _get_prevent_expired_at_map(cls, user_ids: List[int]):
        records = UserPreventRiskControlConfig.query.filter(
            UserPreventRiskControlConfig.user_id.in_(user_ids)
        ).all()
        result = {}
        for record in records:
            history = record.history or []
            for item in history:
                if item['seconds']:
                    result[item['risk_user_id']] = timestamp_to_datetime(item['expired_at'])
                else:
                    result[item['risk_user_id']] = None
        return result

    @classmethod
    def _get_sources_enum(cls, reason: RiskUser.Reason):
        sources = []
        if not reason:
            return sources

        if reason.name in [
            "IMMEDIATELY_WITHDRAWAL",
            "NEW_USER_IMMEDIATELY_WITHDRAWAL",
            "WITHDRAWAL_NO_ON_CHAIN_DEPOSIT",
            "WITHDRAWAL_NO_DEPOSIT",
            "INVESTMENT_BALANCE_CHECK",
            "PERPETUAL_BALANCE_CHECK",
            "RED_PACKET_CHECK",
            "P2P_BALANCE_CHECK",
            "VIABTC_TRANS_BEYOND_THRESHOLD",
            "ORDER_DEPOSIT_BEYOND_THRESHOLD",
            "USER_ASSET_PENDING_ORDER_THRESHOLD",
            "ACCUMULATED_ASSET_DEPOSIT",
            "ACCUMULATED_ASSET_WITHDRAWAL",
            "ACCUMULATED_ASSET_DEPOSIT_PROPORTION",
            "BUS_ONLINE_COIN_DEPOSIT_ASSET_VOLATILITY",
            "BUS_ONLINE_COIN_DEPOSIT_USER_VOLATILITY",
        ]:
            sources = list_all_assets()
        elif reason.name in [
            "MARGIN_LIQUIDATION",
            "MARGIN_LOAN_FLAT_CHECK",
            "PERPETUAL_LIQUIDATION",
            "BUS_ONLINE_COIN_MARKET_VOLATILITY",
            "WASH_DEAL",
        ]:
            sources = set(MarketCache.list_online_markets() + PerpetualMarketCache().get_market_list())
        return sources

    @classmethod
    def get_detail(cls, detail: str, reason: Enum):
        if reason == RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK:
            try:
                detail = json.loads(detail).get('msg', '')
            except json.decoder.JSONDecodeError:
                pass
        return detail

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String,
        reason=ex_fields.EnumField(enum=RiskUser.Reason, enum_by_value=True),
        status=ex_fields.EnumField(enum=RiskUser.Status, enum_by_value=True),
        block_permissions=fields.String,
        source=fields.String,
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """风控-风控审核记录"""
        keyword = kwargs.get('keyword', '').strip()
        reason = kwargs.get('reason')
        status = kwargs.get('status')
        source = kwargs.get('source')
        block_permissions = kwargs.get('block_permissions')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = RiskUser.query

        if keyword:
            if keyword.isdigit():
                users = User.query.filter(or_(User.mobile_num == keyword, User.id == keyword)).all()
            elif validate_email(keyword):
                users = User.query.filter(User.email == keyword).all()
            else:
                users = User.query.filter(User.name == keyword).all()
            user_ids = [x.id for x in users]
            query = query.filter(RiskUser.user_id.in_(user_ids))
        if reason:
            query = query.filter(RiskUser.reason == reason)
            if block_permissions:
                block_permissions = [RiskUser.Permission(i) for i in block_permissions.split(",")]
                p_names = [p.name for p in sorted(block_permissions, key=list(RiskUser.Permission).index)]
                query = query.filter(RiskUser.block_permissions == json.dumps(p_names))
        if status:
            query = query.filter(RiskUser.status == status)
        if source:
            query = query.filter(RiskUser.source == source)
        if start_time:
            query = query.filter(RiskUser.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(RiskUser.created_at < timestamp_to_datetime(end_time))
        if not kwargs.get('export'):
            records = query.order_by(RiskUser.id.desc()).paginate(page, limit)
            user_ids = {item.user_id for item in records.items}
            prevent_expired_at_map = cls._get_prevent_expired_at_map(user_ids)
            name_map = get_admin_user_name_map({item.audited_by for item in records.items if item.audited_by})
            items = []
            for record in records.items:
                user = User.query.get(record.user_id)
                item = dict(
                    id=record.id,
                    created_at=record.created_at,
                    user_id=record.user_id,
                    email=user.email,
                    risk_count=record.risk_count,
                    reason=record.reason,
                    detail=cls.get_detail(record.detail, record.reason),
                    remark=record.remark,
                    source=record.source,
                    audited_by=record.audited_by,
                    audited_by_name=name_map.get(record.audited_by) or '',
                    audited_at=record.audited_at,
                    status=record.status,
                    permissions=record.permissions,
                    prevent_expired_at=prevent_expired_at_map.get(record.id)
                )
                items.append(item)
            result = dict(
                total=records.total,
                items=items,
                sources=cls._get_sources_enum(reason),
                **cls.enums
            )
            return result
        else:
            items = query.order_by(RiskUser.id.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            user_ids = set()
            for i in items:
                user_ids.add(i.user_id)
                if i.audited_by:
                    user_ids.add(i.audited_by)
            prevent_expired_at_map = {}
            for ids in batch_iter(user_ids, 2000):
                prevent_expired_at_map.update(cls._get_prevent_expired_at_map(ids))
            user_emails = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
            user_email_dic = {i.id: i.email for i in user_emails}
            name_map = get_admin_user_name_map(user_ids)
            res = []
            for record in items:
                prevent_expired_at = prevent_expired_at_map.get(record.id)
                data = dict(
                    created_at=datetime_to_utc8_str(record.created_at) if record.created_at else '',
                    user_id=record.user_id,
                    email=user_email_dic.get(record.user_id),
                    reason=cls.enums['reasons'].get(record.reason.value) if record.reason else '',
                    detail=record.detail,
                    risk_count=record.risk_count,
                    remark=record.remark,
                    source=record.source,
                    audited_by_name=name_map.get(record.audited_by, ''),
                    audited_at=datetime_to_utc8_str(record.audited_at) if record.audited_at else '',
                    status=cls.enums['statuses'].get(record.status.value) if record.status else '',
                    permissions=','.join([cls.enums['permissions'].get(x.value, '') for x in
                                          record.permissions]) if record.permissions else '无权限',
                    prevent_expired_at=datetime_to_utc8_str(prevent_expired_at) if prevent_expired_at else ''
                )
                res.append(data)
            return export_xlsx(
                filename='coinex_risk_info_list',
                data_list=res,
                export_headers=cls.export_headers
            )

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer),
        filter=ex_fields.EnumField(enum=RiskUser.Reason, enum_by_value=True),
        status=ex_fields.EnumField(required=True, enum=RiskUser.Status, enum_by_value=True,
                                   choices=(RiskUser.Status.AUDITED, RiskUser.Status.AUDIT_REJECTED)),
        prevent_seconds=fields.Integer(allow_none=True),
        remark=fields.String(allow_none=True)
    ))
    def post(cls, **kwargs):
        """风控-风控账户列表-审核"""
        filter_ = kwargs.get('filter')
        ids = kwargs.get('ids')
        status = kwargs['status']
        remark = kwargs.get('remark', '')
        if filter_:
            records = RiskUser.query.filter(
                RiskUser.reason == filter_,
                RiskUser.status == RiskUser.Status.AUDIT_REQUIRED
            ).all()
        elif ids:
            records = RiskUser.query.filter(RiskUser.id.in_(ids)).all()
        else:
            raise InvalidArgument
        unblocking_settings = defaultdict(lambda: defaultdict(set))
        for record in records:
            if record.status == status:
                continue
            for permission in record.permissions:
                unblocking_settings[record.user_id][permission].add(record.source)

        seconds = kwargs.get("prevent_seconds", 0) or 0
        for record in records:
            if record.status == status:
                continue
            old_data = record.to_dict(enum_to_name=True)
            record.status = status
            record.remark = remark
            record.audited_by = g.user.id
            record.audited_at = now()
            if record.reason == RiskUser.Reason.WITHDRAWAL_ADDRESS_BLACKLISTED:
                cls.sync_audit_withdrawal_audit_record(record)
            if status == RiskUser.Status.AUDITED:
                UserPreventRiskControlConfig.add_config(
                    record.user_id,
                    record.reason,
                    seconds,
                    None,
                    record.id,
                    record.source,
                    remark
                )
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.RiskUserAudit,
                old_data=old_data,
                new_data=record.to_dict(enum_to_name=True),
                target_user_id=record.user_id,
            )
        db.session.commit()

        if status == RiskUser.Status.AUDITED:
            is_async = cls._process_permissions('unblock', unblocking_settings)
        elif status in [
            RiskUser.Status.AUDIT_REQUIRED,
            RiskUser.Status.AUDIT_REJECTED,
        ]:
            is_async = cls._process_permissions('block', unblocking_settings)
        return {'async': is_async}

    @classmethod
    def _process_permissions(self, op: str, permissions) -> bool:
        """返回是否异步执行"""
        if len(permissions) > 100:
            pers = {k: {x.name: list(y) for x, y in v.items()} for k, v in permissions.items()}
            process_permissions_task.delay(op, pers)
            return True
        else:
            for user_id, pers in permissions.items():
                if op == 'unblock':
                    try_unblock_permissions(user_id, pers)
                else:
                    admin_block_permissions(user_id, pers)
            return False

    @classmethod
    def sync_audit_withdrawal_audit_record(cls, risk_user: RiskUser):
        from app.models.wallet import WithdrawalAudit

        try:
            w_id = int(risk_user.detail)  # 约定detail存提现记录ID
        except ValueError:
            return
        status_map = {
            # 风控用户支持的审核操作： 审核通过、审核不通过、回到待审核
            RiskUser.Status.AUDIT_REQUIRED: WithdrawalAudit.Status.AUDIT_REQUIRED,
            RiskUser.Status.AUDITED: WithdrawalAudit.Status.AUDITED,
            RiskUser.Status.AUDIT_REJECTED: WithdrawalAudit.Status.AUDIT_REJECTED,
        }
        w_audit_status = status_map.get(risk_user.status)
        if not w_audit_status:
            return
        WithdrawalAuditHelper.do_audit_address_blacklist_by_wid(
            w_id, w_audit_status, risk_user.audited_by, risk_user.audited_at, risk_user.remark
        )


# noinspection PyUnresolvedReferences
@ns.route('/risk-infos/<int:id_>')
@respond_with_code
class RiskInfoResource(Resource):

    @classmethod
    def get(cls, id_):
        """风控-风控账户审核"""
        if id_ == 0:
            risk_user = RiskUser.query.filter(RiskUser.status == RiskUser.Status.AUDIT_REQUIRED).order_by(
                RiskUser.id).first()
            if not risk_user:
                return {}
        else:
            risk_user = RiskUser.query.get(id_)
            if not risk_user:
                raise NotFound
        next_ = RiskUser.query.filter(RiskUser.id > risk_user.id).order_by(RiskUser.id).first()
        user = User.query.get(risk_user.user_id)
        result = {
            'risk_info': dict(
                id=risk_user.id,
                created_at=risk_user.created_at,
                user_id=risk_user.user_id,
                reason=risk_user.reason,
                detail=RiskInfosResource.get_detail(risk_user.detail, risk_user.reason),
                remark=risk_user.remark,
                source=risk_user.source,
                audited_by=risk_user.audited_by,
                audited_by_name=get_admin_user_name_map([risk_user.audited_by]).get(
                    risk_user.audited_by) if risk_user.audited_by else '',
                audited_at=risk_user.audited_at,
                status=risk_user.status,
                permissions=risk_user.permissions
            ),
            'user_info': dict(
                id=user.id,
                created_at=user.created_at,
                registration_ip=user.registration_ip,
                registration_location=user.registration_location,
                email=user.email,
                mobile=user.mobile,
                name=user.name,
                user_type=user.user_type,
                kyc_status=user.kyc_status
            ),
            'next': next_.id if next_ else 0,
            **RiskInfosResource.enums
        }
        if user.kyc_status == User.KYCStatus.PASSED:
            kyc = KycVerification.query.filter(KycVerification.user_id == user.id,
                                               KycVerification.status == KycVerification.Status.PASSED).first()
            if not kyc:
                result['kyc_info'] = None
            else:
                result['kyc_info'] = dict(
                    country=kyc.country,
                    full_name=kyc.full_name,
                    id_type=kyc.id_type,
                    id_number=kyc.id_number
                )
        return result


@ns.route('/risk-system-record')
@respond_with_code
class RiskRecordsResource(Resource):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "序号"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "reason_type_str", Language.ZH_HANS_CN: "风控类型"},
        {"field": "reason_detail", Language.ZH_HANS_CN: "风控详情"},
        {"field": "trigger_time", Language.ZH_HANS_CN: "触发时间"},
        {"field": "resume_time", Language.ZH_HANS_CN: "恢复时间"},
    )

    @classmethod
    def get_risk_history(cls, db, ids: List = None, market: str = '', start_time: int = None,
                         end_time: int = None, offset: int = None, limit: int = None):
        table = db.table('risk_history')
        columns = ('id', 'market', 'trigger_time', 'resume_time', 'reason', 'ban_exchange', 'detail')
        query_list = []
        if ids:
            ids_str = ','.join(map(str, ids))
            query_list.append(f'`id` in ({ids_str})')
        if market:
            query_list.append(f"market = '{market}'")
        if start_time:
            query_list.append(f'trigger_time > {start_time}')
        if end_time:
            query_list.append(f'trigger_time < {end_time}')
        where = ' AND '.join(query_list)
        total = table.count(where=where)
        if offset is None and limit is None:
            records = list(dict(zip(columns, record)) for record in table.select(
                *columns, where=where, order_by="id desc"))
        else:
            records = list(dict(zip(columns, record)) for record in table.select(
                *columns, where=where, order_by="id desc", limit=(offset, limit)))
        return records, total

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String,
        reason=ex_fields.EnumField(enum=IndexPriceRiskControlHelper.ReasonStrType,
                                   missing=IndexPriceRiskControlHelper.ReasonStrType.PERPETUAL),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """风控-系统风控审核"""
        market = kwargs.get('market')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        reason = kwargs['reason']
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        db_ = PerpetualSysHistoryDB
        if reason == IndexPriceRiskControlHelper.ReasonStrType.MARGIN:
            db_ = TradeLogDB
        if kwargs.get('export'):
            records, _ = cls.get_risk_history(db=db_,
                                              market=market, start_time=start_time, end_time=end_time)
        else:
            records, total = cls.get_risk_history(db=db_,
                                                  market=market, start_time=start_time, end_time=end_time,
                                                  offset=offset, limit=limit)
        result = []
        for record in records:
            tmp = IndexPriceRiskControlHelper.format_index_price_record(record, reason.value)
            result.append(tmp)

        if kwargs.get('export'):
            return export_xlsx(
                filename='risk_system_info_list',
                data_list=result,
                export_headers=cls.export_headers
            )
        spot_market_list = [i.name for i in Market.query.all()]
        perpetual_market_list = [i.name for i in PerpetualMarket.query.all()]
        total_market_list = list(set(spot_market_list + perpetual_market_list))
        extra = dict(market_list=sorted(total_market_list),
                     reasons=IndexPriceRiskControlHelper.ReasonStrType,
                     )

        return dict(items=result,
                    total=total,
                    extra=extra
                    )


@ns.route('/risk-user-event-record')
@respond_with_code
class RiskUserEventRecordsResource(Resource):
    reason_str_map = {
        RiskEventLog.Reason.MARGIN_LIQUIDATION: '杠杆穿仓异常',
        RiskEventLog.Reason.MARGIN_LOAN_FLAT_CHECK: '杠杆对账机制',
        RiskEventLog.Reason.PERPETUAL_BALANCE_CHECK: '合约对账机制',
        RiskEventLog.Reason.RED_PACKET_CHECK: '红包对账机制',
        RiskEventLog.Reason.P2P_BALANCE_CHECK: 'P2P对账机制',
        RiskEventLog.Reason.INVESTMENT_BALANCE_CHECK: '理财对账机制',
        RiskEventLog.Reason.PERPETUAL_LIQUIDATION: '合约穿仓异常',
        RiskEventLog.Reason.MARKET_VOLATILITY: '币币-市场异常波动',
        RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY: '合约-市场异常波动',
        RiskEventLog.Reason.ABNORMAL_ISSUANCE: '币种异常增发',
        RiskEventLog.Reason.VIABTC_TRANS_BEYOND_THRESHOLD: '矿池异常入账',
        RiskEventLog.Reason.NO_DEALS: '交易撮合监控',
        RiskEventLog.Reason.INDEX_PRICE_NOT_UPDATED: '指数价格不更新',
        RiskEventLog.Reason.WITHDRAWAL_FUSE: '全站提现熔断',
        RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT: '币种累计充值监控（环比涨幅）',
        RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION: '币种累计充值监控（流通量占比）',
        RiskEventLog.Reason.ACCUMULATED_ASSET_WITHDRAWAL: '币种累计提现监控',
        RiskEventLog.Reason.WITHDRAWALS_DISABLED_BY_ASSET_LIABILITY: '全站资产负债不平',
        RiskEventLog.Reason.WITHDRAWALS_DISABLED_BY_COIN_ASSET_LIABILITY: '币种资产负债不平',
        RiskEventLog.Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY: '商务上币市场异常波动',
        RiskEventLog.Reason.BUS_ONLINE_COIN_DEPOSIT_VOLATILITY: '商务上币充值风控（币种）',
        RiskEventLog.Reason.SIGNED_WITHDRAWALS_CANCEL: '已签名提现取消',
        RiskEventLog.Reason.DEPOSITS_FUSE: '全站充值受限',
        RiskEventLog.Reason.WASH_DEAL: '现货防对敲监控',
        RiskEventLog.Reason.PLEDGE_LOAN_FLAT_CHECK: '借贷对账不平',
        RiskEventLog.Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD: '借贷异常穿仓',
        RiskEventLog.Reason.SITE_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: '钱包小额提现熔断（全站）',
        RiskEventLog.Reason.ASSET_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: '钱包小额提现熔断（币种）',
    }

    @classmethod
    def format_risk_event_record(cls, record):

        def _format_margin_liquidation_extra(row):
            extra = json.loads(row.extra)
            extra['minutes'] = extra.get('minutes', 15)
            extra['amount'] = amount_to_str(extra['amount'], PrecisionEnum.CASH_PLACES)
            extra['amount_limit'] = amount_to_str(extra['amount_limit'], PrecisionEnum.CASH_PLACES)
            extra['min_price'] = amount_to_str(extra.get('min_price', 0), PrecisionEnum.COIN_PLACES)
            extra['max_price'] = amount_to_str(extra.get('max_price', 0), PrecisionEnum.COIN_PLACES)
            if extra.get('rate'):
                extra['rate'] = Decimal(extra['rate']) * 100
                extra['rate_limit'] = Decimal(extra['rate_limit']) * 100
            else:
                extra['rate'] = Decimal('0')
                extra['rate_limit'] = Decimal('0')
            if extra.get('user_list_rank'):
                extra['user_list_rank'] = [i['user_id'] for i in extra['user_list_rank']]
            if extra.get('buy_list_rank'):
                extra['buy_list_rank'] = [i['user_id'] for i in extra['buy_list_rank']]
            if extra.get('sell_list_rank'):
                extra['sell_list_rank'] = [i['user_id'] for i in extra['sell_list_rank']]
            return extra

        def _format_no_deal_extra(row):
            server_name_str = '现货' if row.source == 'server' else '合约'
            extra = json.loads(row.extra)
            extra['server_name_str'] = server_name_str
            return extra

        def _format_index_price_not_updated_extra(row):
            extra = json.loads(row.extra)
            return extra

        def _format_abnormal_issuance(row):
            asset = row.source
            extra = json.loads(row.extra)
            chain = extra['chain']
            extra['source_str'] = f'{asset}-{chain}'
            extra['user_count'] = extra.get('user_count', 0)
            delta_issue_amount = Decimal(extra['cur_issue_amount']) - Decimal(extra['his_issue_amount'])
            extra['rate'] = format_percent(delta_issue_amount / Decimal(extra['his_issue_amount']), 2) if Decimal(
                extra['his_issue_amount']) else 0
            if 'delta_amount_usd' not in extra:
                extra['delta_amount_usd'] = '0'
            return extra

        def _format_margin_loan_flat_check(row):
            market = row.source
            extra = json.loads(row.extra)
            extra['market'] = market
            return extra

        def _format_red_packet_check(row):
            asset = row.source
            extra = json.loads(row.extra)
            extra['asset'] = asset
            return extra

        def _format_p2p_balance_check(row):
            asset = row.source
            extra = json.loads(row.extra)
            extra['asset'] = asset
            return extra

        def _format_deposits_fuse(row):
            extra = json.loads(row.extra)
            extra['avg'] = extra.get('avg', 0)
            extra['rate'] = extra.get('rate', 0)
            return extra

        def _format_perpetual_balance_check(row):
            asset = row.source
            extra = json.loads(row.extra)
            extra['asset'] = asset
            extra['transfer_in_amount_str'] = amount_to_str(extra['transfer_in_amount'])
            extra['transfer_out_amount_str'] = amount_to_str(extra['transfer_out_amount'])
            extra['server_transfer_in_amount'] = Decimal(extra['server_transfer_in_amount'])
            extra['last_server_transfer_in_amount'] = Decimal(extra['last_server_transfer_in_amount'])
            extra['server_transfer_out_amount'] = Decimal(extra['server_transfer_out_amount'])
            extra['last_server_transfer_out_amount'] = Decimal(extra['last_server_transfer_out_amount'])
            extra['delta_server_transfer_in_str'] = amount_to_str(
                Decimal(extra['server_transfer_in_amount']) - Decimal(extra['last_server_transfer_in_amount']))
            extra['delta_server_transfer_out_str'] = amount_to_str(
                Decimal(extra['server_transfer_out_amount']) - Decimal(extra['last_server_transfer_out_amount']))
            return extra

        def _format_viabtc_trans_beyond_threshold(row):
            asset = row.source
            extra = json.loads(row.extra)
            extra['asset'] = asset
            extra['asset_usd_str'] = amount_to_str(extra['asset_usd'], 2)
            extra['user_list_rank'] = [i['user_id'] for i in extra['user_list_rank']]
            return extra

        def _format_accumulated_asset_deposit_threshold(row):
            extra = json.loads(row.extra)
            extra['user_list_rank'] = [uid for uid in extra['usd_top10'].keys()]
            extra['rank_desc'] = extra.get('rank_desc', '')
            return extra

        def _format_accumulated_asset_deposit_proportion_threshold(row):
            extra = json.loads(row.extra)
            extra['user_list_rank'] = [uid for uid in extra['usd_top10'].keys()]
            extra['rank_desc'] = extra.get('rank_desc', '')
            extra['value_p'] = format_percent(extra['check_proportion'], 6)
            extra['period_p'] = format_percent(extra['asset_period_dp'], 6)
            return extra

        def _format_accumulated_asset_withdrawal_threshold(row):
            extra = json.loads(row.extra)
            extra['user_list_rank'] = [uid for uid in extra['usd_top10'].keys()]
            extra['rank_desc'] = extra.get('rank_desc', '')
            return extra

        def _format_withdrawals_disabled_by_asset_liability(row):
            extra = json.loads(row.extra)
            if 'threshold' not in extra:
                extra['threshold'] = '5000000'
            return extra

        def _format_bus_online_coin_by_market_volatility(row):
            extra = json.loads(row.extra)
            extra['rate'] = amount_to_str(Decimal(extra['rate']) * Decimal('100'), PrecisionEnum.RATE_PLACES)
            extra['rate_limit'] = amount_to_str(Decimal(extra['rate_limit']) * Decimal('100'), PrecisionEnum.RATE_PLACES)
            extra['user_list_rank'] = [i['user_id'] for i in extra['user_list_rank']]
            return extra

        def _format_bus_online_coin_by_asset_volatility(row):
            extra = json.loads(row.extra)
            extra['user_list_rank'] = [i['user_id'] for i in extra['user_list_rank']]
            return extra

        def format_market_volatility(row):
            extra = json.loads(row.extra)
            # 新版本数据
            if period_minuter := extra.get("period_minuter"):
                detail = f'1. {extra["market"]}市场波动{period_minuter}分钟内'
                increase_rate = Decimal(extra["increase_rate"])
                increase_rate_limit = Decimal(extra["increase_rate_limit"])
                decrease_rate = Decimal(extra["decrease_rate"])
                decrease_rate_limit = Decimal(extra["decrease_rate_limit"])
                if increase_rate >= increase_rate_limit:
                    detail += f"上涨{increase_rate * 100:.2f}%（阈值为{increase_rate_limit * 100:.2f}%）  "
                if decrease_rate >= decrease_rate_limit:
                    detail += f"下跌{decrease_rate * 100:.2f}%（阈值为{decrease_rate_limit * 100:.2f}%)"
                detail += "\n2. 成交量{amount}USD （阈值为{amount_limit} USD） " \
                          "\n3. 周期内市场最高成交价: {max_price}; 最低成交价 {min_price} " \
                          "\n4. 风控用户数 {user_count}".format(**extra)
                return detail
            else:
                detail = "1.{market}市场异常波动{rate}%（{rate_limit}%）\n" \
                         "2.成交额{amount}USD（{amount_limit} USD）\n" \
                         "3.周期内市场最低成交价：{min_price} 最高成交价：{max_price}\n" \
                         "4.影响{user_count}用户，{big_user_count}用户限制提现，{small_user_count}用户限制提现&交易\n" \
                         "净买入量前五：{buy_list_rank}\n" \
                         "净卖出量前五：{sell_list_rank}"
                return detail.format(**extra)

        Reason = RiskEventLog.Reason
        reason_detail_map = {
            Reason.MARGIN_LIQUIDATION: "1.{market}杠杆市场{minutes}分钟内穿仓金额{amount}USD（{amount_limit} USD）\n"
                                       "2.影响用户数{user_count}\n"
                                       "3.周期内市场最低成交价：{min_price} 最高成交价：{max_price}\n"
                                       "4.保险金垫付前10：{user_list_rank}",
            Reason.PERPETUAL_LIQUIDATION: "1.{market} 合约市场{minutes}分钟内穿仓{amount}USD（{amount_limit} USD）\n"
                                          "2.影响用户数{user_count}\n"
                                          "3.周期内市场最低成交价{min_price}： 最高成交价：{max_price}\n"
                                          "4.保险金垫付前10：{user_list_rank}\n",

            Reason.RED_PACKET_CHECK: "{asset}红包对账不平，差额为{diff_amount} 受影响用户数{user_count}",
            Reason.INVESTMENT_BALANCE_CHECK: "{asset}理财对账不平，差额为{diff_amount} 受影响用户数{user_count}",
            Reason.P2P_BALANCE_CHECK: "{asset} p2p对账不平，差额为{diff_amount} 受影响用户数{user_count}",
            Reason.PERPETUAL_BALANCE_CHECK: "{asset}永续合约资产对账不平，差额为{diff_amount} 受影响用户数{user_count}",
            Reason.MARGIN_LOAN_FLAT_CHECK: "{market}杠杆市场{asset}借款对账不平，差额为{diff_amount} 受影响用户数{user_count}",
            Reason.VIABTC_TRANS_BEYOND_THRESHOLD: "矿池充值告警: {asset}\n"
                                                  "今日充值：{today_asset_trans_amount}/{history_amount}（当日累计充值币数/近5日均值\n"
                                                  "充值市值：{asset_usd_str}USD\n"
                                                  "注：全部矿池币种已关闭入账，需人工处理恢复，异常用户需审核",
            Reason.NO_DEALS: "{server_name_str}server异常：持续{minutes}分钟没有撮合成交",
            Reason.INDEX_PRICE_NOT_UPDATED: "{source}市场 {markets} 连续{minutes}分钟指数价格不更新",
            Reason.ABNORMAL_ISSUANCE: "{asset}-{chain}发行量告警\n"
                                      "历史发行量：{his_issue_amount}\n"
                                      "当前发行量：{cur_issue_amount}\n增量：{rate}\n"
                                      "增发市值：{delta_amount_usd} USD\n"
                                      "充提已关闭\n"
                                      "被风控用户数：{user_count}\n",
            Reason.WITHDRAWAL_FUSE: """
1.最近24H提现市值/7日均值：{latest_24h_usd}/{last_7d_average_usd}≈{current_usd_threshold}，阈值为{usd_threshold}，{usd_warning}
2.最近24H提现笔数/7日均值：{latest_24h_count}/{last_7d_average_count}≈{current_count_threshold}，阈值为{count_threshold}，{count_warning}
3. 注：已关闭全站提现，请及时查看处理.
            """,
            Reason.ACCUMULATED_ASSET_DEPOSIT: """
1. 档位：{rank_desc}
2. 最近24H充值市值（USD）：{last_24h_usd}，允许监控阈值为{threshold}
3. 充值数量：{amount}/{avg_amount}≈{amount_threshold}，限制阈值为{limit_amount_threshold}，{amount_warning}
4. 充值笔数：{count}/{avg_count}≈{count_threshold}，限制阈值为{limit_count_threshold}，{count_warning}
5. 风控用户数：{user_count}；提现受限用户{user_count}；
6. 注：{asset}充值已关闭，需人工处理恢复；
            """,
            Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION: """
1. 该币种最近{asset_period}H累计充值数量/币种流通量≈{value_p}（阈值为{period_p}）
风控用户数：{user_count}
注：已限制{asset}充值，需人工处理恢复；
            """,
            Reason.ACCUMULATED_ASSET_WITHDRAWAL: """
1. 档位：{rank_desc}
2. 最近24h提现市值（USD）：{last_24h_usd}，允许监控阈值为{threshold}
3. 最近24h提现数量/7日均值：{amount}/{avg_amount}≈{amount_threshold}，阈值为{limit_amount_threshold}，{amount_warning}
4. 风控用户数：{user_count}；禁止提现用户{limit_cnt}；
    5. 注：{asset}提现已关闭，需人工处理恢复；
                    """,
            Reason.WITHDRAWALS_DISABLED_BY_ASSET_LIABILITY: "平台权益：{income} USD（阈值为{threshold} USD）",
            Reason.WITHDRAWALS_DISABLED_BY_COIN_ASSET_LIABILITY: "平台权益：{income} {asset}(阈值为平台负债*{alr}%={alt})\n"
                                            "权益市值：{income_usd} USD（阈值为{at} USD）",
            Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY: """
            1. {market}市场{period_by_hour}小时内下跌{rate}%（阈值为{rate_limit}%） 
            2. 风控用户数：{risk_user_count}
                        """,
            Reason.BUS_ONLINE_COIN_DEPOSIT_VOLATILITY: """
            1. {asset} {asset_period_by_hour}小时内充值数量为{total_deposit_amount}（阈值为{asset_deposit_threshold}）
            2. 风控用户数：{risk_user_count}
                        """,
            Reason.WASH_DEAL: """
            {market}现货市场，{risk_deals_time_range}分钟内价格波动高达{price_fluctuation};
            偏离指数价格{last_price_over_index_price_rate}，充值{deposit_status}，提现{withdrawal_status};
            {risk_deals_time_range}分钟内成交额{deal_usd}USD，超过日均成交市值{deal_over_avg_rate};
            {deal_price_abs_rate}%深度约{depth_deal_usd}U市值，被{top_trade_user_count}个用户清空；
            {risk_deals_time_range}分钟内成交市值排{user_trade_rank}位且单用户成交市值
            大于{user_trade_amount_threshold}U的用户有{top_trade_user_count}人被风控，提现受限；
            """,
            Reason.SIGNED_WITHDRAWALS_CANCEL: "最近24H全站已签名但提现取消的提现市值：{latest_24h_usd}, 阈值为{threshold}，"
                                              "已达到阈值；注：全站提现已关闭，需人工处理恢复；",
            Reason.DEPOSITS_FUSE: "最近24H全站充值市值/7日均值：{latest_24h_usd}/{avg}≈{rate}，阈值：{threshold}。"
                                  "注：全站充值已关闭，需人工处理恢复；",
            Reason.PLEDGE_LOAN_FLAT_CHECK: "1、借贷对账不平，差额为{diff_amount}{asset}\n"
                                            "2、被风控用户数{user_count}，提现受限",

            Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD: "1、借贷订单强平导致总计穿仓{total_fund_repay_usd}USD \n"
                                                        "2、被风控用户数{risk_user_count}，提现受限",
            Reason.SITE_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: \
                "1、最近24H全站小额提现市值/7日均值：{recent_withdrawal_value}/{average_withdrawal_value}≈{rate}；"
            "提现市值阈值为：{withdrawal_threshold}，倍数阈值为：{threshold_multiple}\n"
            "2、注：已关闭全站提现，需人工处理恢复",
            Reason.ASSET_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: \
                "1、最近24小时{asset}小额提现/钱包总资产：{recent_withdrawal_amount}/{wallet_amount}≈{rate} ；"
            "提现市值（USD）：{recent_withdrawal_value}，提现市值阈值为：{withdrawal_threshold}, 倍数阈值为：{threshold_multiple}\n"
            "2、注：已关闭{asset}提现，需人工处理恢复；",
        }

        reason_func_map = {
            Reason.MARGIN_LIQUIDATION: _format_margin_liquidation_extra,
            Reason.PERPETUAL_LIQUIDATION: _format_margin_liquidation_extra,
            Reason.MARKET_VOLATILITY: _format_margin_liquidation_extra,
            Reason.PERPETUAL_MARKET_VOLATILITY: _format_margin_liquidation_extra,
            Reason.RED_PACKET_CHECK: _format_red_packet_check,
            Reason.P2P_BALANCE_CHECK: _format_p2p_balance_check,
            Reason.INVESTMENT_BALANCE_CHECK: _format_red_packet_check,
            Reason.MARGIN_LOAN_FLAT_CHECK: _format_margin_loan_flat_check,
            Reason.PERPETUAL_BALANCE_CHECK: _format_perpetual_balance_check,
            Reason.NO_DEALS: _format_no_deal_extra,
            Reason.INDEX_PRICE_NOT_UPDATED: _format_index_price_not_updated_extra,
            Reason.ABNORMAL_ISSUANCE: _format_abnormal_issuance,
            Reason.VIABTC_TRANS_BEYOND_THRESHOLD: _format_viabtc_trans_beyond_threshold,
            Reason.WITHDRAWAL_FUSE: lambda x: json.loads(x.extra),
            Reason.ACCUMULATED_ASSET_DEPOSIT: _format_accumulated_asset_deposit_threshold,
            Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION: _format_accumulated_asset_deposit_proportion_threshold,
            Reason.ACCUMULATED_ASSET_WITHDRAWAL: _format_accumulated_asset_withdrawal_threshold,
            Reason.WITHDRAWALS_DISABLED_BY_ASSET_LIABILITY: _format_withdrawals_disabled_by_asset_liability,
            Reason.WITHDRAWALS_DISABLED_BY_COIN_ASSET_LIABILITY: lambda x: json.loads(x.extra),
            Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY: _format_bus_online_coin_by_market_volatility,
            Reason.BUS_ONLINE_COIN_DEPOSIT_VOLATILITY: _format_bus_online_coin_by_asset_volatility,
            Reason.SIGNED_WITHDRAWALS_CANCEL: lambda x: json.loads(x.extra),
            Reason.DEPOSITS_FUSE: _format_deposits_fuse,
            Reason.WASH_DEAL: lambda x: json.loads(x.extra),
            Reason.PLEDGE_LOAN_FLAT_CHECK: lambda x: json.loads(x.extra),
            Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD: lambda x: json.loads(x.extra),
            Reason.SITE_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: lambda x: json.loads(x.extra),
            Reason.ASSET_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT: lambda x: json.loads(x.extra),
        }
        source = record.source
        extra_data = reason_func_map[record.reason](record)
        if record.reason == Reason.PERPETUAL_BALANCE_CHECK and abs(Decimal(extra_data['diff_amount'])) < Decimal(
                '1e-5'):
            # 特殊处理合约对账不平，这里同一个事件由于diff_amount有多个文案的告警
            reason_detail = "{asset}永续合约资产划转不平，web划入{transfer_in_amount_str}，" \
                            "划出{transfer_out_amount_str}，server划入{delta_server_transfer_in_str}，" \
                            "划出{delta_server_transfer_out_str}。受影响用户数{user_count}".format(**extra_data)
        elif record.reason in [Reason.MARKET_VOLATILITY, Reason.PERPETUAL_MARKET_VOLATILITY]:
            reason_detail = format_market_volatility(record)
        else:
            reason_detail = reason_detail_map[record.reason].format(**extra_data)
        return dict(
            id=record.id,
            source=source,
            extra=extra_data,
            remark=record.remark,
            reason=record.reason,
            operate_at=record.operate_at,
            status=record.status.name if record.status else None,
            reason_type_str=cls.reason_str_map[record.reason],
            resume_time=datetime_to_str(record.resume_time, 480) if record.resume_time else '--',
            trigger_time=datetime_to_str(record.start_time, 480),
            reason_detail=reason_detail,
        )

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "序号"},
        {"field": "source", Language.ZH_HANS_CN: "来源"},
        {"field": "reason_type_str", Language.ZH_HANS_CN: "风控类型"},
        {"field": "reason_detail", Language.ZH_HANS_CN: "风控详情"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "trigger_time", Language.ZH_HANS_CN: "触发时间"},
        {"field": "resume_time", Language.ZH_HANS_CN: "恢复时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        source=fields.String,
        reason=ex_fields.EnumField(enum=RiskEventLog.Reason),
        status=ex_fields.EnumField(enum=RiskEventLog.Status),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """风控-用户风控事件审核"""

        source = kwargs.get('source')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        reason = kwargs.get('reason')
        status = kwargs.get('status')
        export = kwargs.get('export')
        page = kwargs['page']
        limit = kwargs['limit']
        query = RiskEventLog.query.order_by(RiskEventLog.id.desc())
        if start_time:
            query = query.filter(RiskEventLog.created_at >= start_time)
        if end_time:
            query = query.filter(RiskEventLog.created_at <= end_time)
        if source:
            query = query.filter(RiskEventLog.source == source)
        if reason:
            query = query.filter(RiskEventLog.reason == reason)
        if status:
            query = query.filter(RiskEventLog.status == status)

        if export:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            page_rows = query.paginate(page, limit, error_out=False)
            total = page_rows.total
            records = page_rows.items
        result = []

        for record in records:
            result.append(cls.format_risk_event_record(record))

        if kwargs.get('export'):
            return export_xlsx(
                filename='risk_event_info_list',
                data_list=result,
                export_headers=cls.export_headers
            )
        total_amount = 0
        total_user_count = 0
        statistic_reason_list = [RiskEventLog.Reason.PERPETUAL_LIQUIDATION, RiskEventLog.Reason.MARGIN_LIQUIDATION]
        # 只有选定类型和市场才会聚合
        if reason in statistic_reason_list and source:
            total_records = query.all()
            total_amount = sum([Decimal(json.loads(i.extra).get('amount', 0)) for i in total_records])
            total_user_count = sum([json.loads(i.extra).get('user_count', 0) for i in total_records])

        source_list = []
        if reason == RiskEventLog.Reason.NO_DEALS:
            source_list = ['server', 'perpetual_server']
        elif reason in [RiskEventLog.Reason.PERPETUAL_LIQUIDATION,
                        RiskEventLog.Reason.MARGIN_LIQUIDATION,
                        RiskEventLog.Reason.MARGIN_LOAN_FLAT_CHECK,
                        RiskEventLog.Reason.MARKET_VOLATILITY,
                        RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY,
                        RiskEventLog.Reason.MARGIN_LOAN_FLAT_CHECK]:
            source_list = set(MarketCache.list_online_markets() + PerpetualMarketCache().get_market_list())
        elif reason in [RiskEventLog.Reason.RED_PACKET_CHECK, RiskEventLog.Reason.INVESTMENT_BALANCE_CHECK,
                        RiskEventLog.Reason.VIABTC_TRANS_BEYOND_THRESHOLD, RiskEventLog.Reason.PERPETUAL_BALANCE_CHECK,
                        RiskEventLog.Reason.ABNORMAL_ISSUANCE, RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT,
                        RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION
                        ]:
            source_list = list_all_assets()
        extra = dict(source_list=source_list,
                     reasons={k.name: v for k, v in cls.reason_str_map.items()},
                     total_amount=total_amount,
                     total_user_count=total_user_count,
                     statuses=RiskEventLog.Status,
                     allowed_audit_reasons=[e.value for e in RiskEventLog.ALLOWED_AUDIT_REASONS],
                     )

        return dict(items=result,
                    total=total,
                    extra=extra)


@ns.route('/risk-user-event-record/<int:id_>/audit')
@respond_with_code
class RiskUserEventRecordsAuditResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(RiskEventLog.Status, required=True),
    ))
    def patch(cls, id_, **kwargs):
        """风控-用户风控事件审核-通过/拒绝"""
        row: RiskEventLog = RiskEventLog.query.get(id_)
        if row is None:
            raise RecordNotFound
        if row.reason not in RiskEventLog.ALLOWED_AUDIT_REASONS:
            raise InvalidArgument(message='该类型暂不支持审核！')
        if row.status is not RiskEventLog.Status.AUDIT_REQUIRED:
            raise InvalidArgument(message=f'只有待审核状态才需审核！')
        row.status = kwargs['status']
        row.operate_at = now()
        db.session.commit()

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RiskEvent,
            detail=row.to_dict(enum_to_name=True),
        )


@ns.route('/risk-user-event-record/<int:id_>/remark')
@respond_with_code
class RiskUserEventRecordsRemarkResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """风控-用户风控事件审核-修改备注"""
        row: RiskEventLog = RiskEventLog.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        remark = kwargs.get('remark') or ''
        row.remark = remark
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RiskEvent,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/risk-users')
@respond_with_code
class RiskUsersResource(Resource):
    _field_map = {
        'login_enabled': 'login_disabled_by_admin',
        'withdrawals_enabled': ('withdrawals_disabled_by_admin',
                                'withdrawals_disabled_due_to_margin_arrears',
                                'withdrawals_disabled_due_to_credit_risk',
                                'withdrawals_disabled_after_security_editing'
                                'withdrawals_disabled_after_withdraw_password_editing'
                                ),
        'spot_trading_enabled': ('spot_trading_disabled_by_admin',
                                 'spot_trading_disabled_by_credit',
                                 'trading_disabled_by_risk_control'),
        'margin_trading_enabled': ('margin_trading_disabled_by_admin',
                                   'trading_disabled_by_risk_control'),
        'perpetual_trading_enabled': ('perpetual_trading_disabled_by_admin',
                                      'trading_disabled_by_risk_control'),
        'margin_loan_enabled': 'margin_loan_enabled',
        'forbidden_margin_accounts': 'forbidden_margin_accounts',
        'forbidden_margin_flat': 'forbidden_margin_flat',
        'sub_account_transfer_out_disabled': 'sub_account_transfer_out_disabled',
        'red_packet_disabled_by_risk_control': 'red_packet_disabled_by_risk_control',
        'p2p_sell_disabled': 'p2p_sell_disabled',
        'p2p_disabled_trans': 'p2p_disabled_trans',
    }

    field_admin_setting_map = {
        'fields': {
            'login_enabled': 'login_disabled_by_admin',
            'withdrawals_enabled': 'withdrawals_disabled_by_admin',
            'spot_trading_enabled': 'spot_trading_disabled_by_admin',
            'margin_trading_enabled': 'margin_trading_disabled_by_admin',
            'perpetual_trading_enabled': 'perpetual_trading_disabled_by_admin',
            'margin_loan_enabled': 'margin_loan_enabled',
            'forbidden_margin_accounts': 'forbidden_margin_accounts',
            'forbidden_margin_flat': 'forbidden_margin_flat',
            'sub_account_transfer_out_disabled': 'sub_account_transfer_out_disabled',
            'red_packet_disabled_by_risk_control': 'red_packet_disabled_by_risk_control',
            'p2p_sell_disabled': 'p2p_sell_disabled',
            'p2p_disabled_trans': 'p2p_disabled_trans'
        },
        # 值为true，表示"开启"的字段
        'direct_fields': ["margin_loan_enabled", ]
    }

    field_trans_map = {
        'user_id': '用户ID',
        'email': '邮箱',
        'login_enabled': '禁止登录',
        'withdrawals_enabled': '禁止提现',
        'spot_trading_enabled': '禁止现货交易',
        'margin_trading_enabled': '禁止杠杆交易',
        'perpetual_trading_enabled': '禁止合约交易',
        'margin_loan_enabled': '禁止杠杆借贷',
        'forbidden_margin_accounts': '爆仓中禁止杠杆交易',
        'forbidden_margin_flat': '爆仓中禁止杠杆还币',
        'sub_account_transfer_out_disabled': '禁止子账号向主账号划转',
        'red_packet_disabled_by_risk_control': '风控禁止发红包',
        'p2p_sell_disabled': 'P2P禁止下卖单',
        'p2p_disabled_trans': 'P2P禁止放币',
        'remark': '备注'
    }

    _bool_field = (
        'login_enabled',
        'withdrawals_enabled',
        'spot_trading_enabled',
        'margin_trading_enabled',
        'perpetual_trading_enabled',
        'margin_loan_enabled',
        'sub_account_transfer_out_disabled',
        'red_packet_disabled_by_risk_control',
        'p2p_sell_disabled',
        'p2p_disabled_trans',
    )

    field_names = ('禁止登录', '禁止提现', '禁止现货交易', '禁止杠杆交易', '禁止永续交易',
                   '禁止杠杆借贷', '爆仓中禁止杠杆交易', '爆仓中禁止杠杆还币',
                   '禁止主/子账户划出', '风控禁止发红包', 'P2P禁止下卖单', 'P2P禁止放币')

    source_dict = {
        "ADMIN_FORBID": "客服Admin冻结",
        "MANUAL_BATCH_FORBID": "研发手动冻结",
        "RISK_CONTROL_FORBID": "用户触发风控",
        "USER_SELF_FORBID": "用户自己禁用",
        "RISK_CONTROL_BATCH_FORBID": "风控批量冻结",
    }

    EXPORT_LIMIT = 200000

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "source", Language.ZH_HANS_CN: "冻结类型"},
        {"field": "last_updated_at", Language.ZH_HANS_CN: "最后更新时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user=fields.String,
        type=fields.String,
        remark=fields.String,
        source=EnumField(list(source_dict)),
        start=ex_fields.TimestampField(),
        end=ex_fields.TimestampField(),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """风控-风控用户列表"""
        page, limit = kwargs['page'], kwargs['limit']
        total = 0
        type_ = kwargs.get('type')
        query = UserSetting.query.order_by(
            func.max(UserSetting.updated_at).desc()
        ).filter(
            or_(UserSetting.valid_till.is_(None), UserSetting.valid_till > now())
        ).group_by(
            UserSetting.user_id
        )
        if not type_:
            # 注：修改了此处查询 也要修改下update_risk_user_source_schedule
            query = get_user_setting_risk_query()
        
        query = query.with_entities(
            UserSetting.user_id,
            func.max(UserSetting.updated_at).label("last_updated_at"),
        ).group_by(UserSetting.user_id)
        if keyword := kwargs.get('user', '').strip():
            keyword_results = User.search_for_users(keyword)
            query = query.filter(
                UserSetting.user_id.in_(keyword_results))
        if remark := kwargs.get('remark'):
            remark_search_result = [i.id for i in User.query.filter(User.remark == remark).with_entities(User.id).all()]
            query = query.filter(UserSetting.user_id.in_(remark_search_result))
        if start := kwargs.get('start'):
            query = query.filter(UserSetting.updated_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(UserSetting.updated_at <= end)
        if source := kwargs.get('source', 'RISK_CONTROL_FORBID'):  # 默认给个值，不然会加载超时
            # 按来源搜索时，先单独分页
            if source == "RISK_CONTROL_FORBID":
                q2 = RiskUser.query.filter(
                    RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
                ).order_by(
                    RiskUser.id.desc()
                ).with_entities(
                    RiskUser.user_id.distinct().label('user_id')
                )
            elif source == "USER_SELF_FORBID":
                q2 = RiskUserSource.query.filter(
                    RiskUserSource.source.in_(
                        [RiskUserSource.Source.USER_FREEZE, RiskUserSource.Source.USER_SIGN_OFF,
                         RiskUserSource.Source.RESET_SECURITY]
                    ),
                    RiskUserSource.status == RiskUserSource.Status.VALID,
                ).order_by(RiskUserSource.updated_at.desc()).with_entities(RiskUserSource.user_id)
                if start:
                    q2 = q2.filter(RiskUserSource.updated_at >= start)
                if end:
                    q2 = q2.filter(RiskUserSource.updated_at <= end)
                # page2 = q2.paginate(page, limit, error_out=False)
            else:
                q2 = RiskUserSource.query.filter(
                    RiskUserSource.source == source,
                    RiskUserSource.status == RiskUserSource.Status.VALID,
                ).order_by(RiskUserSource.updated_at.desc()).with_entities(RiskUserSource.user_id)
                if start:
                    q2 = q2.filter(RiskUserSource.updated_at >= start)
                if end:
                    q2 = q2.filter(RiskUserSource.updated_at <= end)
                # page2 = q2.paginate(page, limit, error_out=False)
            if not kwargs.get("export"):
                page2 = q2.paginate(page, limit, error_out=False)
                total = page2.total
                search_user_ids = {i.user_id for i in page2.items}
            else:
                page2 = q2.all()
                total = len(page2)
                search_user_ids = {i.user_id for i in page2}
            page = 1
            query = query.filter(UserSetting.user_id.in_(search_user_ids))

        if type_:
            conditions = cls._field_map[type_]
            if isinstance(conditions, str):
                cond = UserSetting.key == conditions
            else:
                cond = or_(*[UserSetting.key == c for c in conditions])
            if type_ in cls._bool_field:
                field = UserSettings.margin_loan_enabled
                query = query.filter(
                    cond,
                    UserSetting.value == (field.db_value(True) if type_ != field.name else field.db_value(False))
                )
            else:
                query = query.filter(
                    cond,
                    func.char_length(UserSetting.value) > 2
                )

        if export := kwargs.get("export"):
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            records = query.paginate(page, limit, error_out=False)
            rows = records.items
            total = total if source else records.total
        rows = [i for i in rows if i.user_id]

        res = []
        _fields = list(cls._field_map.keys())
        user_updated_at_map = {i.user_id: i.last_updated_at for i in rows}
        for user_ids in batch_iter(user_updated_at_map.keys(), 5000):
            users = User.query.filter(
                User.id.in_(user_ids)
            ).with_entities(User.id, User.email, User.remark).all()

            user_risk_source_map, user_risk_remark_map = cls.get_user_risk_source_and_remark_map(user_ids)
            user_total_usd_map = cls.get_user_total_usd_map(user_ids)

            user_map = {u.id: dict(email=u.email, remark=u.remark) for u in users}
            for user_id in user_ids:
                settings = UserSettings(user_id)
                data = {item: bool(getattr(settings, item)) for item in _fields}
                res.append(dict(
                    user_id=user_id,
                    last_updated_at=user_updated_at_map[user_id],
                    email=user_map[user_id]['email'],
                    total_usd=user_total_usd_map.get(user_id, 0),
                    remark=user_risk_remark_map.get(user_id) or user_map[user_id]['remark'],
                    source=user_risk_source_map.get(user_id),
                    **data
                ))

        if export:
            direct_fields = ["forbidden_margin_accounts", "forbidden_margin_flat",
                             "sub_account_transfer_out_disabled",
                             "red_packet_disabled_by_risk_control",
                             "p2p_sell_disabled", "p2p_disabled_trans"]
            for i in res:
                i["last_updated_at"] = datetime_to_utc8_str(i["last_updated_at"])
                if isinstance(i["source"], Enum):
                    i["source"] = i["source"].value if i["source"] else ""
                for _f in _fields:
                    if _f in direct_fields:
                        i[_f] = "是" if i.get(_f) else "否"
                    else:
                        i[_f] = "否" if i.get(_f) else "是"
            export_headers = list(cls.export_headers)
            export_headers.extend([
                {"field": k, Language.ZH_HANS_CN: v} for k, v in
                dict(zip(_fields, cls.field_names)).items()
            ])
            return export_xlsx(
                filename='risk_user_list',
                data_list=res,
                export_headers=tuple(export_headers),
            )

        return dict(
            items=res,
            total=total,
            types=dict(zip(_fields, cls.field_names)),
            source_dict=cls.source_dict,
        )

    @classmethod
    def post(cls, **kwargs):
        file_ = request.files.get('file')
        file_columns = ["display", "lang", "title"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        return rows

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        remark=fields.String(missing=""),
    ))
    def patch(cls, **kwargs):
        """风控-风控用户列表-修改备注"""
        user_id = kwargs['user_id']
        remark = kwargs['remark']
        RiskUser.query.filter(
            RiskUser.user_id == user_id
        ).update(
            {"remark": remark},
            synchronize_session=False
        )
        # 只更新批量风控的remark
        RiskUserSource.query.filter(
            RiskUserSource.user_id == user_id,
            RiskUserSource.source == RiskUserSource.Source.RISK_CONTROL_BATCH_FORBID,
        ).update(
            {"remark": remark},
            synchronize_session=False
        )
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RiskUser,
            old_data=dict(remark=''),
            new_data=dict(remark=remark),
            target_user_id=user_id,
        )

    @classmethod
    def get_user_risk_source_and_remark_map(cls, user_ids: List[int]) -> Tuple[Dict, Dict]:
        """ 获取用户冻结来源和冻结备注 """
        user_source_map = {}
        all_risk_rows = RiskUser.query.filter(
            RiskUser.user_id.in_(user_ids),
        ).with_entities(RiskUser.user_id, RiskUser.reason, RiskUser.remark, RiskUser.status).all()
        risk_rows = [i for i in all_risk_rows if i.status in (RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED)]
        user_source_map.update({i.user_id: RiskUserSource.Source.RISK_CONTROL_FORBID for i in risk_rows})

        remain_user_ids = set(user_ids) - set(user_source_map)
        source_rows = RiskUserSource.query.filter(
            RiskUserSource.user_id.in_(remain_user_ids),
            RiskUserSource.status == RiskUserSource.Status.VALID,
        ).with_entities(RiskUserSource.user_id, RiskUserSource.source, RiskUserSource.remark).all()
        user_source_map.update({item.user_id: item.source for item in source_rows})

        remain_user_ids = remain_user_ids - set(user_source_map)
        now_ = now()

        setting_rows = UserSetting.query.filter(
            UserSetting.user_id.in_(remain_user_ids),
            or_(UserSetting.valid_till.is_(None), UserSetting.valid_till > now_),
            UserSetting.key.in_([UserSettings.withdrawals_disabled_after_withdraw_password_editing.name,
                                 UserSettings.withdrawals_disabled_after_security_editing.name]),
            UserSetting.value == UserSettings.withdrawals_disabled_after_security_editing.db_value(True),
        ).with_entities(UserSetting.user_id.distinct().label('user_id')).all()
        user_source_map.update({i.user_id: RiskUserSource.Source.RESET_SECURITY for i in setting_rows})

        remain_user_ids = remain_user_ids - set(user_source_map)
        op_rows = OperationLog.query.filter(
            OperationLog.user_id.in_(remain_user_ids),
            OperationLog.operation.in_(
                [OperationLog.Operation.FORBID_ACCOUNT.name, OperationLog.Operation.SIGN_OFF.name]),
        ).with_entities(
            OperationLog.user_id, OperationLog.operation, OperationLog.platform
        ).order_by(OperationLog.id.desc()).all()
        platforms = {i.value for i in RequestPlatform}
        for r in op_rows:
            if r.user_id in user_source_map:
                continue
            if r.platform not in platforms:
                user_source_map[r.user_id] = RiskUserSource.Source.MANUAL_BATCH_FORBID
            elif r.operation == OperationLog.Operation.SIGN_OFF.name:
                user_source_map[r.user_id] = RiskUserSource.Source.USER_SIGN_OFF
            else:
                if UserPreferences(r.user_id).self_forbid:
                    user_source_map[r.user_id] = RiskUserSource.Source.USER_FREEZE

        admin_op_ids = AdminOperationLog.query.filter(
            AdminOperationLog.target_user_id.in_(remain_user_ids),
            AdminOperationLog.namespace == OPNamespaceObjectUser.Setting.namespace.name,
            AdminOperationLog.object == OPNamespaceObjectUser.Setting.object.name,
            AdminOperationLog.operation == AdminOperationLog.Operation.EDIT,
        ).with_entities(
            AdminOperationLog.target_user_id
        ).distinct().all()
        admin_op_ids = [x.target_user_id for x in admin_op_ids]
        # 有操作记录的归类于客服admin冻结
        user_source_map.update({i: RiskUserSource.Source.ADMIN_FORBID for i in admin_op_ids})

        # 其他无法归类的数据：都归类于研发手动冻结
        remain_user_ids = remain_user_ids - set(user_source_map)
        user_source_map.update({i: RiskUserSource.Source.MANUAL_BATCH_FORBID for i in remain_user_ids})

        user_risk_reasons_map = defaultdict(set)
        for r in risk_rows:
            user_risk_reasons_map[r.user_id].add(RiskInfosResource.enums["reasons"].get(r.reason.value, r.reason.value))
        user_remark_map = {}
        for user_id, reasons in user_risk_reasons_map.items():
            user_remark_map[user_id] = "、".join(reasons)
        for user_id, source in user_source_map.items():
            if source == RiskUserSource.Source.USER_SIGN_OFF:
                user_source_map[user_id] = "用户自己禁用"
                user_remark_map[user_id] = "用户注销"
            elif source == RiskUserSource.Source.USER_FREEZE:
                user_source_map[user_id] = "用户自己禁用"
                user_remark_map[user_id] = "用户冻结"
            elif source == RiskUserSource.Source.RESET_SECURITY:
                user_source_map[user_id] = "用户自己禁用"
                user_remark_map[user_id] = "重置安全工具"
        for r in source_rows:
            if r.remark:
                user_remark_map[r.user_id] = r.remark
        for r in all_risk_rows:
            # 覆盖remark
            if r.remark:
                user_remark_map[r.user_id] = r.remark
        return user_source_map, user_remark_map

    @classmethod
    def get_user_total_usd_map(cls, user_ids: List[int]) -> Dict:
        ts = int(today_datetime().timestamp())
        if not ExchangeLogDB.user_account_balance_sum_table(ts).exists():
            ts = ts - 86400

        # lower than 5 usd do not query the data
        result = UserTotalBalanceHelper.get_users_total_balance(ts, user_ids)
        return {k: v for k, v in result.items() if v >= 5}



@ns.route('/risk-users/template')
@respond_with_code
class RiskUsersTemplateResource(Resource):

    class Choice:
        YES = '是'
        NO = '否'


    EXCLUDE_FIELDS = ('forbidden_margin_accounts', 'forbidden_margin_flat',
                     'sub_account_transfer_out_disabled',
                     'red_packet_disabled_by_risk_control')

    @classmethod
    def get(cls):
        """风控-风控用户列表-模板下载"""
        export_headers = [
            # {"field": 'user_id', Language.ZH_HANS_CN: '用户ID', Language.EN_US: 'user_id'},
            # {"field": 'email', Language.ZH_HANS_CN: '邮箱', Language.EN_US: 'email'},
        ]
        for k, v in RiskUsersResource.field_trans_map.items():
            if k in cls.EXCLUDE_FIELDS:
                continue
            export_headers.append(
                {"field": k, Language.ZH_HANS_CN: v, Language.EN_US: 'k'}
            )
        return export_xlsx(
            filename='risk-users-template',
            data_list=[],
            export_headers=export_headers
        )

    @classmethod
    def post(cls):
        """风控-风控用户列表-批量更新"""
        if not (file := request.files.get('file')):
            raise InvalidArgument

        f = []
        for k, v in RiskUsersResource.field_trans_map.items():
            if k in cls.EXCLUDE_FIELDS:
                continue
            f.append(v)

        records = get_table_rows(file, f)

        reversed_field_trans_map = {v: k for k, v in RiskUsersResource.field_trans_map.items()}
        for i in range(len(records)):
            records[i] = {reversed_field_trans_map[k]: v for k, v in records[i].items()}
        emails = [x['email'] for x in records if x['email']]
        email_users = User.query.filter(User.email.in_(emails)).with_entities(User.email, User.id).all()
        email_user_id_map = dict(email_users)
        for record in records:
            if email:= record.get('email'):
                record['user_id'] = email_user_id_map.get(email)
        setting_field_map = RiskUsersResource.field_admin_setting_map['fields']
        direct_fields = RiskUsersResource.field_admin_setting_map['direct_fields']
        field_trans_map = RiskUsersResource.field_trans_map
        count = 0
        user_result_map = defaultdict(dict)
        for item in records:
            user_id = item['user_id']
            if not user_id:
                continue
            
            item.pop('user_id')
            field_map = {k: v for k, v in item.items() if v and v in (cls.Choice.YES, cls.Choice.NO)}
            for k, v in field_map.items():
                result = True if v == cls.Choice.YES else False
                if k in direct_fields:
                    result = not result
                f = setting_field_map[k]
                UserSettings(user_id).set(setting_field_map[k], result)

                user_result_map[user_id][field_trans_map.get(f, f)] = v
            source_record = RiskUserSource.get_or_create(user_id=user_id)
            source_record: RiskUserSource
            source_record.source = RiskUserSource.Source.RISK_CONTROL_BATCH_FORBID
            source_record.status = RiskUserSource.Status.VALID
            db.session.add(source_record)
            if remark := item.get('remark'):
                RiskUserSource.query.filter(RiskUserSource.user_id == user_id).update(
                    {"remark": remark},
                    synchronize_session=False
                )
            count += 1
        db.session.commit()
        for i, v in user_result_map.items():
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.RiskUser,
                target_user_id=i,
                detail=v
            )
        return dict(total=count)


@ns.route('/users/<int:user_id>')
@respond_with_code
class UserRiskInfoResource(Resource):

    @classmethod
    def get(cls, user_id):
        """用户详情-风控信息"""
        # TODO: 未找到调用处，考虑删除
        result = []
        for permission in RiskUser.Permission:
            if RiskUser.test(user_id, permission):
                result.append(RiskInfosResource.enums['permissions'].get(permission.value))

        return {'risk_info': result}


@ns.route('/investment-balance-check')
@respond_with_code
class InvestmentBalanceCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=InvestmentBalanceCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-理财对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = InvestmentBalanceCheck.query
        if asset:
            query = query.filter(InvestmentBalanceCheck.asset == asset)
        if status:
            query = query.filter(InvestmentBalanceCheck.status == status)
        else:
            query = query.filter(InvestmentBalanceCheck.status != InvestmentBalanceCheck.Status.DELETED)
        if start_time:
            query = query.filter(InvestmentBalanceCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(InvestmentBalanceCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(InvestmentBalanceCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                last_amount=record.last_amount,
                in_amount=record.in_amount,
                out_amount=record.out_amount,
                interest_amount=record.interest_amount,
                current_amount=record.current_amount,
                diff_amount=record.diff_amount
            )
            items.append(item)

        accounts = AssetInvestmentConfig.query.filter(AssetInvestmentConfig.status == AssetInvestmentConfig.StatusType.OPEN).all()
        assets = [x.asset for x in accounts]

        result = dict(
            total=records.total,
            items=items,
            assets=assets,
            statuses={InvestmentBalanceCheck.Status.FAILED.value: '不平',
                      InvestmentBalanceCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-理财对账-对账"""
        asset = kwargs['asset']
        check_investment_balance_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-理财对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = InvestmentBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.InvestmentBalanceCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-理财对账-删除"""
        id_ = kwargs['id']

        record = InvestmentBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = InvestmentBalanceCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.InvestmentBalanceCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/red-packet-check')
@respond_with_code
class RedPacketCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=RedPacketCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-红包对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = RedPacketCheck.query
        if asset:
            query = query.filter(RedPacketCheck.asset == asset)
        if status:
            query = query.filter(RedPacketCheck.status == status)
        else:
            query = query.filter(RedPacketCheck.status != RedPacketCheck.Status.DELETED)
        if start_time:
            query = query.filter(RedPacketCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(RedPacketCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(RedPacketCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                last_remain_amount=record.last_remain_amount,
                send_amount=record.send_amount,
                grab_amount=record.grab_amount,
                return_amount=record.return_amount,
                remain_amount=record.remain_amount,
                diff_amount=record.diff_amount
            )
            items.append(item)

        result = dict(
            total=records.total,
            items=items,
            assets=list_all_assets(),
            statuses={RedPacketCheck.Status.FAILED.value: '不平', RedPacketCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-红包对账-对账"""
        asset = kwargs['asset']
        check_red_packet_remain_amount_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-红包对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = RedPacketCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RedPacketCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-红包对账-删除"""
        id_ = kwargs['id']

        record = RedPacketCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = RedPacketCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RedPacketCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/margin-loan-flat-check')
@respond_with_code
class MarginLoanFlatCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        account_id=fields.Integer,
        asset=fields.String,
        status=ex_fields.EnumField(enum=MarginLoanFlatCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-杠杆对账"""
        account_id = kwargs.get('account_id')
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = MarginLoanFlatCheck.query
        if account_id:
            query = query.filter(MarginLoanFlatCheck.account_id == account_id)
        if asset:
            query = query.filter(MarginLoanFlatCheck.asset == asset)
        if status:
            query = query.filter(MarginLoanFlatCheck.status == status)
        else:
            query = query.filter(MarginLoanFlatCheck.status != MarginLoanFlatCheck.Status.DELETED)
        if start_time:
            query = query.filter(MarginLoanFlatCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(MarginLoanFlatCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(MarginLoanFlatCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                last_unflat_amount=record.last_unflat_amount,
                new_loan_amount=record.new_loan_amount,
                new_flat_amount=record.new_flat_amount,
                unflat_amount=record.unflat_amount,
                diff_amount=record.diff_amount
            )
            items.append(item)

        accounts = MarginAccount.query.filter(MarginAccount.status == MarginAccount.StatusType.OPEN).all()
        assets = {}
        for account in accounts:
            info = MarginAccountHelper.get_account_info(account.id)
            assets[account.id] = [
                info['sell_asset_type'],
                info['buy_asset_type']
            ]
        accounts = {x.id: x.name for x in accounts}

        result = dict(
            total=records.total,
            items=items,
            accounts=accounts,
            assets=assets,
            statuses={MarginLoanFlatCheck.Status.FAILED.value: '不平', MarginLoanFlatCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        account_id=fields.Integer(required=True),
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-杠杆对账-对账"""
        account_id = kwargs['account_id']
        asset = kwargs['asset']
        check_margin_loan_flat_task.delay(account_id, asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-杠杆对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = MarginLoanFlatCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.MarginLoanFlatCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-杠杆对账-删除"""
        id_ = kwargs['id']

        record = MarginLoanFlatCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = MarginLoanFlatCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.MarginLoanFlatCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/perpetual-balance-check')
@respond_with_code
class PerpetualBalanceCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=PerpetualBalanceCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-合约对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = PerpetualBalanceCheck.query
        if asset:
            query = query.filter(PerpetualBalanceCheck.asset == asset)
        if status:
            query = query.filter(PerpetualBalanceCheck.status == status)
        else:
            query = query.filter(PerpetualBalanceCheck.status != PerpetualBalanceCheck.Status.DELETED)
        if start_time:
            query = query.filter(PerpetualBalanceCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(PerpetualBalanceCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(PerpetualBalanceCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                transfer_in_amount=record.transfer_in_amount,
                transfer_out_amount=record.transfer_out_amount,
                server_transfer_in_amount=record.server_transfer_in_amount - record.last_server_transfer_in_amount,
                server_transfer_out_amount=record.server_transfer_out_amount - record.last_server_transfer_out_amount,
                insurance_amount=record.insurance_amount,
                profit_amount=record.profit_amount,
                user_amount=record.user_amount,
                fee_amount=record.fee_amount,
                last_record_amount=record.last_record_amount,
                record_amount=record.record_amount,
                diff_amount=record.diff_amount,
                transfer_in_diff_amount=record.transfer_in_amount - (
                        record.server_transfer_in_amount - record.last_server_transfer_in_amount),
                transfer_out_diff_amount=record.transfer_out_amount - (
                        record.server_transfer_out_amount - record.last_server_transfer_out_amount),
            )
            items.append(item)

        assets = PerpetualCoinTypeCache().read_aside()

        result = dict(
            total=records.total,
            items=items,
            assets=assets,
            statuses={PerpetualBalanceCheck.Status.FAILED.value: '不平',
                      PerpetualBalanceCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-合约对账-对账"""
        asset = kwargs['asset']
        check_perpetual_balance_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-合约对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = PerpetualBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PerpetualBalanceCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-合约对账-删除"""
        id_ = kwargs['id']

        record = PerpetualBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = PerpetualBalanceCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PerpetualBalanceCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/viabtc-trans-threshold-conf')
@respond_with_code
class ViabtcTransThresholdConfsResource(Resource):
    return_fields = {
        'id': mash_fields.Integer,
        'asset': mash_fields.String,
        'remark': mash_fields.String,
        'threshold': DecimalType,
        'risk_control_least_usd': DecimalType,
        'status': mash_fields.String(attribute=lambda x: x.status.name),
        'temp_threshold': DecimalType,
        'temp_threshold_expire_at': ex_fields.TimestampMarshalField(
            attribute=lambda x: x.temp_threshold_expire_at)
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """风控-矿池转入币种阈值配置"""
        page, limit = kwargs.get('page'), kwargs.get('limit')
        asset = kwargs.get('asset')
        model_ = ViaBTCRiskControlConfig
        query = model_.query.filter(
            model_.status == model_.Status.OPEN).order_by(model_.id.desc())
        asset_res = query.with_entities(model_.asset).all()
        assets = [i[0] for i in asset_res]
        if asset:
            query = model_.query.filter(model_.asset == asset)
        res = query.paginate(page, limit)
        items = marshal(res.items, cls.return_fields)
        avg_trans_amount_dic = cls.get_avg_amount(assets)
        current_trans_dic, current_trans_usd_dic = cls.get_current_amount(assets)
        for item in items:
            asset = item['asset']
            item['asset_avg_trans_amount'] = avg_trans_amount_dic.get(asset, 0)
            item['asset_current_trans'] = current_trans_dic.get(asset, 0)
            item['asset_current_trans_usd'] = current_trans_usd_dic.get(asset, '0')
        return dict(
            total=res.total,
            items=items,
            extra=assets
        )

    @classmethod
    def get_avg_amount(cls, assets):
        asset_amount_dic = dict()
        static_days = ViaBTCRiskControlConfig.RISK_CONTROL_ASSET_STATIC_DAYS
        model_ = DailyViaBTCPoolOrderSummary
        for asset in assets:
            res = model_.query.filter(
                model_.asset == asset,
                model_.amount > 0
            ).with_entities(
                model_.asset,
                model_.amount
            ).order_by(
                model_.id.desc()
            ).limit(static_days).all()
            avg_amount = sum([amount for asset, amount in res]) / len(res) if res else 0
            asset_amount_dic[asset] = Decimal(avg_amount)
        return asset_amount_dic

    @classmethod
    def get_current_amount(cls, assets):
        model_ = ViaBTCPoolOrder
        today_ = today_datetime()
        price_map = AssetPrice.get_close_price_map(today_)
        whitelist_users = get_viabtc_deposit_whitelist_users()
        query = model_.query.filter(
            model_.order_at >= today_,
            model_.asset.in_(assets)
        ).with_entities(
            model_.asset,
            model_.user_id,
            model_.amount
        ).all()
        amount_dic = defaultdict(Decimal)
        amount_usd_dic = defaultdict(Decimal)
        for asset, user_id, amount in query:
            if user_id in whitelist_users:
                continue
            amount_dic[asset] += amount
            amount_usd = price_map[asset] * amount
            amount_usd_dic[asset] += amount_usd
        return amount_dic, amount_usd_dic

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        threshold=PositiveDecimalField(required=True),
        risk_control_least_usd=PositiveDecimalField(required=True),
        remark=fields.String(missing='', allow_none=True),
        temp_threshold=PositiveDecimalField(allow_none=True),
        temp_threshold_expire_at=TimestampField(is_ms=True, allow_none=True)
    ))
    def post(cls, **kwargs):
        """风控-新增矿池转入币种阈值配置"""

        asset, threshold, risk_control_least_usd = \
            kwargs["asset"], kwargs["threshold"], kwargs["risk_control_least_usd"]
        remark = kwargs.get('remark')
        temp_threshold = kwargs.get('temp_threshold')
        temp_threshold_expire_at = kwargs.get('temp_threshold_expire_at')
        model_ = ViaBTCRiskControlConfig
        rec = model_.query.filter(model_.asset == asset).first()
        if not rec:
            rec = model_(
                asset=asset,
                threshold=threshold,
                risk_control_least_usd=risk_control_least_usd,
                status=model_.Status.OPEN,
                remark=remark,
                temp_threshold=temp_threshold,
                temp_threshold_expire_at=temp_threshold_expire_at
            )
            db.session.add(rec)
        elif rec.status == model_.Status.OPEN:
            raise InvalidArgument(message='该币种配置已存在，无法新增！')
        else:
            rec.threshold = threshold
            rec.risk_control_least_usd = risk_control_least_usd
            rec.status = model_.Status.OPEN
            rec.remark = remark
            rec.temp_threshold = temp_threshold
            rec.temp_threshold_expire_at = temp_threshold_expire_at
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.ViaBTCTransThresholdConf,
            detail=kwargs,
        )


@ns.route('/viabtc-trans-threshold-conf/<int:id_>')
@respond_with_code
class ViabtcTransThresholdConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        threshold=PositiveDecimalField(required=True),
        risk_control_least_usd=PositiveDecimalField(required=True),
        remark=fields.String(missing='', allow_none=True),
        temp_threshold=PositiveDecimalField(allow_none=True),
        temp_threshold_expire_at=TimestampField(is_ms=True, allow_none=True)
    ))
    def put(cls, id_, **kwargs):
        """风控-修改币种风控阈值配置"""
        params = Struct(**kwargs)
        model_ = ViaBTCRiskControlConfig
        rec = model_.query.get(id_)
        old_data = rec.to_dict(enum_to_name=True)
        rec.threshold = params.threshold
        rec.risk_control_least_usd = params.risk_control_least_usd
        rec.remark = params.remark or ''
        rec.temp_threshold = params.temp_threshold
        rec.temp_threshold_expire_at = params.temp_threshold_expire_at

        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.ViaBTCTransThresholdConf,
            old_data=old_data,
            new_data=rec.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """风控-取消币种风控阈值配置"""
        model_ = ViaBTCRiskControlConfig
        rec = model_.query.get(id_)
        rec.status = model_.Status.CLOSE
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.ViaBTCTransThresholdConf,
            detail=rec.to_dict(enum_to_name=True),
        )


@ns.route('/risk-config-setting')
@respond_with_code
class RiskControlSettingResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        config_data=fields.Dict(required=True),
        group_name=fields.String
    ))
    def put(cls, **kwargs):
        """风控-修改风控参数配置"""
        group_name = kwargs.get("group_name", "")
        with CacheLock(LockKeys.risk_config_group(group_name), wait=False):
            config_data = kwargs["config_data"]
            risk_config = RiskControlGroupConfig()
            if group_name:
                group_fields = risk_config.group_fields()
                if group_name not in group_fields:
                    raise InvalidArgument(message=f"group config {group_name} does not exist")
                for k in config_data.keys():
                    if k not in group_fields[group_name].field_group:
                        raise InvalidArgument(message=f"group {group_name} config {k} does not exist")
                old_data = risk_config.get_many(group_name)
                risk_config.set_many(group_name, config_data)
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.RiskConfigSetting,
                    old_data=dict(old_data),
                    new_data=dict(risk_config.get_many(group_name)),
                    special_data=dict(group_name=group_name),
                )
            else:
                for k in config_data.keys():
                    if k not in chain(risk_config.fields(), risk_config.group_fields()):
                        raise InvalidArgument(message=f"config {k} does not exist")
                for k, v in config_data.items():
                    old_value = getattr(risk_config, k)
                    setattr(risk_config, k, v)
                    AdminOperationLog.new_edit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectRisk.RiskConfigSetting,
                        old_data=dict(value=old_value),
                        new_data=dict(value=v),
                        special_data=dict(key=k),
                    )

    @classmethod
    def get(cls):
        """风控-获取所有风控参数配置"""
        risk_config = RiskControlGroupConfig()
        single_fields = [
            {
                "name": name,
                "desc": field.desc,
                "value": getattr(risk_config, name),
                "detail": field.meta.get("detail") or name,
                "type": field.type.__name__,
            }
            for name, field in risk_config.fields().items()
        ]
        group_fields = [
            {
                "name": group_name,
                "desc": group.desc,
                "fields": [
                    {
                        "name": field_name,
                        "value": getattr(risk_config, group_name)[field_name],
                        "desc": field.desc,
                        "detail": field.meta.get("detail", ""),
                        "type": field.type.__name__,
                    }
                    for field_name, field in group.field_group.items()
                ]
            }
            for group_name, group in risk_config.group_fields().items()
        ]
        return {
            "single_fields": single_fields,
            "group_fields": group_fields,
            "extra": {
                "spot_markets": MarketCache.list_online_markets(),
                "perpetual_markets": PerpetualMarketCache().get_market_list()
            }
        }


@ns.route('/market-volatility-risk')
@respond_with_code
class MarketVolatilityRiskResource(Resource):

    @classmethod
    def get(cls):
        """风控-获取市场异常波动风控参数配置"""
        risk_config = MarketVolatilityRiskConfig()
        return risk_config.format_desc()

    @classmethod
    @ns.use_kwargs(dict(
        config_data=fields.Dict(required=True),
    ))
    def put(cls, **kwargs):
        """风控-修改市场异常波动风控参数配置"""
        with CacheLock(LockKeys.risk_config_group("market-volatility"), wait=False):
            config_data = kwargs["config_data"]
            risk_config = MarketVolatilityRiskConfig()
            for k in config_data.keys():
                if k not in risk_config.fields():
                    raise InvalidArgument(message=f"config {k} does not exist")
            for config_name, value_list in config_data.items():
                for idx, value in enumerate(value_list):
                    for k, v in value.items():
                        err = InvalidArgument(message=f"config {config_name} idx {idx} key {k} value {v} is error")
                        try:
                            field = MarketVolatilityRiskConfig.new_market_volatility[k]
                            value[k] = field._type(v)
                            if not field.validate(value[k]):
                                raise err
                        except Exception:
                            raise err
                old_value = getattr(risk_config, config_name)
                setattr(risk_config, config_name, value_list)
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.MarketVolatilityRisk,
                    old_data=dict(value=old_value),
                    new_data=dict(value=value_list),
                    special_data=dict(config_name=config_name),
                )


@ns.route('/withdrawal-fuse-settings')
@respond_with_code
class WithdrawalFuseSettingsResource(Resource):

    @classmethod
    def get(cls):
        """风控-全站提现熔断阈值配置列表"""
        cache_data = WithdrawalFuseStatisticsCache().hgetall()
        fields_data = WithdrawalFuseConfig.fields_and_values_json
        normal_configs = [
            v for v in fields_data
            if not v.get("meta", {}).get("support_valid_intervals", False)
        ]
        time_configs = [
            v for v in fields_data
            if v.get("meta", {}).get("support_valid_intervals", False)
        ]
        return dict(
            configs=normal_configs,
            time_configs=time_configs,
            statistics=[
                {
                    "name": "latest_24_hours_usd",
                    "desc": "近24H提现市值",
                    "detail": "最近24H全站提现市值，折算成USD",
                    "value": cache_data.get("latest_24h_usd", 0),
                },
                {
                    "name": "latest_24_hours_count",
                    "desc": "近24H提现笔数",
                    "detail": "最近24H全站提现笔数",
                    "value": cache_data.get("latest_24h_count", 0),
                },
                {
                    "name": "last_average_update_ts",
                    "desc": "近24小时数据更新时间",
                    "detail": "近24小时数据更新时间",
                    "value": int(cache_data.get("latest_24h_update_ts", 0)),
                },
                {
                    "name": "last_7d_average_usd",
                    "desc": "日均提现市值",
                    "detail": "最近7日（不包括当日）全站提现市值/7，折算成USD",
                    "value": cache_data.get("last_7d_average_usd", 0),
                },
                {
                    "name": "last_7d_average_count",
                    "desc": "日均提现笔数",
                    "detail": "最近7日（不包括当日）全站提现笔数/7",
                    "value": cache_data.get("last_7d_average_count", 0),
                },
                {
                    "name": "latest_24h_update_ts",
                    "desc": "日均数据更新时间",
                    "detail": "日均数据更新时间",
                    "value": int(cache_data.get("last_average_update_ts", 0)),
                },
            ]
        )


# noinspection PyUnresolvedReferences
@ns.route('/withdrawal-fuse-settings/<field>')
@respond_with_code
class WithdrawalFuseSettingsManagementResource(Resource):

    @classmethod
    def get(cls, field):
        """风控-全站提现熔断阈值配置详情"""
        try:
            WithdrawalFuseConfig.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True),
        valid_from=TimestampField(is_ms=True, allow_none=True, required=False),
        valid_till=TimestampField(is_ms=True, allow_none=True, required=False),
    ))
    def put(cls, field, **kwargs):
        """风控-全站提现熔断阈值配置编辑"""
        value = kwargs['value']
        _field_obj = WithdrawalFuseConfig.get_field(field)
        if _field_obj.meta.get("support_valid_intervals"):
            if "valid_from" not in kwargs or "valid_till" not in kwargs:
                raise InvalidArgument
            valid_from = kwargs["valid_from"]
            valid_till = kwargs["valid_till"]
            if valid_from > valid_till:
                raise InvalidArgument
        else:
            valid_from = None
            valid_till = None
        old_valid_interval = WithdrawalFuseConfig.get_valid_interval(field)
        try:
            WithdrawalFuseConfig.set(field, value,
                                     valid_from=valid_from,
                                     valid_till=valid_till)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.WithdrawalFuseConfig,
            old_data=dict(value='', valid_interval=old_valid_interval),
            new_data=dict(value=value, valid_interval=WithdrawalFuseConfig.get_valid_interval(field)),
            special_data=dict(field_name=field),
        )

        return WithdrawalFuseConfig.get_field_and_value_json(field)

    @classmethod
    def delete(cls, field):
        """风控-全站提现熔断阈值配置删除"""
        try:
            WithdrawalFuseConfig.delete(field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.WithdrawalFuseConfig,
            detail=dict(field=field),
        )

        return dict(
            value=getattr(WithdrawalFuseConfig, field)
        )


@ns.route('/deposit-fuse-settings')
@respond_with_code
class DepositFuseSettingsResource(Resource):

    @classmethod
    def get(cls):
        """风控-全站充值熔断阈值配置列表"""
        cache_data = NewDepositFuseStatisticsCache().hgetall()
        fields_data = DepositFuseConfig.fields_and_values_json
        normal_configs = [
            v for v in fields_data
            if not v.get("meta", {}).get("support_valid_intervals", False)
        ]
        time_configs = [
            v for v in fields_data
            if v.get("meta", {}).get("support_valid_intervals", False)
        ]
        return dict(
            configs=normal_configs,
            time_configs=time_configs,
            statistics=[
                {
                    "name": "latest_24_hours_usd",
                    "desc": "近24H充值市值",
                    "detail": "最近24H全站充值市值，折算成USD",
                    "value": cache_data.get("latest_24h_usd", 0),
                },
                {
                    "name": "last_average_update_ts",
                    "desc": "近24小时数据更新时间",
                    "detail": "近24小时数据更新时间",
                    "value": int(cache_data.get("latest_24h_update_ts", 0)),
                },
                {
                    "name": "last_7d_average_usd",
                    "desc": "日均充值市值",
                    "detail": "最近7日（不包括当日）全站充值市值/7，折算成USD",
                    "value": cache_data.get("last_7d_average_usd", 0),
                },
                {
                    "name": "latest_24h_update_ts",
                    "desc": "日均数据更新时间",
                    "detail": "日均数据更新时间",
                    "value": int(cache_data.get("last_average_update_ts", 0)),
                },
            ]
        )


# noinspection PyUnresolvedReferences
@ns.route('/deposit-fuse-settings/<field>')
@respond_with_code
class DepositFuseSettingsManagementResource(Resource):

    @classmethod
    def get(cls, field):
        """风控-全站充值熔断阈值配置详情"""
        try:
            DepositFuseConfig.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True),
        valid_from=TimestampField(is_ms=True, allow_none=True, required=False),
        valid_till=TimestampField(is_ms=True, allow_none=True, required=False),
    ))
    def put(cls, field, **kwargs):
        """风控-全站充值熔断阈值配置编辑"""
        value = kwargs['value']
        _field_obj = DepositFuseConfig.get_field(field)
        if _field_obj.meta.get("support_valid_intervals"):
            if "valid_from" not in kwargs or "valid_till" not in kwargs:
                raise InvalidArgument
            valid_from = kwargs["valid_from"]
            valid_till = kwargs["valid_till"]
            if valid_from > valid_till:
                raise InvalidArgument
        else:
            valid_from = None
            valid_till = None
        old_valid_interval = DepositFuseConfig.get_valid_interval(field)
        try:
            DepositFuseConfig.set(field, value,
                                  valid_from=valid_from,
                                  valid_till=valid_till)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DepositFuseConfig,
            old_data=dict(value='', valid_interval=old_valid_interval),
            new_data=dict(value=value, valid_interval=DepositFuseConfig.get_valid_interval(field)),
            special_data=dict(field_name=field),
        )

        return DepositFuseConfig.get_field_and_value_json(field)

    @classmethod
    def delete(cls, field):
        """风控-全站充值熔断阈值配置删除"""
        try:
            DepositFuseConfig.delete(field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DepositFuseConfig,
            detail=dict(field=field),
        )

        return dict(
            value=getattr(DepositFuseConfig, field)
        )


@ns.route('/accumulated-deposit-conf')
@respond_with_code
class AccumulatedDepositThresholdConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-币种累计充值阈值配置"""
        model_ = AssetAccumulatedDepositConfig
        query = model_.query.order_by(model_.rank_min.asc())
        rows = query.all()
        ret = []
        ranks = []
        for idx, row in enumerate(rows, start=1):
            item = row.to_dict()
            item['grade'] = idx
            item['rank_range'] = f'{amount_to_str(row.rank_min)}~{amount_to_str(row.rank_max)}'
            ret.append(item)
            ranks.append({
                'id': row.id,
                'grade': idx,
                'rank_min': row.rank_min,
                'rank_max': row.rank_max,
            })
        return dict(
            items=ret,
            extra=dict(ranks=ranks)
        )

    @classmethod
    @ns.use_kwargs(dict(
        ranks=fields.List(fields.Dict, required=True),
        removes=fields.List(fields.Integer, required=False),
    ))
    def post(cls, **kwargs):
        """风控-币种累计充值阈值档位配置"""
        model_ = AssetAccumulatedDepositConfig
        ranks = kwargs['ranks']
        if len(ranks) < 3:
            raise InvalidArgument(message='至少要配置三档数据')
        last_rank = None
        format_ranks = []
        for rank in ranks:
            id_ = rank.get('id')
            rank_min = rank.get('rank_min')
            rank_max = rank.get('rank_max')
            if not rank_min or not rank_max:
                raise InvalidArgument(message='流通市值区间不能为空')
            rank_min, rank_max = Decimal(rank_min), Decimal(rank_max)
            if rank_min >= rank_max:
                raise InvalidArgument(message='流通市值区间范围需大于 0')
            if last_rank and last_rank > rank_min:
                raise InvalidArgument(message='档位流通市值区间不能交叉')
            if last_rank and last_rank != rank_min:
                raise InvalidArgument(message='档位配置必须连续')
            if id_:
                row = model_.query.get(id_)
                if not row:
                    raise InvalidArgument
            last_rank = rank_max
            format_ranks.append(dict(
                id=id_,
                rank_min=rank_min,
                rank_max=rank_max,
            ))

        for rank in format_ranks:
            id_ = rank.get('id')
            if not id_:
                row = model_(
                    rank_min=rank['rank_min'],
                    rank_max=rank['rank_max'],
                )
                db.session.add(row)
                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(rank_min=rank['rank_min'], rank_max=rank['rank_max']),
                )
            else:
                row = model_.query.get(id_)
                old_data = row.to_dict(enum_to_name=True)
                row.rank_min = rank['rank_min']
                row.rank_max = rank['rank_max']
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    old_data=old_data,
                    new_data=row.to_dict(enum_to_name=True),
                )
        if removes := kwargs.get('removes'):
            model_.query.filter(
                model_.id.in_(removes)
            ).delete()
            for remove_id in removes:
                AdminOperationLog.new_delete(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(id=remove_id),
                )
        db.session.commit()


@ns.route('/accumulated-deposit-conf/<int:id_>')
@respond_with_code
class AccumulatedDepositThresholdDetailConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        deposit_threshold=PositiveDecimalField(required=True),
        user_deposit_threshold=PositiveDecimalField(required=True),
        limit_count_threshold=PositiveDecimalField(required=True),
        limit_amount_threshold=PositiveDecimalField(required=True),
        temp_limit_count_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_amount_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_expire_at=TimestampField(is_ms=True, allow_none=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-币种累计充值阈值档位配置-详情配置"""
        params = Struct(**kwargs)
        model_ = AssetAccumulatedDepositConfig
        row = model_.query.get(id_)
        if params.temp_limit_count_threshold or params.temp_limit_amount_threshold:
            if not params.temp_limit_expire_at:
                raise InvalidArgument(message='临时限制阈值配置时，需同时设置过期时间')
        old_data = row.to_dict(enum_to_name=True)

        row.deposit_threshold = params.deposit_threshold
        row.user_deposit_threshold = params.user_deposit_threshold
        row.limit_count_threshold = params.limit_count_threshold
        row.limit_amount_threshold = params.limit_amount_threshold
        row.temp_limit_count_threshold = params.temp_limit_count_threshold
        row.temp_limit_amount_threshold = params.temp_limit_amount_threshold
        row.temp_limit_expire_at = params.temp_limit_expire_at
        row.remark = params.remark or ''
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/accumulated-deposit-special-conf')
@respond_with_code
class AccumulatedDepositThresholdSpecialConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-特殊币种累计充值阈值配置"""
        model_ = SpecialAssetAccumulatedDepositConfig
        query = model_.query.filter(
            model_.status == model_.Status.OPEN
        ).order_by(model_.id.desc())
        rows = query.all()
        ret = []
        for row in rows:
            item = row.to_dict()
            ret.append(item)
        return dict(
            items=ret,
            extra=dict(
                statuses=model_.Status,
                assets=list_all_assets()
            ),
        )


@ns.route('/accumulated-deposit-special-conf/<int:id_>')
@respond_with_code
class AccumulatedDepositThresholdSpecialDetailConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        deposit_threshold=PositiveDecimalField(required=True),
        user_deposit_threshold=PositiveDecimalField(required=True),
        limit_count_threshold=PositiveDecimalField(required=True),
        limit_amount_threshold=PositiveDecimalField(required=True),
        temp_limit_count_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_amount_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_expire_at=TimestampField(is_ms=True, allow_none=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-特殊币种累计充值阈值档位配置-详情配置"""
        params = Struct(**kwargs)
        model_ = SpecialAssetAccumulatedDepositConfig
        if params.temp_limit_count_threshold or params.temp_limit_amount_threshold:
            if not params.temp_limit_expire_at:
                raise InvalidArgument(message='临时限制阈值配置时，需同时设置过期时间')
        if id_ == 0:
            row = model_.get_or_create(asset=kwargs['asset'])
            db.session.add(row)
        else:
            row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.status = model_.Status.OPEN
        row.asset = params.asset
        row.deposit_threshold = params.deposit_threshold
        row.user_deposit_threshold = params.user_deposit_threshold
        row.limit_count_threshold = params.limit_count_threshold
        row.limit_amount_threshold = params.limit_amount_threshold
        row.temp_limit_count_threshold = params.temp_limit_count_threshold
        row.temp_limit_amount_threshold = params.temp_limit_amount_threshold
        row.temp_limit_expire_at = params.temp_limit_expire_at
        row.remark = params.remark or ''
        db.session.commit()

        if id_ == 0:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                detail=kwargs,
            )
        else:
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

    @classmethod
    def patch(cls, id_):
        """风控-特殊币种累计充值阈值档位配置-关闭"""
        model_ = SpecialAssetAccumulatedDepositConfig
        row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.status = model_.Status.CLOSE
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/accumulated-deposit-proportion-conf')
@respond_with_code
class AccumulatedDepositThresholdProportionConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-币种累计充值阈值配置（流通量占比）"""
        model_ = AssetAccumulatedDepositConfigProportion
        query = model_.query.order_by(model_.rank_min.asc())
        rows = query.all()
        ret = []
        ranks = []
        for idx, row in enumerate(rows, start=1):
            item = row.to_dict()
            item['grade'] = idx
            item['rank_range'] = f'{amount_to_str(row.rank_min)}~{amount_to_str(row.rank_max)}'
            ret.append(item)
            ranks.append({
                'id': row.id,
                'grade': idx,
                'rank_min': row.rank_min,
                'rank_max': row.rank_max,
            })
        return dict(
            items=ret,
            extra=dict(ranks=ranks)
        )

    @classmethod
    @ns.use_kwargs(dict(
        ranks=fields.List(fields.Dict, required=True),
        removes=fields.List(fields.Integer, required=False),
    ))
    def post(cls, **kwargs):
        """风控-币种累计充值阈值档位配置（流通量占比）"""
        model_ = AssetAccumulatedDepositConfigProportion
        ranks = kwargs['ranks']
        if len(ranks) < 3:
            raise InvalidArgument(message='至少要配置三档数据')
        last_rank = None
        format_ranks = []
        for rank in ranks:
            id_ = rank.get('id')
            rank_min = rank.get('rank_min')
            rank_max = rank.get('rank_max')
            if not rank_min or not rank_max:
                raise InvalidArgument(message='流通市值区间不能为空')
            rank_min, rank_max = Decimal(rank_min), Decimal(rank_max)
            if rank_min >= rank_max:
                raise InvalidArgument(message='流通市值区间范围需大于 0')
            if last_rank and last_rank > rank_min:
                raise InvalidArgument(message='档位流通市值区间不能交叉')
            if last_rank and last_rank != rank_min:
                raise InvalidArgument(message='档位配置必须连续')
            if id_:
                row = model_.query.get(id_)
                if not row:
                    raise InvalidArgument
            last_rank = rank_max
            format_ranks.append(dict(
                id=id_,
                rank_min=rank_min,
                rank_max=rank_max,
            ))

        for rank in format_ranks:
            id_ = rank.get('id')
            if not id_:
                row = model_(
                    rank_min=rank['rank_min'],
                    rank_max=rank['rank_max'],
                )
                db.session.add(row)
                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(rank_min=rank['rank_min'], rank_max=rank['rank_max']),
                )
            else:
                row = model_.query.get(id_)
                old_data = row.to_dict(enum_to_name=True)
                row.rank_min = rank['rank_min']
                row.rank_max = rank['rank_max']
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    old_data=old_data,
                    new_data=row.to_dict(enum_to_name=True),
                )
        if removes := kwargs.get('removes'):
            model_.query.filter(
                model_.id.in_(removes)
            ).delete()
            for remove_id in removes:
                AdminOperationLog.new_delete(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(id=remove_id),
                )
        db.session.commit()


@ns.route('/accumulated-deposit-proportion-conf/<int:id_>')
@respond_with_code
class AccumulatedDepositThresholdDetailProportionConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        # user_period=fields.Integer(required=True),
        user_period_dp=PositiveDecimalField(required=True),
        # user_single_dp=PositiveDecimalField(required=True),
        asset_period=fields.Integer(required=True),
        asset_period_dp=PositiveDecimalField(required=True),
        user_deposit_threshold=PositiveDecimalField(required=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-币种累计充值阈值档位配置（流通量占比）-详情配置"""
        params = Struct(**kwargs)
        model_ = AssetAccumulatedDepositConfigProportion
        row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        # row.user_period = params.user_period
        row.user_period_dp = params.user_period_dp
        # row.user_single_dp = params.user_single_dp
        row.asset_period = params.asset_period
        row.asset_period_dp = params.asset_period_dp
        row.user_deposit_threshold = params.user_deposit_threshold
        row.remark = params.remark or ''
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/accumulated-deposit-special-proportion-conf')
@respond_with_code
class AccumulatedDepositThresholdSpecialProportionConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-特殊币种累计充值阈值配置（流通量占比）"""
        model_ = SpecialAssetAccumulatedDepositConfigProportion
        query = model_.query.filter(
            model_.status == model_.Status.OPEN
        ).order_by(model_.id.desc())
        rows = query.all()
        ret = []
        for row in rows:
            item = row.to_dict()
            ret.append(item)
        return dict(
            items=ret,
            extra=dict(
                statuses=model_.Status,
                assets=list_all_assets()
            ),
        )


@ns.route('/accumulated-deposit-special-proportion-conf/<int:id_>')
@respond_with_code
class AccumulatedDepositThresholdSpecialDetailProportionConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        user_period=fields.Integer(missing=0),
        user_period_dp=PositiveDecimalField(required=True),
        user_single_dp=PositiveDecimalField(missing=0),
        asset_period=fields.Integer(required=True),
        asset_period_dp=PositiveDecimalField(required=True),
        user_deposit_threshold=PositiveDecimalField(required=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-特殊币种累计充值阈值档位配置（流通量占比）-详情配置"""
        params = Struct(**kwargs)
        model_ = SpecialAssetAccumulatedDepositConfigProportion
        if id_ == 0:
            row = model_(**kwargs)
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                detail=kwargs,
            )
        else:
            row = model_.query.get(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.asset = params.asset
            row.user_period = params.user_period or Decimal()
            row.user_period_dp = params.user_period_dp
            row.user_single_dp = params.user_single_dp or Decimal()
            row.asset_period = params.asset_period
            row.asset_period_dp = params.asset_period_dp
            row.user_deposit_threshold = params.user_deposit_threshold
            row.remark = params.remark or ''

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        db.session.commit()

    @classmethod
    def patch(cls, id_):
        """风控-特殊币种累计充值阈值档位配置（流通量占比）-关闭"""
        model_ = SpecialAssetAccumulatedDepositConfigProportion
        row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.status = model_.Status.CLOSE
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/accumulated-withdrawal-conf')
@respond_with_code
class AccumulatedWithdrawalThresholdConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-币种累计提现阈值配置"""
        model_ = AssetAccumulatedWithdrawalConfig
        query = model_.query.order_by(model_.circulation_min.asc())
        rows = query.all()
        ret = []
        circulations = []
        for idx, row in enumerate(rows, start=1):
            item = row.to_dict()
            item['grade'] = idx
            item['circulation_range'] = f'{amount_to_str(row.circulation_min)}~{amount_to_str(row.circulation_max)}'
            ret.append(item)
            circulations.append({
                'id': row.id,
                'grade': idx,
                'circulation_min': amount_to_str(row.circulation_min, PrecisionEnum.COIN_PLACES),
                'circulation_max': amount_to_str(row.circulation_max, PrecisionEnum.COIN_PLACES)
            })
        return dict(
            items=ret,
            extra=dict(circulations=circulations)
        )

    @classmethod
    @ns.use_kwargs(dict(
        circulations=fields.List(fields.Dict, required=True),
        removes=fields.List(fields.Integer, required=False),
    ))
    def post(cls, **kwargs):
        """风控-币种累计提现阈值档位配置"""
        model_ = AssetAccumulatedWithdrawalConfig
        circulations = kwargs['circulations']
        if len(circulations) < 3:
            raise InvalidArgument(message='至少要配置三档数据')
        last_circulation = None
        for circulation in circulations:
            id_ = circulation.get('id')
            circulation_min = circulation.get('circulation_min')
            circulation_max = circulation.get('circulation_max')
            if not circulation_min or not circulation_max:
                raise InvalidArgument(message='流通市值配置不能为空')
            circulation_min, circulation_max = Decimal(circulation_min), Decimal(circulation_max)
            if circulation_min >= circulation_max:
                raise InvalidArgument(message='区间配置有误')
            if last_circulation and last_circulation != circulation_min:
                raise InvalidArgument(message='档位配置必须连续')
            if id_:
                row = model_.query.get(id_)
                if not row:
                    raise InvalidArgument
            last_circulation = circulation_max

        for circulation in circulations:
            id_ = circulation.get('id')
            if not id_:
                row = model_(
                    circulation_min=circulation['circulation_min'],
                    circulation_max=circulation['circulation_max'],
                )
                db.session.add(row)

                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(
                        circulation_min=circulation['circulation_min'],
                        circulation_max=circulation['circulation_max'],
                    ),
                )
            else:
                row = model_.query.get(id_)
                old_data = row.to_dict(enum_to_name=True)
                row.circulation_min = circulation['circulation_min']
                row.circulation_max = circulation['circulation_max']

                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    old_data=old_data,
                    new_data=row.to_dict(enum_to_name=True),
                )
        if removes := kwargs.get('removes'):
            model_.query.filter(
                model_.id.in_(removes)
            ).delete()
            for remove_id in removes:
                AdminOperationLog.new_delete(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                    detail=dict(id=remove_id),
                )
        db.session.commit()


@ns.route('/accumulated-withdrawal-conf/<int:id_>')
@respond_with_code
class AccumulatedWithdrawalThresholdDetailConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        withdrawal_threshold=PositiveDecimalField(required=True),
        user_withdrawal_threshold=PositiveDecimalField(required=True),
        limit_amount_threshold=PositiveDecimalField(required=True),
        temp_limit_amount_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_expire_at=TimestampField(is_ms=True, allow_none=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-币种累计提现阈值档位配置-详情配置"""
        params = Struct(**kwargs)
        model_ = AssetAccumulatedWithdrawalConfig
        row = model_.query.get(id_)
        if params.temp_limit_count_threshold or params.temp_limit_amount_threshold:
            if not params.temp_limit_expire_at:
                raise InvalidArgument(message='临时限制阈值配置时，需同时设置过期时间')
        old_data = row.to_dict(enum_to_name=True)

        row.withdrawal_threshold = params.withdrawal_threshold
        row.user_withdrawal_threshold = params.user_withdrawal_threshold
        row.limit_amount_threshold = params.limit_amount_threshold
        row.temp_limit_amount_threshold = params.temp_limit_amount_threshold
        row.temp_limit_expire_at = params.temp_limit_expire_at
        row.remark = params.remark or ''
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/accumulated-withdrawal-special-conf')
@respond_with_code
class AccumulatedWithdrwalThresholdSpecialConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-特殊币种累计提现阈值配置"""
        model_ = SpecialAssetAccumulatedWithdrawalConfig
        query = model_.query.filter(
            model_.status == model_.Status.OPEN
        ).order_by(model_.id.desc())
        rows = query.all()
        ret = []
        for row in rows:
            item = row.to_dict()
            ret.append(item)
        return dict(
            items=ret,
            extra=dict(
                statuses=model_.Status,
                assets=list_all_assets()
            ),
        )


@ns.route('/accumulated-withdrawal-special-conf/<int:id_>')
@respond_with_code
class AccumulatedWithdrawalThresholdSpecialDetailConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        withdrawal_threshold=PositiveDecimalField(required=True),
        limit_amount_threshold=PositiveDecimalField(required=True),
        user_withdrawal_threshold=PositiveDecimalField(required=True),
        temp_limit_amount_threshold=PositiveDecimalField(required=False, allow_none=True, allow_zero=True),
        temp_limit_expire_at=TimestampField(is_ms=True, allow_none=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-特殊币种累计提现阈值档位配置-详情配置"""
        params = Struct(**kwargs)
        model_ = SpecialAssetAccumulatedWithdrawalConfig
        if params.temp_limit_count_threshold or params.temp_limit_amount_threshold:
            if not params.temp_limit_expire_at:
                raise InvalidArgument(message='临时限制阈值配置时，需同时设置过期时间')
        if id_ == 0:
            row = model_(**kwargs)
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                detail=kwargs,
            )
        else:
            row = model_.query.get(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.asset = params.asset
            row.withdrawal_threshold = params.withdrawal_threshold
            row.limit_amount_threshold = params.limit_amount_threshold
            row.user_withdrawal_threshold = params.user_withdrawal_threshold
            row.temp_limit_amount_threshold = params.temp_limit_amount_threshold
            row.temp_limit_expire_at = params.temp_limit_expire_at
            row.remark = params.remark or ''

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        db.session.commit()

    @classmethod
    def patch(cls, id_):
        """风控-特殊币种累计提现阈值档位配置-关闭"""
        model_ = SpecialAssetAccumulatedWithdrawalConfig
        row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.status = model_.Status.CLOSE
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AccumulatedDepositConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/p2p-balance-check')
@respond_with_code
class P2pBalanceCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=P2pBalanceCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-p2p对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = P2pBalanceCheck.query
        if asset:
            query = query.filter(P2pBalanceCheck.asset == asset)
        if status:
            query = query.filter(P2pBalanceCheck.status == status)
        else:
            query = query.filter(P2pBalanceCheck.status != P2pBalanceCheck.Status.DELETED)
        if start_time:
            query = query.filter(P2pBalanceCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(P2pBalanceCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(P2pBalanceCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                release_amount=record.release_amount,
                fee_amount=record.fee_amount,
                receive_amount=record.receive_amount + record.fee_amount,
                diff_amount=record.diff_amount,
                diff_volume_usd=quantize_amount(record.diff_volume_usd, 2)
            )
            items.append(item)

        result = dict(
            total=records.total,
            items=items,
            assets=P2pAssetConfigCache.get_assets(),
            statuses={P2pBalanceCheck.Status.FAILED.value: '不平', P2pBalanceCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-p2p对账-对账"""
        asset = kwargs['asset']
        check_p2p_balance_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-p2p对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = P2pBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.P2pBalanceCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-p2p对账-删除"""
        id_ = kwargs['id']

        record = P2pBalanceCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = P2pBalanceCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.P2pBalanceCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/pre-trading-settle-check')
@respond_with_code
class PreTadingSettleCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=PreMarketSettleCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-预测市场对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        model = PreMarketSettleCheck
        query = model.query
        if asset:
            query = query.filter(model.asset == asset)
        if status:
            query = query.filter(model.status == status)
        else:
            query = query.filter(model.status != model.Status.DELETED)
        if start_time:
            query = query.filter(model.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(model.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(model.id.desc()).paginate(page, limit, error_out=False)

        items = []
        for record in records.items:
            record: model
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                expect_issue_settle_amount=record.expect_issue_settle_amount,
                expect_position_settle_amount=record.expect_position_settle_amount,
                expect_settle_amount=record.expect_settle_amount,
                real_settle_amount=record.real_settle_amount,
                diff_amount=record.diff_amount,
                diff_volume_usd=quantize_amount(record.diff_volume_usd, 2),
            )
            items.append(item)

        pre_assets = [i.asset for i in PreTradingAssetConfig.query.with_entities(PreTradingAssetConfig.asset)]
        result = dict(
            total=records.total,
            items=items,
            assets=pre_assets,
            statuses={P2pBalanceCheck.Status.FAILED.value: '不平', P2pBalanceCheck.Status.PASSED.value: '平'},
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """风控-预测市场对账-对账"""
        asset = kwargs['asset']
        check_pre_market_settle_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """风控-预测市场对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = PreMarketSettleCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PreTradingSettleCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
    ))
    def delete(cls, **kwargs):
        """风控-预测市场对账-删除"""
        id_ = kwargs['id']

        record = PreMarketSettleCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = PreMarketSettleCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PreTradingSettleCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/forbid-withdrawals')
@respond_with_code
class ForbidWithdrawalsResource(Resource):

    @classmethod
    def get(cls):
        """风控-资产负债风控币种"""
        asset_chains_map = asset_to_chains()

        assets = {item.asset for item in AssetChainConfig.query.filter(
            AssetChainConfig.key == AssetChainConfigControl.withdrawals_disabled_by_asset_liability.name,
            AssetChainConfig.status == AssetChainConfig.Status.VALID,
        ).all() if item.value == '1' and item.chain in asset_chains_map.get(item.asset, [])}

        last_record = SystemAssetLiability.query.filter().order_by(SystemAssetLiability.id.desc()).first()
        if not last_record:
            updated_at = now()
        else:
            updated_at = last_record.created_at

        last_records = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset.in_(assets),
        ).with_entities(
            func.max(SystemAssetLiability.id).label('id'),
        ).group_by(
            SystemAssetLiability.asset,
        ).all()

        price_map = PriceManager.assets_to_usd(assets)

        items = [
            {
                'asset': row.asset,
                'sys_total': row.sys_total,
                'sys_total_usd': quantize_amount(row.sys_total * price_map.get(row.asset, Decimal()), 4),
                'sys_debt': row.sys_debt,
                'sys_debt_usd': quantize_amount(row.sys_debt * price_map.get(row.asset, Decimal()), 4),
                'sys_income': row.sys_income,
                'sys_income_usd': quantize_amount(row.sys_income * price_map.get(row.asset, Decimal()), 4)
            } for row in SystemAssetLiability.query.filter(
                SystemAssetLiability.id.in_([i.id for i in last_records]),
            ).all()
        ]
        return dict(
            assets=assets,
            items=items,
            updated_at=updated_at,
        )


@ns.route('/bus-online-coin-by-market')
@respond_with_code
class BusOnlineCoinByMarketResource(Resource):

    @classmethod
    def get(cls):
        """风控-商务上币风控阈值配置(市场)"""
        items = [
            dict(
                market=item.market,
                period_by_min=item.period_by_min,
                decrease_threshold=quantize_amount(item.decrease_threshold * Decimal('100'), 2),
                sell_volume_usd_threshold=item.sell_volume_usd_threshold,
            ) for item in BusOnlineCoinRiskConfigByMarket.query.all()
        ]
        return dict(
            markets=MarketCache.list_online_markets(),
            items=items,
        )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        period_by_min=fields.Integer(required=True),
        decrease_threshold=PositiveDecimalField(required=True),
        sell_volume_usd_threshold=PositiveDecimalField(required=True),
    ))
    def post(cls, **kwargs):
        """风控-商务上币风控阈值配置(市场)-新增/编辑"""
        market = kwargs['market']
        period_by_min = kwargs['period_by_min']
        decrease_threshold = quantize_amount(kwargs['decrease_threshold'] / Decimal('100'), 4)
        sell_volume_usd_threshold = kwargs['sell_volume_usd_threshold']

        if market not in MarketCache.list_online_markets():
            raise InvalidArgument(message=f'{market} is not online market')
        if period_by_min <= 0 or period_by_min > 7 * 24 * 60:
            raise InvalidArgument(message='period_by_hour must be greater than 0 and less than 168')
        if decrease_threshold > Decimal('1') or decrease_threshold < Decimal('0'):
            raise InvalidArgument(message='decrease_threshold must be greater than 0 and less than 100')
        if sell_volume_usd_threshold <= Decimal('0'):
            raise InvalidArgument(message='sell_volume_usd_threshold must be greater than 0')

        row = BusOnlineCoinRiskConfigByMarket.query.filter(
            BusOnlineCoinRiskConfigByMarket.market == market,
        ).first()
        if row:
            old_data = row.to_dict(enum_to_name=True)
            row.period_by_min = period_by_min
            row.decrease_threshold = decrease_threshold
            row.sell_volume_usd_threshold = sell_volume_usd_threshold
            db.session.commit()
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.BusOnlineCoinConfigByMarket,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                special_data=dict(market=market),
            )
        else:
            db.session_add_and_commit(BusOnlineCoinRiskConfigByMarket(
                market=market,
                period_by_min=period_by_min,
                decrease_threshold=decrease_threshold,
                sell_volume_usd_threshold=sell_volume_usd_threshold,
            ))
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.BusOnlineCoinConfigByMarket,
                detail=kwargs,
            )

        return {}

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """风控-商务上币风控阈值配置(市场)-删除"""
        market = kwargs['market']
        row = BusOnlineCoinRiskConfigByMarket.query.filter(
            BusOnlineCoinRiskConfigByMarket.market == market,
        ).first()
        if not row:
            raise InvalidArgument(message=f'{market} is not exist')
        db.session.delete(row)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.BusOnlineCoinConfigByMarket,
            detail=kwargs,
        )
        return {}


@ns.route('/country-sms')
@respond_with_code
class CountrySmsResource(Resource):
    model = CountrySmsRiskConfig

    @classmethod
    @ns.use_kwargs(dict(
        mobile_country_code=fields.String(),
        status=EnumField(model.Status),
    ))
    def get(cls, **kwargs):
        """风控-国家短信区号风控列表"""
        query = cls.model.query
        if mobile_country_code := kwargs.get('mobile_country_code'):
            query = query.filter(cls.model.mobile_country_code == mobile_country_code)
        if status:= kwargs.get('status'):
            query = query.filter(cls.model.status == status)

        rows = query.all()
        admin_map = get_admin_user_name_map([i.admin_user_id for i in rows])
        items = []
        country_map = country_mobile_code_to_cn_name()
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item['admin_user_name'] = admin_map.get(row.admin_user_id, "/")
            item['country_name'] = '，'.join(country_map.get(row.mobile_country_code, []))
            items.append(item)

        return dict(
            items=items,
            extra=dict(
                mobile_country_codes=list_mobile_country_codes(),
                country_mobile_map=cn_name_to_country_mobile_code(),
                statuses=cls.model.Status
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        days=fields.Integer(),
        status=EnumField(model.Status),
    ))
    def patch(cls, **kwargs):
        """风控-修改国家短信区号风控"""
        row = cls.model.query.get(kwargs['id'])
        row.admin_user_id = g.user.id
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        if days := kwargs.get('days'):
            row.days = days
        if status := kwargs.get('status'):
            row.status = status
        db.session.commit()
        CountrySmsRiskCodeCache.reload()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.CountrySms,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/wash-sale-risk')
@respond_with_code
class WashSaleRiskResource(Resource):

    @classmethod
    def get(cls):
        """风控-获取现货市场防对敲参数配置"""
        risk_config = WashSaleRiskConfig()
        return risk_config.format_desc()

    @classmethod
    @ns.use_kwargs(dict(
        config_data=fields.Dict(required=True),
    ))
    def put(cls, **kwargs):
        """风控-修改现货市场防对敲风控参数配置"""
        with CacheLock(LockKeys.risk_config_group("wash-sale"), wait=False):
            config_data = kwargs["config_data"]
            risk_config = WashSaleRiskConfig()
            for k in config_data.keys():
                if k not in risk_config.fields():
                    raise InvalidArgument(message=f"config {k} does not exist")
            for config_name, value_list in config_data.items():
                for idx, value in enumerate(value_list):
                    for k, v in value.items():
                        err = InvalidArgument(message=f"config {config_name} idx {idx} key {k} value {v} is error")
                        try:
                            field = WashSaleRiskConfig.wash_sale[k]
                            value[k] = field._type(v)
                            if not field.validate(value[k]):
                                raise err
                        except Exception:
                            raise err
                old_value = getattr(risk_config, config_name)
                setattr(risk_config, config_name, value_list)

                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.WashSale,
                    old_data=dict(value=old_value),
                    new_data=dict(value=value_list),
                )


@ns.route('/price-verify-settings')
@respond_with_code
class PriceVerifySettingsResource(Resource):

    @classmethod
    def get(cls):
        """风控-价格保护机制参数设置列表"""
        b_mapping = {
            OrderBusinessType.NORMAL_BUSINESS_TYPE.value: "现货市场",
            OrderBusinessType.MARGIN_BUSINESS_TYPE.value: "杠杆市场",
            OrderBusinessType.PERPETUAL_BUSINESS_TYPE.value: "合约市场",
        }
        o_mapping = {
            OrderType.MARKET_ORDER_TYPE.value: "市价",
            OrderType.STOP_MARKET_ORDER_TYPE.value: "计划市价",
            OrderType.LIMIT_ORDER_TYPE.value: "限价",
            OrderType.STOP_LIMIT_ORDER_TYPE.value: "计划限价",
        }

        def get_description(k: Tuple):
            return f"{o_mapping[k[0]]}-{b_mapping[k[1]]}"

        result = PriceVerifySettings.fields_and_values_json
        keys = sorted([tuple(v["json_schema_extra"]["group"]) for v in result])
        names = [get_description(key) for key in keys]
        names = list(dict.fromkeys(names))
        group_data = group_by(lambda x: get_description(tuple(x["json_schema_extra"]["group"])), result)
        return dict(names=names, group_data=group_data)


# noinspection PyUnresolvedReferences
@ns.route('/price-verify-settings/<field>')
@respond_with_code
class PriceVerifySettingsManagementResource(Resource):

    @classmethod
    def put(cls, field: str):
        """风控-价格保护机制参数设置编辑"""
        old_value = getattr(PriceVerifySettings, field)
        raw_data = request.get_data(as_text=True)
        try:
            PriceVerifySettings.set_setting_value(field, raw_data)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(PriceVerifySettings, field)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PriceVerifySettings,
            old_data={field: old_value},
            new_data={field: new_value},
        )
        return dict(
            value=new_value
        )

    @classmethod
    def delete(cls, field):
        """风控-价格保护机制参数设置置删除"""
        try:
            delattr(PriceVerifySettings, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PriceVerifySettings,
            detail=dict(field=field),
        )
        return dict(
            value=getattr(PriceVerifySettings, field)
        )


@ns.route('/abnormal-issuance-conf')
@respond_with_code
class AssetAbnormalIssuanceConfResource(Resource):

    @classmethod
    def get(cls):
        """风控-币种异常增发配置"""
        model_ = AssetAbnormalIssuanceConfig
        query = model_.query.order_by(model_.rank_min.asc())
        rows = query.all()
        ret = []
        ranks = []
        for idx, row in enumerate(rows, start=1):
            item = row.to_dict()
            item['grade'] = idx
            item['rank_range'] = f'{row.rank_min}~{row.rank_max}'
            ret.append(item)
            ranks.append({
                'id': row.id,
                'grade': idx,
                'rank_min': row.rank_min,
                'rank_max': row.rank_max,
            })
        risk_config = RiskControlGroupConfig()
        cfg = RiskControlGroupConfig.abnormal_issuance
        abnormal_issuance = {
            "name": cfg._name,
            "desc": cfg.desc,
            "fields": [
                {
                    "name": field_name,
                    "value": getattr(risk_config, cfg._name)[field_name],
                    "desc": field.desc,
                    "detail": field.meta.get("detail", ""),
                    "type": field.type.__name__,
                }
                for field_name, field in cfg.field_group.items()
            ]
        }
        return dict(
            items=ret,
            extra=dict(
                ranks=ranks,
                abnormal_issuance=abnormal_issuance
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        ranks=fields.List(fields.Dict, required=True),
        removes=fields.List(fields.Integer, required=False),
    ))
    def post(cls, **kwargs):
        """风控-币种异常增发档位配置"""
        model_ = AssetAbnormalIssuanceConfig
        ranks = kwargs['ranks']
        if len(ranks) < 3:
            raise InvalidArgument(message='至少要配置三档数据')
        last_rank = None
        for rank in ranks:
            id_ = rank.get('id')
            rank_min = rank.get('rank_min')
            rank_max = rank.get('rank_max')
            if not rank_min or not rank_max:
                raise InvalidArgument(message='流通市值排名不能为空')
            rank_min, rank_max = int(rank_min), int(rank_max)
            if rank_min >= rank_max:
                raise InvalidArgument(message='流通市值排名范围需大于 1')
            if last_rank and last_rank >= rank_min:
                raise InvalidArgument(message='档位流通市值排名不能交叉')
            if last_rank and last_rank + 1 != rank_min:
                raise InvalidArgument(message='档位配置必须连续')
            if id_:
                row = model_.query.get(id_)
                if not row:
                    raise InvalidArgument
            last_rank = rank_max

        for rank in ranks:
            id_ = rank.get('id')
            if not id_:
                row = model_(
                    rank_min=rank['rank_min'],
                    rank_max=rank['rank_max'],
                )
                db.session.add(row)
                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AbnormalIssuanceConfig,
                    detail=dict(rank_min=rank['rank_min'], rank_max=rank['rank_max']),
                )
            else:
                row = model_.query.get(id_)
                old_data = row.to_dict(enum_to_name=True)
                row.rank_min = rank['rank_min']
                row.rank_max = rank['rank_max']
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AbnormalIssuanceConfig,
                    old_data=old_data,
                    new_data=row.to_dict(enum_to_name=True),
                )
        if removes := kwargs.get('removes'):
            model_.query.filter(
                model_.id.in_(removes)
            ).delete()
            for remove_id in removes:
                AdminOperationLog.new_delete(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectRisk.AbnormalIssuanceConfig,
                    detail=dict(id=remove_id),
                )
        db.session.commit()


@ns.route('/abnormal-issuance-conf/<int:id_>')
@respond_with_code
class AssetAbnormalIssuanceDetailConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        issuance_threshold=PositiveDecimalField(required=True),
        issuance_usd_threshold=PositiveDecimalField(required=True),
        remark=fields.String(missing='', allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """风控-币种异常增发配置-详情配置"""
        params = Struct(**kwargs)
        model_ = AssetAbnormalIssuanceConfig
        row = model_.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.issuance_threshold = params.issuance_threshold
        row.issuance_usd_threshold = params.issuance_usd_threshold
        row.remark = params.remark or ''
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.AbnormalIssuanceConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/mobile-notice-config')
@respond_with_code
class MobileNoticeConfigResource(Resource):

    @classmethod
    def is_valid_mobile_phone(cls, phone_number):
        mobile_pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(mobile_pattern, phone_number))

    @classmethod
    def get(cls):
        """风控-风控电话告警列表-列表详情"""
        query = RiskControlMobileNoticeConfig.query.filter(
            RiskControlMobileNoticeConfig.status == RiskControlMobileNoticeConfig.StatusType.VALID
        ).all()
        data = defaultdict(list)
        all_user_ids = set()
        for v in query:
            all_user_ids.add(v.user_id)
            data[v.event_type].append(dict(
                user_id=v.user_id,
                event_type=v.event_type,
                mobile=v.mobile,
                remark=v.remark,
                )
            )

        admin_user_ids = {v.user_id for v in AdminUser.query.filter(
                AdminUser.status == AdminUser.Status.PASSED
        ).with_entities(AdminUser.user_id).all()}
        user_name_mapping = get_admin_user_name_map(admin_user_ids)
        user_names = [dict(user_id=k, name=v) for k, v in get_admin_user_name_map(admin_user_ids).items()]
        result = []
        can_add_event_types = []
        for _event_type in RiskControlMobileNoticeConfig.MobileNoticeEventType:
            if _event_type in data:
                result.append(
                    {
                        "event_type": _event_type.name,
                        "description": RiskControlMobileNoticeConfig.get_description(_event_type),
                        "user_details": [
                            v | {"name": user_name_mapping.get(v["user_id"], "")}
                            for v in data[_event_type]]
                    }
                )
            else:
                can_add_event_types.append(_event_type)
        return {
            "extra": {
                "event_types": {i.name: i.value for i in RiskControlMobileNoticeConfig.MobileNoticeEventType},
                "add_event_types": {i.name: i.value for i in can_add_event_types},
                "admin_user_name_mapping": user_names
            },
            "data": result
        }

    @classmethod
    @ns.use_kwargs(dict(
        event_type=EnumField(RiskControlMobileNoticeConfig.MobileNoticeEventType, required=True),
        mobile_details=fields.List(fields.Dict(), required=True),
    ))
    def post(cls, **kwargs):
        """风控-风控电话告警-创建/编辑"""
        _model = RiskControlMobileNoticeConfig
        mobile_details = kwargs['mobile_details']
        keys = ["user_id", "mobile"]
        for _detail in mobile_details:
            for _key in keys:
                if not _detail.get(_key):
                    raise InvalidArgument(message="请检查用户ID或者手机号是否填写")
                if _key == "mobile" and not cls.is_valid_mobile_phone(_detail["mobile"]):
                    raise InvalidArgument(message="请填写有效的手机号")

        event_type = kwargs["event_type"]
        query = _model.query.filter(
            _model.event_type == event_type,
        ).all()
        exist_data = [(_v.mobile, _v.user_id) for _v in query]
        update_data = [(_detail["mobile"], int(_detail["user_id"])) for _detail in mobile_details]

        _model.query.filter(_model.event_type == event_type).update(
            {'status': _model.StatusType.DELETED},
            synchronize_session=False
        )

        for _detail in (set(update_data) & set(exist_data)):
            _model.query.filter(_model.event_type == event_type,
                                _model.mobile == _detail[0],
                                _model.user_id == _detail[1]
                                ).update(
                {'status': _model.StatusType.VALID},
                synchronize_session=False
            )
        for _detail in (set(update_data) - set(exist_data)):
            db.session.add(
                _model(
                    user_id=_detail[1],
                    event_type=event_type,
                    status=_model.StatusType.VALID,
                    mobile=_detail[0],
                    remark="",
                )
            )
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.MobileNoticeConfig,
            old_data={"event_type": event_type, "old_data": exist_data},
            new_data={"event_type": event_type, "new_data": update_data},
        )

    @classmethod
    @ns.use_kwargs(dict(
        event_type=EnumField(RiskControlMobileNoticeConfig.MobileNoticeEventType, required=True),
    ))
    def delete(cls, **kwargs):
        """风控-风控电话告警-删除"""
        event_type = kwargs["event_type"]
        _model = RiskControlMobileNoticeConfig
        _model.query.filter(_model.event_type == event_type).update(
            {'status': _model.StatusType.DELETED},
            synchronize_session=False
        )
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.MobileNoticeConfig,
            detail={"event_type": event_type},
        )


@ns.route('/deposit-withdrawal-switch')
@respond_with_code
class DepositWithdrawalSwitchResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(),
    ))
    def get(cls, **kwargs):
        """风控-风控关闭充提币种列表"""
        # None表示全部
        asset = kwargs["asset"] if kwargs.get("asset") else None
        if asset is not None:
            asset_chain_data = {asset: asset_to_chains(asset)}
        else:
            asset_chain_data = asset_to_chains()
        keys = [
            AssetChainConfigControl.deposits_disabled_by_accumulate_rc_incr,
            AssetChainConfigControl.deposits_disabled_by_accumulate_rc_proportion,
            AssetChainConfigControl.withdrawals_disabled_by_asset_liability,
            AssetChainConfigControl.withdrawals_disabled_by_accumulate_rc,
            AssetChainConfigControl.deposits_disabled_by_rc_abnormal,
            AssetChainConfigControl.withdrawals_disabled_by_rc_abnormal,
            AssetChainConfigControl.withdrawals_disabled_remark,
        ]
        key_typ_mapping = {key.name: key for key in keys}

        q = AssetChainConfig.query.filter(
            AssetChainConfig.key.in_([key.name for key in keys]),
            AssetChainConfig.status == AssetChainConfig.Status.VALID
        )
        if asset is not None:
            q = q.filter(AssetChainConfig.asset == asset)
        result = defaultdict(dict)
        for v in q:
            result[f"{v.asset}-{v.chain}"][v.key] = {
                "updated_at": v.updated_at,
                "value": value_convert(key_typ_mapping[v.key].type, v.value),
            }

        build_asset_chains = [
            f"{_asset}-{_chain}"
            for _asset, _chains in asset_chain_data.items()
            for _chain in _chains
        ]
        data = []
        default_value = False

        remark_name = AssetChainConfigControl.withdrawals_disabled_remark.name

        for _asset_chain in build_asset_chains:
            _switch_data = {}
            for key in key_typ_mapping:
                default = dict(
                    value=key_typ_mapping[key].default,
                    updated_at="",
                )
                _switch_data[key] = result[_asset_chain].get(key, default)
            if all([v["value"] == default_value for k, v in _switch_data.items() if k != remark_name]):
                continue
            _asset, _chain = _asset_chain.split('-')
            data.append(dict(
                **_switch_data,
                name=_asset_chain,
                asset=_asset,
                chain=_chain,
            ))
        return dict(
            items=data,
            extra=dict(
                keys=[(key.name, key.desc) for key in keys if key.name != remark_name],
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        chain=fields.String(required=True),
        remark=fields.String(required=True)
    ))
    def patch(cls, **kwargs):
        """风控-风控关闭充提币种列表-编辑备注"""
        old_remark = AssetChainConfigControl(kwargs["asset"], kwargs["chain"]).withdrawals_disabled_remark

        AssetChainConfigControl(kwargs["asset"], kwargs["chain"]).withdrawals_disabled_remark = kwargs["remark"]

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DepositWithdrawalSwitch,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=kwargs['remark']),
            special_data=dict(asset=kwargs['asset'], chain=kwargs['chain']),
        )


@ns.route('/pledge-loan-flat-check')
@respond_with_code
class PledgeLoanFlatCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=ex_fields.EnumField(enum=PledgeLoanFlatCheck.Status, enum_by_value=True),
        start_time=fields.Integer,
        end_time=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """风控-借贷对账"""
        asset = kwargs.get('asset')
        status = kwargs.get('status')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']

        query = PledgeLoanFlatCheck.query
        if asset:
            query = query.filter(PledgeLoanFlatCheck.asset == asset)
        if status:
            query = query.filter(PledgeLoanFlatCheck.status == status)
        else:
            query = query.filter(PledgeLoanFlatCheck.status != PledgeLoanFlatCheck.Status.DELETED)
        if start_time:
            query = query.filter(PledgeLoanFlatCheck.created_at >= timestamp_to_datetime(start_time))
        if end_time:
            query = query.filter(PledgeLoanFlatCheck.created_at < timestamp_to_datetime(end_time))

        records = query.order_by(PledgeLoanFlatCheck.id.desc()).paginate(page, limit)

        items = []
        for record in records.items:
            item = dict(
                id=record.id,
                start_time=record.start_time,
                end_time=record.end_time,
                detail=record.detail,
                remark=record.remark,
                status=record.status,
                asset=record.asset,
                last_unflat_amount=record.last_unflat_amount,
                new_loan_amount=record.new_loan_amount,
                new_interest_amount=record.new_interest_amount,
                new_flat_amount=record.new_flat_amount,
                unflat_amount=record.unflat_amount,
                diff_amount=record.diff_amount
            )
            items.append(item)

        recs = LoanAsset.query.filter(
            LoanAsset.status != LoanAsset.Status.CLOSE,
        ).with_entities(
            LoanAsset.asset
        ).all()

        result = dict(
            total=records.total,
            items=items,
            assets={i.asset for i in recs},
            statuses={PledgeLoanFlatCheck.Status.FAILED.value: '不平', PledgeLoanFlatCheck.Status.PASSED.value: '平'}
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """风控-杠杆对账-对账"""
        asset = kwargs['asset']
        check_pledge_loan_flat_task.delay(asset, FailOption.DUMMY)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """风控-杠杆对账-审核"""
        id_ = kwargs['id']
        remark = kwargs['remark']

        record = PledgeLoanFlatCheck.query.get(id_)
        if not record:
            raise NotFound
        old_data = record.to_dict(enum_to_name=True)
        record.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PledgeLoanFlatCheck,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """风控-杠杆对账-删除"""
        id_ = kwargs['id']

        record = PledgeLoanFlatCheck.query.get(id_)
        if not record:
            raise NotFound
        record.status = PledgeLoanFlatCheck.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.PledgeLoanFlatCheck,
            detail=record.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/user-prevent-risk-control-config')
@respond_with_code
class UserPreventRiskControlConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        reason=ex_fields.EnumField(enum=RiskUser.Reason, enum_by_value=True),
        user_id=fields.Integer,
        start_created_ts=TimestampField(is_ms=True),
        source=fields.String(),
        end_created_ts=TimestampField(is_ms=True),
        start_expired_ts=TimestampField(is_ms=True),
        end_expired_ts=TimestampField(is_ms=True),
        page=ex_fields.PageField,
        limit=LimitField(missing=100, max_limit=10000)
    ))
    def get(cls, **kwargs):
        """风控-防干扰列表"""
        reason = kwargs.get('reason')
        user_id = kwargs.get("user_id")
        start_created = kwargs.get('start_created_ts')
        end_created = kwargs.get('end_created_ts')
        source = kwargs.get('source')
        start_expired = kwargs.get('start_expired_ts')
        end_expired = kwargs.get('end_expired_ts')
        page = kwargs['page']
        limit = kwargs['limit']

        reason_map = RiskInfosResource.enums["reasons"]
        _model = UserPreventRiskControlConfig
        query = _model.query.select_from(_model).join(
            RiskUser, _model.latest_risk_user_record_id == RiskUser.id
        ).order_by(_model.id.desc())
        if user_id:
            query = query.filter(_model.user_id == user_id)
        if reason:
            query = query.filter(_model.reason == reason)
        if start_created:
            query = query.filter(RiskUser.created_at >= start_created)
        if end_created:
            query = query.filter(RiskUser.created_at <= end_created)
        if start_expired:
            query = query.filter(_model.expired_at >= start_expired)
        if end_expired:
            query = query.filter(_model.expired_at <= end_expired)
        if source:
            query = query.filter(_model.source == source)

        query = query.with_entities(
            _model.id,
            _model.user_id,
            _model.latest_risk_user_record_id,
            RiskUser.created_at,
            _model.reason,
            RiskUser.audited_at,
            RiskUser.audited_by,
            _model.source,
            _model.expired_at,
            _model.remark
        )
        pagination = query.paginate(page, limit, error_out=False)

        rows = pagination.items

        user_ids = set()
        for item in rows:
            user_ids.add(item.user_id)
            user_ids.add(item.audited_by)

        user_email_q = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email).all()

        id_email_mapping = {
            v.id: v.email
            for v in user_email_q
        }

        items = []
        for item in rows:
            items.append(
                dict(
                    id=item.id,
                    risk_id=item.latest_risk_user_record_id,
                    user_id=item.user_id,
                    email=id_email_mapping.get(item.user_id),
                    reason=item.reason.value,
                    reason_display=reason_map.get(item.reason.value, ''),
                    triggered_at=item.created_at,
                    audited_at=item.audited_at,
                    audit_user_id=item.audited_by,
                    audit_user_email=id_email_mapping.get(item.audited_by) if item.audited_by else '',
                    source=item.source,
                    expired_at=item.expired_at,
                    remark=item.remark
                )
            )

        build_headers = []
        source_list = []

        source_list.extend(list(list_all_assets()))
        source_list.extend(list(MarketCache.list_online_markets()))
        source_list.extend(list(PerpetualMarketCache().get_market_list()))
        build_filters = [
            dict(prop="reason", label="类型",
                 type="select",
                 options=[{"label": v, "value": k} for k, v in reason_map.items()],
                 ),
            dict(
                prop="source", label="来源",
                type="select",
                options=[{"label": k, "value": k} for k in source_list],
            )
        ]

        return dict(
            total=pagination.total,
            items=items,
            headers=build_headers,
            filters=build_filters,
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        expired_at=TimestampField(is_ms=True),
        remark=fields.String(required=True)
    ))
    def patch(cls, **kwargs):
        _id = kwargs["id"]
        expired_at = kwargs["expired_at"]
        remark = kwargs["remark"]

        item = UserPreventRiskControlConfig.query.get(_id)
        if not item:
            raise InvalidArgument(message=f"{_id} not found")
        old_data = {
            "expired_at": expired_at,
            "remark": remark
        }
        item.expired_at = expired_at
        item.remark = remark
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.RiskUserPreventConfig,
            old_data=old_data,
            new_data=item.to_dict(enum_to_name=True),
            target_user_id=item.user_id,
        )


@ns.route("/device-id-blacklist")
@respond_with_code
class DeviceIDBlacklistResource(Resource):

    model = DeviceIDBlacklist

    @classmethod
    @ns.use_kwargs(
        dict(
            device_id=fields.String,
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 风控-风控黑名单-设备ID黑名单-列表 """
        query = cls.model.query.filter(cls.model.status == cls.model.Status.VALID)
        if device_id := kwargs.get("device_id"):
            query = query.filter(cls.model.device_id == device_id)
        if start_time := kwargs.get("start_time"):
            query = query.filter(cls.model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(cls.model.created_at <= end_time)
        pagination = query.order_by(cls.model.id.desc()).paginate(kwargs["page"], kwargs["limit"], error_out=False)
        user_ids = {r.created_by for r in pagination.items}
        user_name_map = get_admin_user_name_map(user_ids)
        res_items = []
        for row in pagination.items:
            item = row.to_dict(enum_to_name=True)
            item['created_by_name'] = user_name_map.get(row.created_by, row.created_by)
            res_items.append(item)

        return dict(
            items=res_items,
            total=pagination.total,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            device_id=fields.String(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 风控-风控黑名单-设备ID黑名单-新增 """
        device_id = kwargs['device_id']
        row = cls.model.query.filter(
            cls.model.device_id == device_id,
            cls.model.status == cls.model.Status.VALID
        ).first()
        if row:
            raise InvalidArgument(message='设备ID已加入黑名单')
        row = cls.model(
            device_id=kwargs['device_id'],
            created_by=g.user.id,
            remark=kwargs.get('remark') or '',
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DeviceIDBlacklist,
            detail=row.to_dict(enum_to_name=True),
        )
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            remark=fields.String(required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 风控-风控黑名单-设备ID黑名单-编辑备注 """
        row: cls.model = cls.model.query.get(kwargs['id'])
        old_data = row.to_dict(enum_to_name=True)
        row.remark = kwargs["remark"]
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DeviceIDBlacklist,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 风控-风控黑名单-设备ID黑名单-删除 """
        row: cls.model = cls.model.query.get(kwargs['id'])
        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.DeviceIDBlacklist,
            detail=row.to_dict(enum_to_name=True),
        )
        return {}


@ns.route("/ip-blacklist")
@respond_with_code
class IPBlacklistResource(Resource):

    model = IPBlacklist

    @classmethod
    @ns.use_kwargs(
        dict(
            ip=fields.String,
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 风控-风控黑名单-IP地址黑名单-列表 """
        query = cls.model.query.filter(cls.model.status == cls.model.Status.VALID)
        if ip := kwargs.get("ip"):
            query = query.filter(cls.model.ip == ip)
        if start_time := kwargs.get("start_time"):
            query = query.filter(cls.model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(cls.model.created_at <= end_time)
        pagination = query.order_by(cls.model.id.desc()).paginate(kwargs["page"], kwargs["limit"], error_out=False)
        user_ids = {r.created_by for r in pagination.items}
        user_name_map = get_admin_user_name_map(user_ids)
        res_items = []
        for row in pagination.items:
            item = row.to_dict(enum_to_name=True)
            item['created_by_name'] = user_name_map.get(row.created_by, row.created_by)
            res_items.append(item)

        return dict(
            items=res_items,
            total=pagination.total,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            ip=fields.String(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 风控-风控黑名单-IP地址黑名单-新增 """
        ip = kwargs['ip']
        if not validate_ip_address(ip):
            raise InvalidArgument
        row = cls.model.query.filter(
            cls.model.ip == ip,
            cls.model.status == cls.model.Status.VALID
        ).first()
        if row:
            raise InvalidArgument(message='IP已加入黑名单')
        location = GeoIP(ip).location
        row = cls.model(
            ip=ip,
            location=location,
            created_by=g.user.id,
            remark=kwargs.get('remark') or '',
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IPBlacklist,
            detail=row.to_dict(enum_to_name=True),
        )
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            remark=fields.String(required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 风控-风控黑名单-IP地址黑名单-编辑备注 """
        row: cls.model = cls.model.query.get(kwargs['id'])
        old_data = row.to_dict(enum_to_name=True)
        row.remark = kwargs['remark']
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IPBlacklist,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 风控-风控黑名单-IP地址黑名单-删除 """
        row: cls.model = cls.model.query.get(kwargs['id'])
        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IPBlacklist,
            detail=row.to_dict(enum_to_name=True),
        )
        return {}
