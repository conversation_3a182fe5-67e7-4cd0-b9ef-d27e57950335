# -*- coding: utf-8 -*-
from datetime import timedelta
from decimal import Decimal
from enum import Enum

from flask_restx import Resource
from webargs import fields

from app.api.common import Namespace
from app.api.common.decorators import respond_with_code
from app.api.common.fields import EnumField
from app.models.investment import InvStatisticTimeRange
from app.assets import list_all_assets
from app.utils.amount import amount_to_str
from app.utils.date_ import now, timestamp_to_datetime
from app.caches.investment import InvestmentStatisticCache, FixedInvStatisticCache

ns = Namespace("Investment-Statistic")


@ns.route("")
@respond_with_code
class InvestmentStatisticResource(Resource):
    class InvestmentType(Enum):
        ALL = "ALL"
        CURRENT = "活期"
        FIXED = "定期"

    @classmethod
    @ns.use_kwargs(
        dict(
            investment_type=EnumField(InvestmentType, missing=InvestmentType.ALL),
            asset=fields.String(missing=""),
            time_range=EnumField(InvStatisticTimeRange, missing=InvStatisticTimeRange.DAYS_7),
        )
    )
    def get(cls, **kwargs):
        """统计-理财统计-报表查看"""
        investment_type = kwargs.get("investment_type")
        query_asset = kwargs.get("asset")
        time_range = kwargs["time_range"]

        # 理财类型筛选
        resp = dict(
            assets=list_all_assets(),
            records=[],
            update_time=now() - timedelta(minutes=30),
            investment_types=cls.InvestmentType,
            time_ranges=InvStatisticTimeRange,
        )

        # 根据理财类型获取数据
        if investment_type != cls.InvestmentType.ALL:
            records = cls._get_bus_investment_data(investment_type, query_asset, time_range)
        else:
            records = cls._get_all_investment_data(query_asset, time_range)
        if records:
            resp["update_time"] = timestamp_to_datetime(float(records[0]["update_time"]))

        # 去除 bitmap 占用
        for r in records:
            r.pop("user_bitmap", None)

        # 默认按理财持仓市值倒序排序
        records.sort(key=lambda x: Decimal(str(x["investment_usd"])), reverse=True)

        if not query_asset:
            total_record = cls._calculate_total_record(records, time_range)
            records.insert(0, total_record)

        resp["records"] = records
        return resp

    @classmethod
    def _get_bus_investment_data(cls, inv_type: InvestmentType, query_asset: str, time_range: InvStatisticTimeRange) -> list:
        """获取活期理财数据"""
        if inv_type == cls.InvestmentType.CURRENT:
            cache = InvestmentStatisticCache(time_range)
        else:
            cache = FixedInvStatisticCache(time_range)

        if query_asset:
            asset_data = cache.get_asset_data(query_asset)
            return [asset_data] if asset_data else []
        else:
            data = cache.get_data()
            return list(data.values()) if data else []

    @classmethod
    def _get_all_investment_data(cls, query_asset: str, time_range: InvStatisticTimeRange) -> list:
        """获取所有理财数据（活期+定期）"""
        current_data = cls._get_bus_investment_data(cls.InvestmentType.CURRENT, query_asset, time_range)
        current_data_map = {data["asset"]: data for data in current_data}
        fixed_data = cls._get_bus_investment_data(cls.InvestmentType.FIXED, query_asset, time_range)
        fixed_data_map = {data["asset"]: data for data in fixed_data}

        for asset, data in fixed_data_map.items():
            if asset in current_data_map:
                # 合并同币种的数据
                for k, v in data.items():
                    if k in [
                        "investment_amount",
                        "investment_usd",
                        "ladder_usd",
                        "total_interest_usd",
                        "base_interest_usd",
                        "subsidy_interest_usd",
                    ]:
                        current_data_map[asset][k] = Decimal(current_data_map[asset][k]) + Decimal(v)
                    if k == "user_bitmap":
                        current_data_map[asset]["user_count"] = len(current_data_map[asset]["user_bitmap"] | v)
            else:
                # 只有定期的币种
                current_data_map[asset] = data

        # 重新计算年化利率
        for asset, data in current_data_map.items():
            data["annual_rate"] = InvestmentStatisticCache.calculate_annual_rate(
                Decimal(data["total_interest_usd"]),
                Decimal(data["investment_usd"]),
                time_range.value,
            )

        return list(current_data_map.values())

    @classmethod
    def _calculate_total_record(cls, records: list, time_range: InvStatisticTimeRange) -> dict:
        """计算总计行"""
        total_interest_usd = sum(Decimal(str(r["total_interest_usd"])) for r in records)
        investment_usd = sum(Decimal(str(r["investment_usd"])) for r in records)
        total_record = {
            "asset": "ALL",
            "user_count": sum(r["user_count"] for r in records),
            "investment_usd": amount_to_str(investment_usd, 2),
            "total_interest_usd": amount_to_str(total_interest_usd, 2),
            "ladder_usd": sum(Decimal(str(r["ladder_usd"])) for r in records),
            "base_interest_usd": sum(Decimal(str(r["base_interest_usd"])) for r in records),
            "subsidy_interest_usd": sum(Decimal(str(r["subsidy_interest_usd"])) for r in records),
            "annual_rate": InvestmentStatisticCache.calculate_annual_rate(
                total_interest_usd,
                investment_usd,
                time_range.value,
            ),
        }

        return total_record
