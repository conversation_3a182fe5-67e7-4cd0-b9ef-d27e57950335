# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import datetime
from decimal import Decimal, ROUND_DOWN
from enum import Enum
from typing import Callable
from flask import g, current_app, request
from marshmallow import Schema, EXCLUDE
from webargs import fields as wa_fields

from app.business.auth import get_admin_user_name_map, is_super_user
from app.business.email import send_bus_ambassador_train_book_email
from app.business import ServerClient, BalanceBusiness, SPOT_ACCOUNT_ID
from app.api.common import Resource, Namespace, respond_with_code, lock_request
from app.business.referral import TreeAmbHelper
from app.api.common.fields import (
    EnumField,
    PageField,
    LimitField,
    PositiveDecimalField,
    TimestampField,
)
from app.common import Language, get_country, list_country_codes_3_admin, PrecisionEnum, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument
from app.models import (
    db, User, AdminUser, Ambassador, AmbassadorApplication, AmbassadorAgent,
    BusinessTeam, BusinessUser, BusinessAmbassadorAudit, BusinessAmbassador, BusinessAmbassadorShareUser,
    BusinessTeamStatistics, BusinessUserStatistics, BusinessAmbassadorStatistics,
    BusinessAmbassadorLoanApply, BusinessAmbassadorLoan, BusinessAmbassadorRepayHistory, BusinessAmbassadorPermission,
    MonthlyBusinessAmbassadorReferralReport, DailyBusinessAmbassadorReferralReport,
    TreeAmbassador, TreeAmbassadorHierarchy, TreeAmbassadorStatistics
)
from app.models.broker import Broker
from app.models.mongo.referral import BusRelationUserSnapshotMySQL
from app.models.referral import ReferralHistory, BusRelationChangelog, Referral
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.schedules.ambassador import update_business_ambassadors_last_team_id, update_business_user_ambassadors_last_team_id
from app.utils import export_xlsx, amount_to_str, quantize_amount
from app.business.bus_referral import BusRelationUserQuerier
from app.utils.date_ import now, next_month
from app.utils.importer import get_table_rows
from app.utils.files import AWSBucketPublic
from app.utils.parser import JsonEncoder


ns = Namespace('Business-Ambassador')
url_prefix = '/bus-amb'


class BusSnapshotHelper:

    @classmethod
    def add_snapshot(cls, row, op_type: str, op_time: datetime,
                     op_user_id: int = None, operate_remark: str = None, **extra_data):
        if op_user_id is None:
            if hasattr(g, "user"):
                op_user_id = getattr(g, "user").id
            else:
                op_user_id = 0
        if op_type not in [i.name for i in BusRelationUserSnapshotMySQL.OpType]:
            raise InvalidArgument(message=f"not support op_type {op_type}")

        if isinstance(row, BusinessTeam):
            role_ = BusRelationUserSnapshotMySQL.Role.BUS_TEAM.name
            trace_id_ = row.id
        elif isinstance(row, BusinessUser):
            role_ = BusRelationUserSnapshotMySQL.Role.BUS_USER.name
            trace_id_ = row.user_id
        elif isinstance(row, BusinessAmbassador):
            role_ = BusRelationUserSnapshotMySQL.Role.BUS_AMB.name
            trace_id_ = row.user_id
        else:
            raise InvalidArgument(message=f"not support role {type(row).__name__}")

        _sp = BusRelationUserSnapshotMySQL(
            trace_id_=trace_id_,
            role_=role_,
            op_type_=op_type,
            operator_=op_user_id or 0,
            operate_at_=op_time,
            operate_remark_=operate_remark or "",
        )
        db.session.add(_sp)
        db.session.commit()

    @classmethod
    def get_amb_remarks(cls, amb_id: int, page: int = 1, limit: int = 100) -> tuple[list[dict], int]:
        model = BusRelationUserSnapshotMySQL
        query = model.query.filter(
            model.trace_id_ == amb_id,
            model.role_ == model.Role.BUS_AMB.name,
            model.op_type_ == model.OpType.EDIT_REMARK.name
        )
        query = query.order_by(model.id.desc())
        total = query.count()
        amb_snapshots = query.offset((page - 1) * limit).limit(limit).all()
        remark_items = []
        for s in amb_snapshots:
            remark_items.append(
                {
                    "id": s.id,
                    "operator": s.operator_,
                    "operate_at": s.operate_at_,
                    "remark": s.operate_remark_,
                }
            )
        return remark_items, total


class PrUserRateSchema(Schema):
    """ 商务用户的PR分层比例-scheme """
    class Meta:
        unknown = EXCLUDE
    user_id = wa_fields.Integer(required=True)
    rate = PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN, required=True, allow_zero=True)


@ns.route("/teams")
@respond_with_code
class BusTeamsResource(Resource):

    class SortField(Enum):
        bus_user_count = '商务数量'
        effect_refer_trade_amount = 'refer 总交易额'
        this_month_deal_amount = '当月 refer 交易额'
        last_month_deal_amount = '上月 refer 交易额'
        total_refer_amount = '累计返佣'
        pending_refer_amount = '当月待返佣'

    @classmethod
    def get_teams_by_leader(cls, user_id: int) -> list[BusinessTeam]:
        teams = BusinessTeam.query.order_by(BusinessTeam.id.desc()).limit(1000).all()
        res_teams = []
        for team in teams:
            if team.leader_id == user_id:
                res_teams.append(team)
            elif user_id in team.pr_user_ids:
                res_teams.append(team)
        return res_teams

    @classmethod
    @ns.use_kwargs(dict(
        sort=EnumField(SortField)
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-团队列表"""
        model = BusinessTeam
        teams: list[model] = model.query.filter(
            model.status == model.Status.VALID,
        ).order_by(model.id.desc()).all()
        team_ids = [i.id for i in teams]

        biz_user_rows = BusinessUser.query.filter(
            BusinessUser.status == BusinessUser.Status.VALID,
            BusinessUser.team_id.in_(team_ids),
        ).with_entities(
            BusinessUser.team_id,
            BusinessUser.user_id,
        ).all()
        team_biz_users_map = defaultdict(set)
        biz_uids = set()
        for biz_row in biz_user_rows:
            biz_uids.add(biz_row.user_id)
            team_biz_users_map[biz_row.team_id].add(biz_row.user_id)

        team_statics_rows = BusinessTeamStatistics.query.filter(
            BusinessTeamStatistics.team_id.in_(team_ids),
        ).with_entities(
            BusinessTeamStatistics.team_id,
            BusinessTeamStatistics.total_refer_amount,
            BusinessTeamStatistics.pending_refer_amount,
            BusinessTeamStatistics.last_update_time,
        ).all()
        team_statics_map: dict[int, BusinessTeamStatistics] = {i.team_id: i for i in team_statics_rows}

        bus_user_st_rows = BusinessUserStatistics.query.filter(
            BusinessUserStatistics.user_id.in_(biz_uids),
        ).with_entities(
            BusinessUserStatistics.user_id,
            BusinessUserStatistics.effect_trade_amount,
            BusinessUserStatistics.month_effect_trade_amount,
            BusinessUserStatistics.last_month_effect_trade_amount,
            BusinessUserStatistics.refer_user_balance_usd,
            BusinessUserStatistics.refer_count,
            BusinessUserStatistics.refer_trade_count,
            BusinessUserStatistics.refer_active_user_count,
        ).all()
        bus_user_st_map: dict[int, BusinessUserStatistics] = {i.user_id: i for i in bus_user_st_rows}

        leader_ids = {i.leader_id for i in teams}
        has_pr_teams = [i for i in teams if i.pr_user_ids]
        pr_ids = {i for t in has_pr_teams for i in t.pr_user_ids}
        all_user_ids = leader_ids | pr_ids
        leader_name_map = get_admin_user_name_map(all_user_ids)
        statics_data = {
            'bus_user_count': 0,
            'effect_refer_trade_amount': 0,
            'this_month_deal_amount': 0,
            'last_month_deal_amount': 0,
            'total_refer_amount': 0,
            'pending_refer_amount': 0,
            'refer_count': 0,
            'refer_trade_count': 0,
            'refer_active_user_count': 0,
            'refer_user_balance_usd': 0,
        }
        table_items = []
        for item in teams:
            item: model
            team_biz_uids = team_biz_users_map[item.id]
            effect_refer_trade_amount = 0
            this_month_deal_amount = last_month_deal_amount = 0
            refer_user_balance_usd = 0
            refer_count = refer_trade_count = refer_active_user_count = 0
            for biz_uid in team_biz_uids:
                biz_st = bus_user_st_map.get(biz_uid)
                if biz_st:
                    this_month_deal_amount += biz_st.month_effect_trade_amount
                    last_month_deal_amount += biz_st.last_month_effect_trade_amount
                    effect_refer_trade_amount += biz_st.effect_trade_amount
                    refer_user_balance_usd += biz_st.refer_user_balance_usd
                    refer_count += biz_st.refer_count
                    refer_trade_count += biz_st.refer_trade_count
                    refer_active_user_count += biz_st.refer_active_user_count

            d = item.to_dict(enum_to_name=True)
            d["effect_refer_trade_amount"] = effect_refer_trade_amount
            d["this_month_deal_amount"] = this_month_deal_amount
            d["last_month_deal_amount"] = last_month_deal_amount
            d["refer_user_balance_usd"] = refer_user_balance_usd
            d["refer_count"] = refer_count
            d["refer_trade_count"] = refer_trade_count
            d["refer_active_user_count"] = refer_active_user_count
            d["leader_name"] = leader_name_map.get(item.leader_id, "")
            pr_info_list = []
            for pr_id in d['pr_user_ids']:
                pr_info_list.append({'user_id': pr_id, 'name': leader_name_map.get(pr_id, "")})
            d['pr_info_list'] = pr_info_list
            d["bus_user_count"] = len(team_biz_uids)
            statics = team_statics_map.get(item.id)
            d["total_refer_amount"] = statics.total_refer_amount if statics else 0
            d["pending_refer_amount"] = statics.pending_refer_amount if statics else 0
            d["statics_last_update_time"] = int(statics.last_update_time.timestamp()) if statics else 0
            table_items.append(d)
            statics_data['bus_user_count'] += d["bus_user_count"]
            statics_data['effect_refer_trade_amount'] += effect_refer_trade_amount
            statics_data['this_month_deal_amount'] += this_month_deal_amount
            statics_data['last_month_deal_amount'] += last_month_deal_amount
            statics_data['refer_user_balance_usd'] += refer_user_balance_usd
            statics_data['refer_count'] += refer_count
            statics_data['refer_trade_count'] += refer_trade_count
            statics_data['refer_active_user_count'] += refer_active_user_count
            statics_data['total_refer_amount'] += d["total_refer_amount"]
            statics_data['pending_refer_amount'] += d["pending_refer_amount"]
        if sort := kwargs.get('sort'):
            table_items.sort(key=lambda x: x[sort.name], reverse=True)
        return dict(
            total=len(table_items),
            items=table_items,
            statics_data=statics_data,
            extra=dict(
                sorts=cls.SortField
            )
        )

    @classmethod
    def check_params(cls, kwargs: dict):
        leader_id = kwargs["leader_id"]
        pr_user_ids = kwargs.get("pr_user_ids") or []
        if len(set(pr_user_ids)) != len(pr_user_ids):
            raise InvalidArgument(f"统筹账号重复")
        if leader_id in pr_user_ids:
            raise InvalidArgument(f"组长和统筹账号重复")

        q_user_ids = [leader_id] + pr_user_ids
        q_user_rows = User.query.filter(User.id.in_(q_user_ids)).with_entities(User.id).all()
        db_user_ids = {i.id for i in q_user_rows}
        if miss_pr_user_ids := (db_user_ids - set(q_user_ids)):
            if leader_id in miss_pr_user_ids:
                raise InvalidArgument(f"组长{leader_id}不存在")
            raise InvalidArgument(f"统筹账号{miss_pr_user_ids}不存在")

    @classmethod
    def sync_bus_user_pr_rates(cls, team_id: int, new_pr_user_ids: list[int]):
        # 修改团队配置的PR用户时：新增的PR默认分成比例为0，删除的PR，那这个PR的分成比例也就直接删除了
        bus_users: list[BusinessUser] = BusinessUser.query.filter(
            BusinessUser.team_id == team_id,
        ).all()
        for bus_user in bus_users:
            pr_rate_map = {i['user_id']: i['rate'] for i in bus_user.pr_rates}
            new_pr_rates = [
                {
                    "user_id": pr_id,
                    "rate": Decimal(pr_rate_map[pr_id]) if pr_id in pr_rate_map else Decimal(0),
                }
                for pr_id in new_pr_user_ids
            ]
            bus_user.pr_rates = new_pr_rates

    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            name=wa_fields.String(required=True),
            leader_id=wa_fields.Integer(required=True),
            pr_user_ids=wa_fields.List(wa_fields.Integer),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-新增团队"""
        cls.check_params(kwargs)
        leader_id = kwargs["leader_id"]
        pr_user_ids = kwargs.get("pr_user_ids") or []

        team = BusinessTeam()
        team.name = kwargs["name"]
        team.leader_id = leader_id
        team.pr_user_ids = pr_user_ids
        now_ = now()
        team.set_leader_at = now_
        team.leader_refer_start_at = now_.date()
        team.remark = kwargs.get("remark") or ""
        db.session.add(team)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorTeam,
            detail=team.to_dict(enum_to_name=True),
            target_user_id=leader_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            name=wa_fields.String(required=True),
            leader_id=wa_fields.Integer(required=True),
            pr_user_ids=wa_fields.List(wa_fields.Integer),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def put(cls, **kwargs):
        """用户-商务大使体系-编辑团队"""
        cls.check_params(kwargs)
        new_leader_id = kwargs["leader_id"]
        pr_user_ids = kwargs.get("pr_user_ids") or []

        team: BusinessTeam = BusinessTeam.query.get(kwargs["id"])
        old_data = team.to_dict(enum_to_name=True)
        now_ = now()
        if team.leader_id != new_leader_id:
            old_leader_id = team.leader_id
            team.leader_id = new_leader_id
            team.set_leader_at = now()
            team.leader_refer_start_at = next_month(now_.year, now_.month)
            BusRelationChangelog.add(
                biz_id=team.id,
                admin_user_id=g.user.id,
                change_type=BusRelationChangelog.ChangeType.EDIT_TEAM_LEADER,
                old_value=old_leader_id,
                new_value=new_leader_id,
                is_commit=False,
            )
        if (remark := kwargs.get("remark")) is not None:
            team.remark = remark
        if team.pr_user_ids != pr_user_ids:
            old_pr_user_id = team.pr_user_ids
            team.pr_user_ids = pr_user_ids
            BusRelationChangelog.add(
                biz_id=team.id,
                admin_user_id=g.user.id,
                change_type=BusRelationChangelog.ChangeType.EDIT_TEAM_PR,
                old_value=old_pr_user_id,
                new_value=pr_user_ids,
                is_commit=False,
            )
        team.name = kwargs["name"]
        db.session.add(team)
        cls.sync_bus_user_pr_rates(team.id, pr_user_ids)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorTeam,
            old_data=old_data,
            new_data=team.to_dict(enum_to_name=True),
            target_user_id=new_leader_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """用户-商务大使体系-删除团队"""
        team_id = kwargs["id"]
        has_bus_user = BusinessUser.query.filter(
            BusinessUser.team_id == team_id,
        ).first()
        if has_bus_user:
            raise InvalidArgument(message=f"当前团队下存在商务，无法删除")

        team = BusinessTeam.query.get(team_id)
        team.status = BusinessTeam.Status.DELETED
        db.session.add(team)
        db.session.commit()
        now_ = now()
        BusSnapshotHelper.add_snapshot(team, "DEL", now_)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorTeam,
            detail=team.to_dict(enum_to_name=True),
            target_user_id=team.leader_id,
        )


@ns.route("/bus-users")
@respond_with_code
class BusUsersResource(Resource):
    model = BusinessUser

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "商务ID"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "bus_name", Language.ZH_HANS_CN: "商务名称"},
        {"field": "team_name", Language.ZH_HANS_CN: "团队"},
        {"field": "leader_rate", Language.ZH_HANS_CN: "组长分成比例"},
        {"field": "effected_at", Language.ZH_HANS_CN: "成为商务时间"},
        {"field": "bus_amb_count", Language.ZH_HANS_CN: "直属代理数"},
        {"field": "bus_amb_delete_count", Language.ZH_HANS_CN: "失效直属代理数"},
        {"field": "tree_amb_count", Language.ZH_HANS_CN: "子代理数"},
        {"field": "bus_amb_refer_count", Language.ZH_HANS_CN: "代理推荐用户数"},
        {"field": "bus_amb_refer_trade_amount", Language.ZH_HANS_CN: "代理推荐交易用户数"},
        {"field": "refer_active_user_count", Language.ZH_HANS_CN: "代理推荐活跃用户数"},
        {"field": "refer_user_balance_usd", Language.ZH_HANS_CN: "代理推荐用户总资产"},
        {"field": "bus_amb_refer_deal_amount", Language.ZH_HANS_CN: "代理推荐用户累计交易量(USD)"},
        {"field": "total_refer_amount", Language.ZH_HANS_CN: "累计返佣(USDT)"},
        {"field": "pending_refer_amount", Language.ZH_HANS_CN: "当月待返佣(USDT)"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    def get_all_bus_name_map(cls, only_valid: bool = False) -> dict[int, str]:
        model = cls.model
        q = model.query.with_entities(model.user_id)
        if only_valid:
            q = q.filter(model.status == model.Status.VALID)
        bus_rows = q.all()
        bus_ids = [i.user_id for i in bus_rows]
        bus_name_map = get_admin_user_name_map(bus_ids)
        return bus_name_map

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer,
            team_id=wa_fields.Integer,
            status=EnumField(model.Status),
            page=PageField,
            limit=LimitField,
            export=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-商务列表"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        page, limit = kwargs["page"], kwargs["limit"]
        query = model.query.order_by(model.id.desc())

        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_LIST
        )
        leader_own_teams = BusTeamsResource.get_teams_by_leader(g.user.id)
        limit_visible = permission_limit and not is_super_user(g.user.id)

        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if limit_visible:
            # 组长在列表里默认能看到自己组内的商务，能给自己组内的商务备注
            team_ids = [i.id for i in leader_own_teams]
            query = query.filter(model.team_id.in_(team_ids))
        if team_id := kwargs.get("team_id"):
            query = query.filter(model.team_id == team_id)

        is_export = kwargs.get("export")
        if is_export:
            _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
            records = _paginate.items
            total = _paginate.total
        else:
            _paginate = query.paginate(page, limit, error_out=False)
            records = _paginate.items
            total = _paginate.total

        bus_user_ids = {i.user_id for i in records}
        pr_ids = {i['user_id'] for t in records for i in t.pr_rates}

        all_teams = BusinessTeam.query.filter(
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).all()
        team_dict = {i.id: i for i in all_teams}
        team_id_name_dict = {i.id: f"{i.id}-{i.name}" for i in all_teams}
        all_team_pr_ids = {j for i in all_teams for j in i.pr_user_ids}
        all_user_ids = bus_user_ids | pr_ids | all_team_pr_ids
        user_name_map = get_admin_user_name_map(all_user_ids)
        team_id_pr_user_ids_dict = {i.id: i.pr_user_ids for i in all_teams}
        team_pr_user_id_name_dict = {j: user_name_map.get(j, '') for i in all_teams for j in i.pr_user_ids}

        # 商务的统计信息
        bus_statics_map = {
            i.user_id: i for i in BusinessUserStatistics.query.filter(
                BusinessUserStatistics.user_id.in_(bus_user_ids)
            ).all()
        }

        table_items = []
        for item in records:
            bus_uid = item.user_id
            bus_statics: BusinessUserStatistics = bus_statics_map.get(bus_uid)
            d = item.to_dict(enum_to_name=True)
            d["bus_name"] = user_name_map.get(bus_uid, "")
            team = team_dict.get(item.team_id)
            d["team_name"] = team_dict[item.team_id].name if team else ""
            for pr_info in d['pr_rates']:
                pr_info['name'] = user_name_map.get(pr_info['user_id'], "")
            d["bus_amb_count"] = bus_statics.refer_bus_amb_count if bus_statics else 0  # 推荐生效大使数
            d["bus_amb_delete_count"] = bus_statics.refer_bus_amb_delete_count if bus_statics else 0  # 推荐失效大使数
            d["tree_amb_count"] = bus_statics.refer_tree_amb_count if bus_statics else 0  # 推荐tree大使数｜子代理数
            d["bus_amb_refer_count"] = bus_statics.refer_count if bus_statics else 0  # 大使推荐用户数
            d["bus_amb_refer_trade_count"] = bus_statics.refer_trade_count if bus_statics else 0  # 大使推荐交易用户数
            d["refer_active_user_count"] = bus_statics.refer_active_user_count if bus_statics else 0  # 大使推荐refer活跃用户
            d["refer_user_balance_usd"] = bus_statics.refer_user_balance_usd if bus_statics else 0  # 大使推荐用户资产
            d["bus_amb_refer_deal_amount"] = bus_statics.effect_trade_amount if bus_statics else 0  # 大使推荐用户累计交易量（USD）
            d["total_refer_amount"] = bus_statics.total_refer_amount if bus_statics else 0  # 商务累计返佣（USDT）
            d["pending_refer_amount"] = bus_statics.pending_refer_amount if bus_statics else 0  # 当月待返佣（USDT）
            table_items.append(d)

        if is_export:
            for d in table_items:
                d["leader_rate"] = f'{amount_to_str(d["leader_rate"] * 100, 4)}%'
                d["effected_at"] = d["effected_at"].strftime("%Y-%m-%d %H:%M:%S") if d["effected_at"] else ""
                d["status"] = model.Status[d["status"]].value

            return export_xlsx(
                filename="business_user_list",
                data_list=table_items,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=table_items,
            extra=dict(
                team_dict=team_id_name_dict,
                team_id_pr_user_ids_dict=team_id_pr_user_ids_dict,
                team_pr_user_id_name_dict=team_pr_user_id_name_dict,
                status_dict=model.Status,
            ),
        )

    @classmethod
    def check_bus_user_id(cls, user_id: int):
        if not User.query.filter(User.id == user_id).with_entities(User.id).first():
            raise InvalidArgument(f"商务用户{user_id}不存在")
        model = cls.model
        bus_user = model.query.filter(model.user_id == user_id).first()
        if bus_user:
            raise InvalidArgument(f"商务{user_id}已存在")
        agent = AmbassadorAgent.query.filter(
            AmbassadorAgent.user_id == user_id,
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
        ).first()
        if agent:
            raise InvalidArgument(f"用户{user_id}存在生效中的大使代理，不允许设置为商务")

    @classmethod
    def check_pr_params(cls, kwargs: dict):
        leader_rate = Decimal(kwargs["leader_rate"])
        pr_rates = kwargs.get("pr_rates") or []
        if not (0 <= leader_rate <= 1):
            raise InvalidArgument(message=f"组长分成比例不在0～1之间")
        for pr_rate in pr_rates:
            _pr_rate = Decimal(pr_rate['rate'])
            if not (0 <= _pr_rate <= 1):
                raise InvalidArgument(message=f"统筹账号分成比例不在0～1之间")
        sum_rate = Decimal(leader_rate) + sum([Decimal(i["rate"]) for i in pr_rates])
        if not (0 <= sum_rate <= Decimal(1)):
            raise InvalidArgument(message=f"组长分成比例+统筹账号分成比例 不在：[0, 1]")
        pr_user_ids = [i['user_id'] for i in pr_rates]
        if len(set(pr_user_ids)) != len(pr_user_ids):
            raise InvalidArgument(f"统筹账号重复")

        team_id = kwargs["team_id"]
        team: BusinessTeam = BusinessTeam.query.filter(
            BusinessTeam.id == team_id,
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).first()
        if not team:
            raise InvalidArgument(f"团队{team_id}不存在")
        if set(pr_user_ids) != set(team.pr_user_ids):
            raise InvalidArgument(f"设置分成统筹账号用户ID 和 团队内统筹账号用户ID 不一致，请刷新页面重新操作")

    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            team_id=wa_fields.Integer(required=True),
            leader_rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                             required=True, allow_zero=True),
            pr_rates=wa_fields.Nested(PrUserRateSchema, many=True),  # noqa
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-新建商务"""
        model = cls.model
        user_id = kwargs["user_id"]
        cls.check_bus_user_id(user_id)
        cls.check_pr_params(kwargs)
        team_id = kwargs["team_id"]
        team = BusinessTeam.query.filter(
            BusinessTeam.id == team_id,
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).first()
        if not team:
            raise InvalidArgument(f"团队{team_id}不存在")

        leader_rate = Decimal(kwargs["leader_rate"])
        pr_rates = [{"user_id": i["user_id"], "rate": i["rate"]} for i in kwargs.get("pr_rates") or []]
        bus_user: model = model.get_or_create(user_id=user_id)
        bus_user.team_id = team_id
        bus_user.leader_rate = leader_rate
        bus_user.pr_rates = pr_rates
        now_ = now()
        bus_user.effected_at = now_
        bus_user.bind_team_at = now_
        bus_user.leader_refer_start_at = now_.date()
        bus_user.remark = kwargs.get("remark") or ""
        bus_user.status = model.Status.VALID
        db.session.add(bus_user)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusUser,
            detail=kwargs,
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            team_id=wa_fields.Integer(required=True),
            leader_rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                             required=True, allow_zero=True),
            pr_rates=wa_fields.Nested(PrUserRateSchema, many=True),  # noqa
            remark=wa_fields.String(allow_none=True),
        )
    )
    def put(cls, **kwargs):
        """用户-商务大使体系-编辑商务"""
        model = cls.model
        user_id = kwargs["user_id"]
        bus_user: model = model.query.filter(model.user_id == user_id).first()
        if not bus_user:
            raise InvalidArgument(f"商务{user_id}不存在")
        old_data = bus_user.to_dict(enum_to_name=True)
        cls.check_pr_params(kwargs)
        leader_rate = Decimal(kwargs["leader_rate"])
        pr_rates = [{"user_id": i["user_id"], "rate": i["rate"]} for i in kwargs.get("pr_rates") or []]

        now_ = now()
        new_team_id = kwargs["team_id"]
        change_team = False
        if bus_user.team_id != new_team_id:
            team = BusinessTeam.query.filter(
                BusinessTeam.id == new_team_id,
                BusinessTeam.status == BusinessTeam.Status.VALID,
            ).first()
            if not team:
                raise InvalidArgument(f"团队{new_team_id}不存在")

            old_team_id = bus_user.team_id
            bus_user.team_id = new_team_id
            bus_user.bind_team_at = now_
            bus_user.leader_refer_start_at = next_month(now_.year, now_.month)

            BusRelationChangelog.add(
                biz_id=bus_user.user_id,
                admin_user_id=g.user.id,
                change_type=BusRelationChangelog.ChangeType.EDIT_BUS_USER_TEAM,
                old_value=old_team_id,
                new_value=new_team_id,
                is_commit=False,
            )
            change_team = True

        if bus_user.leader_rate != leader_rate:
            old_leader_rate = bus_user.leader_rate
            bus_user.leader_rate = leader_rate
            BusRelationChangelog.add(
                biz_id=bus_user.user_id,
                admin_user_id=g.user.id,
                change_type=BusRelationChangelog.ChangeType.EDIT_BUS_USER_LEADER_RATE,
                old_value=old_leader_rate,
                new_value=leader_rate,
                is_commit=False,
            )

        if bus_user.pr_rates != pr_rates:
            old_pr_rates = bus_user.pr_rates
            bus_user.pr_rates = pr_rates
            BusRelationChangelog.add(
                biz_id=bus_user.user_id,
                admin_user_id=g.user.id,
                change_type=BusRelationChangelog.ChangeType.EDIT_BUS_USER_PR_RATE,
                old_value=old_pr_rates,
                new_value=pr_rates,
                is_commit=False,
            )
        bus_user.remark = kwargs.get("remark") or ""
        db.session.add(bus_user)
        db.session.commit()
        if change_team:
            update_business_user_ambassadors_last_team_id.delay(bus_user.user_id)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusUser,
            old_data=old_data,
            new_data=bus_user.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            status=EnumField(BusinessUser.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """用户-商务大使体系-编辑商务状态"""
        model = cls.model
        user_id = kwargs["user_id"]
        bus_user: model = model.query.filter(model.user_id == user_id).first()
        if not bus_user:
            raise InvalidArgument(f"商务{user_id}不存在")
        old_data = bus_user.to_dict(enum_to_name=True)
        cls.check_pr_params(bus_user.to_dict())

        old_status = bus_user.status
        new_status = kwargs["status"]
        if bus_user.status == new_status:
            return

        now_ = now()
        bus_user.status = new_status
        if new_status == model.Status.VALID:
            bus_user.effected_at = now_
            bus_user.bind_team_at = now_
            bus_user.leader_refer_start_at = next_month(now_.year, now_.month)
        db.session.add(bus_user)

        BusRelationChangelog.add(
            biz_id=bus_user.user_id,
            admin_user_id=g.user.id,
            change_type=BusRelationChangelog.ChangeType.EDIT_BUS_USER_STATUS,
            old_value=old_status.value,
            new_value=new_status.value,
            is_commit=False,
        )

        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusUser,
            old_data=old_data,
            new_data=bus_user.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route("/bus-users/export")
@respond_with_code
class BusUsersExportResource(Resource):
    model = BusinessUser

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer,
            team_id=wa_fields.Integer,
            status=EnumField(model.Status),
            page=PageField,
            limit=LimitField,
            export=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-商务列表-导出"""
        kwargs["export"] = True
        return BusUsersResource.get_(**kwargs)


@ns.route('/bus-ambassador-audits')
@respond_with_code
class BusAmbAuditsResource(Resource):
    model = BusinessAmbassadorAudit

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "流水ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "bus_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "change_desc", Language.ZH_HANS_CN: "操作详情"},
        {"field": "created_at", Language.ZH_HANS_CN: "提交时间"},
        {"field": "creator_email", Language.ZH_HANS_CN: "提交人"},
        {"field": "first_audited_at", Language.ZH_HANS_CN: "初审时间"},
        {"field": "first_auditor_email", Language.ZH_HANS_CN: "初审人"},
        {"field": "audited_at", Language.ZH_HANS_CN: "复审时间"},
        {"field": "auditor_email", Language.ZH_HANS_CN: "复审人"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "comment", Language.ZH_HANS_CN: "审核意见"},
    )

    @classmethod
    def paginate_amb_audit_rows(
        cls,
        page: int,
        limit: int,
        bus_amb_id: int,
    ) -> tuple[list[dict], int]:
        """ 查询某个`商务大使`的审核记录，用于商务大使详情展示 """
        model = cls.model
        query = model.query.filter(
            model.user_id == bus_amb_id,
        ).order_by(model.id.desc())
        _paginate = query.paginate(page, limit, error_out=False)
        records = _paginate.items
        total = _paginate.total
        res = []
        user_ids = set(i.creator for i in records) | set(i.auditor for i in records)
        user_name_map = get_admin_user_name_map(user_ids)

        for item in records:
            d = item.to_dict(enum_to_name=True)
            d["creator_name"] = user_name_map.get(item.creator, "")
            d["auditor_name"] = user_name_map.get(item.auditor, "")
            d["first_auditor_name"] = user_name_map.get(item.first_auditor, "")
            d["file_urls"] = cls.format_file_url(item.file_keys)
            d["change_desc_list"] = cls.format_change_desc_list(item)
            res.append(d)
        return res, total

    @classmethod
    def paginate_add_amb_audit_rows(
        cls,
        page: int,
        limit: int,
        bus_user_ids: list[int],
        status: model.Status,
    ) -> tuple[list[model], int]:
        """ 查询`新增商务大使`的审核记录，用于商务大使列表展示 """
        model = cls.model
        query = model.query.filter(
            model.type == model.Type.ADD_AMB,
        ).order_by(model.id.desc())
        if bus_user_ids is not None:
            query = query.filter(model.bus_user_id.in_(bus_user_ids))
        if status:
            query = query.filter(model.status == status)
        _paginate = query.paginate(page, limit, error_out=False)
        records = _paginate.items
        total = _paginate.total
        return records, total

    @classmethod
    def format_change_desc_list(cls, row: model) -> list[str]:
        model = BusinessAmbassador

        def to_enum_val(enum_class, v):
            try:
                return enum_class[v].value
            except (ValueError, KeyError):
                try:
                    return enum_class(v).value
                except (ValueError, KeyError):
                    pass
            return v

        old_data = json.loads(row.old_data) if row.old_data else {}
        new_data = json.loads(row.new_data) if row.new_data else {}
        if row.type == cls.model.Type.ADD_AMB:
            n1 = [
                    f"代理 {i['target_user_id']} 返佣比例 {i['rate']}"
                    for i in new_data["share_users"]
                ] if new_data.get("share_users") else []
            desc_l = [
                f"大使返佣比例 {new_data['rate']}",
                *n1,
            ]
            if 'delay_repay_month' in new_data:
                desc_l.append(
                    f'预付金还款时间 {new_data["delay_repay_month"]} 月'
                )
        elif row.type == cls.model.Type.EDIT_AMB_STATUS:
            o1 = old_data['status'] if old_data else ''
            o1 = to_enum_val(model.Status, o1)
            n1 = new_data['status'] if new_data else ''
            n1 = to_enum_val(model.Status, n1)
            desc_l = [
                f"{o1} 改为 {n1}",
            ]
        elif row.type == cls.model.Type.EDIT_RATE:
            o1 = []
            if old_data.get('rate'):
                o1.append(f"大使返佣比例 {old_data.get('rate')}")
            if old_data.get("share_users"):
                o1.extend([
                    f"代理 {i['target_user_id']} 返佣比例 {i['rate']}"
                    for i in old_data["share_users"]
                ])
            n1 = []
            if new_data.get('rate'):
                n1.append(f"大使返佣比例 {new_data.get('rate')}")
            if new_data.get("share_users"):
                n1.extend([
                    f"代理 {i['target_user_id']} 返佣比例 {i['rate']}"
                    for i in new_data["share_users"]
                ])
            desc_l = [
                f"修改前：",
                *o1,
                f"修改后：",
                *n1
            ]
        elif row.type == cls.model.Type.EDIT_BUS_USER:
            o1 = old_data['bus_user_id'] if old_data else ''
            n1 = new_data['bus_user_id'] if new_data else ''
            desc_l = [
                f"商务由 {o1} 改为 {n1}",
            ]
        elif row.type == cls.model.Type.EDIT_DELAY_REPAY_MONTH:
            o1 = old_data['delay_repay_month'] if old_data else 0
            n1 = new_data['delay_repay_month'] if new_data else 0
            desc_l = [
                f"预付金还款时间由 {o1}月 改为 {n1}月",
            ]
        elif row.type == cls.model.Type.RESTORE_INVALID_REFERRAL:
            desc_l = [
                f"恢复失效邀请关系",
            ]
        elif row.type == cls.model.Type.EDIT_ALLOW_ADD_CHILD:
            o1 = "是" if old_data and old_data.get('allow_add_child') else "否"
            n1 = "是" if new_data and new_data.get('allow_add_child') else "否"
            desc_l = [
                f"{o1} 改为 {n1}",
            ]
        elif row.type == cls.model.Type.EDIT_AMB_TYPE:
            o1 = old_data['type'] if old_data else ''
            o1 = to_enum_val(model.Type, o1)
            n1 = new_data['type'] if new_data else ''
            n1 = to_enum_val(model.Type, n1)
            desc_l = [
                f"代理类型由 {o1} 改为 {n1}",
            ]
        elif row.type == cls.model.Type.DELETE_AMB:
            desc_l = [
                f"删除总代理",
            ]
        else:
            desc_l = []
        return desc_l

    @classmethod
    def format_file_url(cls, file_keys):
        return [AWSBucketPublic.get_file_url(i) for i in file_keys] if file_keys else []

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status),
        type=EnumField(model.Type),
        amb_type=EnumField(BusinessAmbassador.Type),
        user_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使审核列表"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        page, limit = kwargs["page"], kwargs["limit"]
        query = model.query.order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if bus_user_id := kwargs.get("bus_user_id"):
            query = query.filter(model.bus_user_id == bus_user_id)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if type_ := kwargs.get("type"):
            query = query.filter(model.type == type_)
        if amb_type := kwargs.get("amb_type"):
            query = query.filter(model.amb_type == amb_type)

        is_export = kwargs.get('export')
        if is_export:
            _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
            records = _paginate.items
            total = _paginate.total
        else:
            _paginate = query.paginate(page, limit, error_out=False)
            records = _paginate.items
            total = _paginate.total

        records: list[model]
        amb_ids = [item.user_id for item in records]
        user_rows = User.query.filter(
            User.id.in_(amb_ids)
        ).with_entities(User.id, User.email).all()
        user_email_map = {i.id: i.email for i in user_rows}
        user_ids = set()
        for r in records:
            user_ids.update((r.creator, r.auditor, r.bus_user_id, r.first_auditor))
        user_name_map = get_admin_user_name_map(user_ids)

        res = []
        for item in records:
            d = item.to_dict(enum_to_name=True)
            d["email"] = user_email_map.get(item.user_id, "")
            d["creator_name"] = user_name_map.get(item.creator, "")
            d["auditor_name"] = user_name_map.get(item.auditor, "")
            d["first_auditor_email"] = user_name_map.get(item.first_auditor, "")
            d["bus_name"] = user_name_map.get(item.bus_user_id, "")
            d["change_desc_list"] = cls.format_change_desc_list(item)
            _new_data = json.loads(item.new_data) if item.new_data else {}
            d["mobile_num"] = _new_data.get("mobile_num", "")
            d["file_urls"] = cls.format_file_url(item.file_keys)
            res.append(d)

        if is_export:
            for d in res:
                d["change_desc"] = "\n".join(d["change_desc_list"])
                d["created_at"] = d["created_at"].strftime("%Y-%m-%d %H:%M:%S") if d["created_at"] else ""
                d["audited_at"] = d["audited_at"].strftime("%Y-%m-%d %H:%M:%S") if d["audited_at"] else ""
                d["first_audited_at"] = d["first_audited_at"].strftime("%Y-%m-%d %H:%M:%S") \
                    if d["first_audited_at"] else ""
                d["status"] = model.Status[d["status"]].value
                d["type"] = model.Type[d["type"]].value

            return export_xlsx(
                filename="business_ambassador_audit_list",
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
            extra=dict(
                status_dict=model.Status,
                type_dict=model.Type,
                amb_type_dict=BusinessAmbassador.Type,
            ),
        )


@ns.route('/bus-ambassador-audits/export')
@respond_with_code
class BusAmbAuditsExportResource(Resource):
    model = BusinessAmbassadorAudit

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status),
        type=EnumField(model.Type),
        user_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使审核列表-导出"""
        kwargs["export"] = True
        return BusAmbAuditsResource.get_(**kwargs)


class BusAmbShareUserSchema(Schema):
    """ 商务大使返佣分成对象-scheme """
    class Meta:
        UNKNOWN = EXCLUDE
    # id = wa_fields.Integer(required=False)  # row id
    target_user_id = wa_fields.Integer(required=True)
    rate = PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN, required=True, allow_zero=True)
    # remark = wa_fields.String(required=True)


@ns.route('/bus-ambassador-audits/add-tree-root-amb')
@respond_with_code
class AddTreeRootBusAmbAuditResource(Resource):

    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            bus_user_id=wa_fields.Integer(allow_none=True),
            type=EnumField(BusinessAmbassador.Type, required=True),
            rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                      required=True, allow_zero=True),
            # team_rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN, required=True),
            share_users=wa_fields.Nested(BusAmbShareUserSchema, many=True),  # noqa
            mobile_num=wa_fields.String(allow_none=True),
            delay_repay_month=wa_fields.Integer(allow_none=True),
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使列表-新增总代理商务大使(审核)"""
        if kwargs['type'] not in [BusinessAmbassador.Type.TREE_ROOT]:
            raise InvalidArgument(message=f"不支持新增{kwargs['type'].value}类型的商务大使")
        AddBusAmbAuditHelper.post(**kwargs)


@ns.route('/bus-ambassador-audits/add-amb')
@respond_with_code
class AddBusAmbAuditResource(Resource):

    DEFAULT_TEAM_RATE = Decimal("0.2")  # 写死20%

    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            bus_user_id=wa_fields.Integer(allow_none=True),
            type=EnumField(BusinessAmbassador.Type, required=True),
            rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                      required=True, allow_zero=True),
            # team_rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN, required=True),
            share_users=wa_fields.Nested(BusAmbShareUserSchema, many=True),  # noqa
            mobile_num=wa_fields.String(allow_none=True),
            delay_repay_month=wa_fields.Integer(allow_none=True),
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使列表-新增商务大使(审核)"""
        if kwargs['type'] not in [BusinessAmbassador.Type.NORMAL, BusinessAmbassador.Type.BROKER]:
            raise InvalidArgument(message=f"不支持新增{kwargs['type'].value}类型的商务大使")
        AddBusAmbAuditHelper.post(**kwargs)


class AddBusAmbAuditHelper:

    DEFAULT_TEAM_RATE = Decimal("0.2")  # 写死20%
    @classmethod
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使列表-新增商务大使(审核)"""
        team_rate = cls.DEFAULT_TEAM_RATE
        user_id = kwargs["user_id"]
        bus_user_id = kwargs.get("bus_user_id") or None
        rate = Decimal(kwargs["rate"])
        delay_repay_month = kwargs.get('delay_repay_month') or 0
        allow_add_child = False
        if not (0 <= rate <= 1):
            raise InvalidArgument(message=f"代理返佣比例不在0～1之间")
        if delay_repay_month < 0:
            raise InvalidArgument(message=f"代理预付金还款时间不能为负数")
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.ADD_AMB,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        amb_type = kwargs['type']
        if amb_type == BusinessAmbassador.Type.BROKER:
            # Broker商务大使：商务代理返佣比例、预付金还款时间这两个字段不需要填
            share_users = []
            delay_repay_month = 0
        elif amb_type == BusinessAmbassador.Type.TREE_ROOT:
            share_users = []
            allow_add_child = True
        else:
            share_users = kwargs.get("share_users") or []
        param = dict(
            user_id=user_id,
            bus_user_id=bus_user_id,
            rate=rate,
            type=amb_type.name,
            team_rate=team_rate,
            share_users=share_users,
            mobile_num=kwargs.get("mobile_num") or "",
            delay_repay_month=delay_repay_month,
            allow_add_child=allow_add_child,
        )
        BusAmbassadorEditor.check_add_bus_amb_param(param)
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_user_id,
            amb_type=amb_type,
            type=BusinessAmbassadorAudit.Type.ADD_AMB,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=kwargs["file_keys"],
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            detail=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route('/bus-ambassador-audits/edit-amb-status')
@respond_with_code
class EditBusAmbStatusAuditResource(Resource):
    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            status=EnumField([i.name for i in BusinessAmbassador.Status], required=True),
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-修改状态(需审核)"""
        user_id = kwargs["user_id"]
        param = dict(
            user_id=user_id,
            status=kwargs["status"],
        )
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_AMB_STATUS,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")
        bus_amb = BusAmbassadorEditor.check_edit_amb_status_param(param)
        old_data = {"status": bus_amb.status.name}
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_AMB_STATUS,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=kwargs["file_keys"]
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
            only_special_data=True,
        )


@ns.route('/bus-ambassador-audits/delete-amb')
@respond_with_code
class DeleteBusAmbAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-删除总代理(需审核)"""
        user_id = kwargs["user_id"]
        param = dict(
            user_id=user_id,
            status=BusinessAmbassador.Status.DELETED.name,
        )
        bus_amb = BusAmbassadorEditor.check_edit_amb_status_param(param)
        if not bus_amb.is_tree_root_amb:
            raise InvalidArgument(message="只有总代理能删除")
        if bus_amb.status != BusinessAmbassador.Status.VALID:
            raise InvalidArgument(message="总代理状态不是正常")
        if TreeAmbHelper.has_child(user_id):
            raise InvalidArgument(message="该用户存在子代理，不允许删除")
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.DELETE_AMB,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        old_data = {"status": bus_amb.status.name}
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.DELETE_AMB,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=[],
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route('/bus-ambassador-audits/edit-type')
@respond_with_code
class EditBusAmbTypeAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            edit_amb_type=EnumField(BusinessAmbassador.Type, required=True),
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-修改代理类型(需审核)"""
        amb_type = kwargs['edit_amb_type']
        if amb_type not in [
            BusinessAmbassador.Type.BROKER,
        ]:
            raise InvalidArgument(message=f"不支持修改为{amb_type.value}")

        user_id = kwargs["user_id"]
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_AMB_TYPE,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        param = dict(
            user_id=user_id,
            type=amb_type.name,
        )
        bus_amb = BusAmbassadorEditor.check_edit_amb_type_param(param)

        old_data = {"type": bus_amb.type.name}
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_AMB_TYPE,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=kwargs["file_keys"]
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route('/bus-ambassador-audits/edit-allow-add-child')
@respond_with_code
class EditBusAmbAllowAddChildAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            allow_add_child=wa_fields.Boolean(required=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-修改允许扩展子代理(需审核)"""
        user_id = kwargs["user_id"]
        param = dict(
            user_id=user_id,
            allow_add_child=kwargs['allow_add_child'],
        )
        bus_amb = BusAmbassadorEditor.check_and_get_root_tree_amb(user_id)
        if bool(bus_amb.allow_add_child) == kwargs['allow_add_child']:
            raise InvalidArgument('无需修改')
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_ALLOW_ADD_CHILD,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        old_data = {"allow_add_child": bool(bus_amb.allow_add_child)}
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_ALLOW_ADD_CHILD,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=[],
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route('/bus-ambassador-audits/edit-rate')
@respond_with_code
class EditBusAmbRateAuditResource(Resource):
    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                      required=True, allow_zero=True),
            share_users=wa_fields.Nested(BusAmbShareUserSchema, many=True),  # noqa
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-编辑返佣比例(需审核)"""
        cls.do_business(**kwargs)

    @classmethod
    def do_business(cls, **kwargs):
        user_id = kwargs["user_id"]
        rate = Decimal(kwargs["rate"])
        if not (0 <= rate <= 1):
            raise InvalidArgument(message=f"代理返佣比例不在0～1之间")
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_RATE,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        model = BusinessAmbassador
        bus_amb: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        if bus_amb.is_broker_amb or bus_amb.is_tree_root_amb:
            share_users = []
        else:
            share_users = kwargs.get("share_users") or []

        param = dict(
            user_id=user_id,
            rate=rate,
            share_users=share_users,
        )
        amb_row = BusAmbassadorEditor.check_edit_rate_param(param)
        db_share_rows = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()
        old_share_users = [
            {"target_user_id": i.target_user_id, "rate": i.rate}
            for i in db_share_rows
        ]
        old_data = {"share_users": old_share_users, "rate": amb_row.rate}

        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_RATE,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=kwargs["file_keys"]
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
            only_special_data=True,
        )


@ns.route('/bus-ambassador-audits/edit-bus-user')
@respond_with_code
class EditBusAmbBusUserAuditResource(Resource):
    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            bus_user_id=wa_fields.Integer,
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-编辑绑定商务(需审核)"""
        cls.do_business(**kwargs)

    @classmethod
    def do_business(cls, **kwargs):
        user_id = kwargs["user_id"]
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_BUS_USER,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        param = dict(
            user_id=user_id,
            bus_user_id=kwargs.get("bus_user_id") or None,
        )
        bus_amb = BusAmbassadorEditor.check_edit_bus_user_param(param)
        old_data = {"bus_user_id": bus_amb.bus_user_id}
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_BUS_USER,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
            file_keys=kwargs["file_keys"]
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
            only_special_data=True,
        )


@ns.route('/bus-ambassador-audits/restore-invalid-referral')
@respond_with_code
class RestoreAmbInvalidReferralAuditResource(Resource):
    @classmethod
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-恢复失效邀请关系(需审核)"""
        user_id = kwargs["user_id"]
        bus_amb = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id == user_id,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        referral_history = ReferralHistory.query.filter(
            ReferralHistory.referrer_id == user_id,
            ReferralHistory.status == ReferralHistory.Status.EXPIRED
        ).first()
        if not referral_history:
            raise InvalidArgument(f"该代理名下没有失效的邀请关系")
        audit = BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.RESTORE_INVALID_REFERRAL,
            BusinessAmbassadorAudit.status.in_([
                BusinessAmbassadorAudit.Status.CREATED,
                BusinessAmbassadorAudit.Status.FIRST_PASSED
            ])
        ).first()
        if audit:
            raise InvalidArgument(f"该类型的操作存在待审核的记录，无法提交")

        param = dict(user_id=user_id)
        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.RESTORE_INVALID_REFERRAL,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(param, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
            only_special_data=True,
        )


@ns.route('/bus-ambassador-audits/edit-delay-repay-month')
@respond_with_code
class EditBusAmbDelayRepayMonthAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            delay_repay_month=wa_fields.Integer(required=True),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-编辑预付金还款时间(需审核)"""
        user_id = kwargs["user_id"]
        delay_repay_month = kwargs["delay_repay_month"]
        if delay_repay_month < 0:
            raise InvalidArgument(message=f"代理预付金还款时间不能为负数")
        if BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.EDIT_DELAY_REPAY_MONTH,
            BusinessAmbassadorAudit.status.in_([BusinessAmbassadorAudit.Status.CREATED, BusinessAmbassadorAudit.Status.FIRST_PASSED]),
        ).first():
            raise InvalidArgument(message="该用户存在待审核的同类型审核申请")

        param = dict(
            user_id=user_id,
            delay_repay_month=delay_repay_month,
        )

        bus_amb = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id == user_id,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")

        old_data = {'delay_repay_month': bus_amb.delay_repay_month}

        audit_row = BusinessAmbassadorAudit(
            user_id=user_id,
            bus_user_id=bus_amb.bus_user_id,
            amb_type=bus_amb.type,
            type=BusinessAmbassadorAudit.Type.EDIT_DELAY_REPAY_MONTH,
            creator=g.user.id,
            remark=kwargs.get("remark") or "",
            old_data=json.dumps(old_data, cls=JsonEncoder),
            new_data=json.dumps(param, cls=JsonEncoder),
        )
        db.session.add(audit_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            special_data=audit_row.to_dict(enum_to_name=True),
            target_user_id=user_id,
            only_special_data=True,
        )


@ns.route('/bus-ambassador-audits/do-audit')
@respond_with_code
class DoBusAmbAuditResource(Resource):
    model = BusinessAmbassadorAudit

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            status=EnumField([model.Status.FIRST_PASSED.name, model.Status.REJECTED.name], required=True),
            comment=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使审核-初审"""
        # 初审接口
        model = cls.model
        row: model = model.query.get(kwargs["id"])
        if row.status != model.Status.CREATED:
            raise InvalidArgument(message="状态不是待初审")
        old_data = row.to_dict(enum_to_name=True)

        new_status = kwargs["status"]
        row.status = new_status
        row.first_comment = kwargs.get("comment") or ""
        row.first_auditor = g.user.id
        row.first_audited_at = now()
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route('/bus-ambassador-audits/do-audit-batch')
@respond_with_code
class DoBusAmbAuditBatchResource(Resource):
    model = BusinessAmbassadorAudit

    @classmethod
    @ns.use_kwargs(
        dict(
            ids=wa_fields.List(wa_fields.Integer(), required=True),
            status=EnumField([model.Status.FIRST_PASSED.name, model.Status.REJECTED.name], required=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使审核-初审(批处理)"""
        rows: list[BusinessAmbassadorAudit] = BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.id.in_(kwargs['ids']),
        ).all()
        for row in rows:
            if row.status != BusinessAmbassadorAudit.Status.CREATED:
                raise InvalidArgument(message=f'id: {row.id}, user_id: {row.user_id}的状态不是待初审')

        new_status = kwargs['status']
        for row in rows:
            row.status = new_status
            row.first_comment = ''
            row.first_auditor = g.user.id
            row.first_audited_at = now()
        db.session.commit()

        for row in rows:
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
                old_data=dict(status=BusinessAmbassadorAudit.Status.CREATED.name),
                new_data=dict(status=row.status.name),
                target_user_id=row.user_id,
            )


@ns.route('/bus-ambassador-audits/do-review')
@respond_with_code
class DoBusAmbReviewResource(Resource):
    model = BusinessAmbassadorAudit

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            status=EnumField([model.Status.PASSED.name, model.Status.REJECTED.name], required=True),
            comment=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使审核-复审"""
        model = cls.model
        row: model = model.query.get(kwargs["id"])
        if row.status != model.Status.FIRST_PASSED:
            raise InvalidArgument(message="状态不是待复审")
        if row.first_auditor == g.user.id:
            raise InvalidArgument(message="初审和复审不能是同一个人")

        old_status = row.status
        new_status = kwargs["status"]
        op_time = now()
        row.status = new_status
        row.comment = kwargs.get("comment") or ""
        row.auditor = g.user.id
        row.audited_at = op_time
        row.file_keys = kwargs["file_keys"]
        db.session.add(row)
        if new_status == model.Status.PASSED.name:
            _old_data = cls.do_pass_op(row, op_time)
            row.old_data = json.dumps(_old_data, cls=JsonEncoder)
        db.session.add(row)
        db.session.commit()

        log_data = row.to_dict(enum_to_name=True)
        log_data['status'] = f'{old_status.name} -> {new_status}'
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            detail=log_data,
            target_user_id=row.user_id,
        )

    @classmethod
    def do_pass_op(cls, row: model, op_time: datetime) -> dict:
        op_type_func_map = {
            cls.model.Type.ADD_AMB: BusAmbassadorEditor.do_add_bus_amb,
            cls.model.Type.EDIT_AMB_STATUS: BusAmbassadorEditor.do_edit_amb_status,
            cls.model.Type.DELETE_AMB: BusAmbassadorEditor.do_edit_amb_status,
            cls.model.Type.EDIT_AMB_TYPE: BusAmbassadorEditor.do_edit_amb_type,
            cls.model.Type.EDIT_ALLOW_ADD_CHILD: BusAmbassadorEditor.do_edit_amb_allow_add_child,
            cls.model.Type.EDIT_RATE: BusAmbassadorEditor.do_edit_rate,
            cls.model.Type.EDIT_BUS_USER: BusAmbassadorEditor.do_edit_bus_user,
            cls.model.Type.EDIT_DELAY_REPAY_MONTH: BusAmbassadorEditor.do_edit_delay_repay_month,
            cls.model.Type.RESTORE_INVALID_REFERRAL: BusAmbassadorEditor.do_restore_invalid_referral,
        }
        async_task_map = {
            cls.model.Type.EDIT_BUS_USER: update_business_ambassadors_last_team_id
        }
        func_: Callable = op_type_func_map.get(row.type)
        if not func_:
            raise InvalidArgument(message=f"不支持的操作类型{row.type.name}")
        if not row.new_data:
            raise InvalidArgument(message=f"修改数据为空")
        new_data = json.loads(row.new_data)
        new_data["__op_time"] = op_time
        new_data["__amb_type"] = row.amb_type
        res = func_(new_data)
        if _task := async_task_map.get(row.type):
            _task.delay(row.user_id)
        return res


@ns.route('/bus-ambassador-audits/do-review-batch')
@respond_with_code
class DoBusAmbReviewBatchResource(Resource):
    model = BusinessAmbassadorAudit

    @classmethod
    @ns.use_kwargs(
        dict(
            ids=wa_fields.List(wa_fields.Integer(), required=True),
            status=EnumField([model.Status.PASSED.name, model.Status.REJECTED.name], required=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使审核-复审(批处理)"""
        rows: list[BusinessAmbassadorAudit] = BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.id.in_(kwargs['ids']),
        ).all()
        for row in rows:
            if row.status != BusinessAmbassadorAudit.Status.FIRST_PASSED:
                raise InvalidArgument(message=f'id: {row.id}, user_id: {row.user_id}的状态不是待复审')
            if row.first_auditor == g.user.id:
                raise InvalidArgument(message=f'id: {row.id}, user_id: {row.user_id}的初审和复审不能是同一个人')

        new_status = kwargs['status']
        for row in rows:
            op_time = now()
            row.status = new_status
            row.comment = ''
            row.auditor = g.user.id
            row.audited_at = op_time
            if new_status == BusinessAmbassadorAudit.Status.PASSED.name:
                _old_data = DoBusAmbReviewResource.do_pass_op(row, op_time)
                row.old_data = json.dumps(_old_data, cls=JsonEncoder)
        db.session.commit()

        for row in rows:
            log_data = row.to_dict(enum_to_name=True)
            log_data['status'] = f'{BusinessAmbassadorAudit.Status.FIRST_PASSED.name} -> {new_status}'
            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
                detail=log_data,
                target_user_id=row.user_id,
            )


class BusAmbassadorEditor:
    """ 商务大使的新增、修改等逻辑 """
    model = BusinessAmbassador

    @classmethod
    def check_share_users(cls, user_id: int, rate: Decimal, share_users: list[dict]):
        """ 检查商务大使 和 关联的商务代理 """
        model = cls.model
        share_ids = [i["target_user_id"] for i in share_users]
        tree_ambs = [i.user_id for i in TreeAmbassador.query.filter(
            TreeAmbassador.user_id.in_(share_ids),
            TreeAmbassador.status == TreeAmbassador.Status.VALID,
        ).with_entities(
            TreeAmbassador.user_id,
        ).all()]
        if tree_ambs:
            raise InvalidArgument(message=f"商务代理{tree_ambs}存在生效中的子代理，不允许设置为商务代理")
        root_ambs = [i.user_id for i in BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
            BusinessAmbassador.type == BusinessAmbassador.Type.TREE_ROOT,
            BusinessAmbassador.user_id.in_(share_ids),
        ).with_entities(
            BusinessAmbassador.user_id,
        ).all()]
        if root_ambs:
            raise InvalidArgument(message=f"商务代理{root_ambs}存在生效中的总代理，不允许设置为商务代理")
        broker_ambs =  [i.user_id for i in BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
            BusinessAmbassador.type == BusinessAmbassador.Type.BROKER,
            BusinessAmbassador.user_id.in_(share_ids),
        ).with_entities(
            BusinessAmbassador.user_id,
        ).all()]
        if broker_ambs:
            raise InvalidArgument(message=f"商务代理{broker_ambs}存在生效中的Broker，不允许设置为商务代理")
        for s in share_users:
            if not (0 <= Decimal(s["rate"]) <= 1):
                raise InvalidArgument(message=f"关联的商务代理{s['target_user_id']}返佣比例不在0～1之间")
        if user_id in share_ids:
            raise InvalidArgument(message=f"商务大使在商务代理中")
        if len(share_ids) != len(set(share_ids)):
            raise InvalidArgument(message=f"商务代理重复")
        share_bus_amb = model.query.filter(
            model.user_id.in_(share_ids),
            model.status == model.Status.VALID,
        ).all()
        if len(share_ids) != len(share_bus_amb):
            diff_share_ids = set(share_ids) - {i.user_id for i in share_bus_amb}
            raise InvalidArgument(message=f"{diff_share_ids}不是有效的商务代理")
        sum_rate = Decimal(rate) + sum([Decimal(i["rate"]) for i in share_users])
        if not (0 <= sum_rate <= Decimal(1)):
            raise InvalidArgument(message=f"商务大使返佣比例+所有关联的商务代理的返佣比例 不在：[0, 100%]")

    @classmethod
    def check_add_bus_amb_param(cls, param: dict):
        """ 检查增加商务大使的参数 """
        user_id = param["user_id"]
        if not User.query.filter(User.id == user_id).with_entities(User.id).first():
            raise InvalidArgument(f"代理用户{user_id}不存在")
        model = cls.model
        bus_amb = model.query.filter(model.user_id == user_id).first()
        if bus_amb and bus_amb.status == model.Status.VALID:
            raise InvalidArgument(f"代理{user_id}已存在，且状态是正常")
        bus_amb_audit_row = BusinessAmbassadorAudit.query.filter(
            BusinessAmbassadorAudit.user_id == user_id,
            BusinessAmbassadorAudit.status == BusinessAmbassadorAudit.Status.CREATED,
            BusinessAmbassadorAudit.type == BusinessAmbassadorAudit.Type.ADD_AMB,
        ).first()
        if bus_amb_audit_row:
            raise InvalidArgument(f"{user_id}已存在待审核的代理申请记录")
        nor_amb_audit_row = AmbassadorApplication.query.filter(
            AmbassadorApplication.user_id == user_id,
            AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
            AmbassadorApplication.status == AmbassadorApplication.Status.CREATED,
        ).first()
        if nor_amb_audit_row:
            raise InvalidArgument(f"{user_id}已存在待审核的普通大使申请记录")
        normal_amb = Ambassador.query.filter(
            Ambassador.user_id == user_id,
            Ambassador.status == Ambassador.Status.VALID,
        ).first()
        if normal_amb:
            raise InvalidArgument(f"用户{user_id}存在生效中的普通大使，不允许设置为代理")
        bus_user_id = param.get("bus_user_id")
        if bus_user_id:
            if bus_user_id == user_id:
                raise InvalidArgument(f"代理和绑定商务不能是同一个人")
            bus_user = BusinessUser.query.filter(
                BusinessUser.user_id == bus_user_id,
                BusinessUser.status == BusinessUser.Status.VALID,
            ).first()
            if not bus_user:
                raise InvalidArgument(f"绑定的商务用户{bus_user_id}不是有效的商务")
        amb_type = param.get("type")
        if amb_type == BusinessAmbassador.Type.BROKER.name:
            if not Broker.query.filter(
                Broker.status == Broker.Status.VALID,
                Broker.user_id == user_id,
            ).first():
                raise InvalidArgument(f"该用户{user_id}不是Broker")
        if TreeAmbHelper.get_ambassador(user_id, need_valid=True):
            raise InvalidArgument(f"用户{user_id}存在生效中的子代理，不允许设置为代理")
        if amb_type == BusinessAmbassador.Type.TREE_ROOT.name:
            if not TreeAmbHelper.can_become_root_tree_amb(user_id):
                raise InvalidArgument(f"检查失败，{user_id}不允许成为总代理")
        #
        share_users = param["share_users"]  # default empty list
        cls.check_share_users(user_id, param["rate"], share_users)

    @classmethod
    def do_add_bus_amb(cls, param: dict):
        """ 增加一个商务大使，审核通过时调用 """
        cls.check_add_bus_amb_param(param)
        model = cls.model
        user_id = param["user_id"]

        # 确定 source
        nor_amb_row = Ambassador.query.filter(Ambassador.user_id == user_id).first()
        if not nor_amb_row:
            source = BusinessAmbassador.Source.BUSINESS_EXPANSION
        else:
            if nor_amb_row.source == Ambassador.Source.BUSINESS:
                source = BusinessAmbassador.Source.BUSINESS_EXPANSION
            else:
                source = BusinessAmbassador.Source.NORMAL_AMB_RECALL

        bus_amb: model = model.get_or_create(user_id=user_id)
        bus_amb.type = param["__amb_type"]
        if bus_amb.type == BusinessAmbassador.Type.BROKER:
            bus_amb.check_status = BusinessAmbassador.CheckStatus.CHECK_PASSED
        else:
            bus_amb.check_status = BusinessAmbassador.CheckStatus.NOT_CHECK
        bus_amb.source = source
        bus_amb.rate = param["rate"]
        bus_amb.team_rate = AddBusAmbAuditResource.DEFAULT_TEAM_RATE
        bus_amb.delay_repay_month = param.get('delay_repay_month', 0)
        now_ = param["__op_time"]
        bus_amb.effected_at = now_
        bus_user_id = param.get("bus_user_id")
        if bus_user_id:
            bus_amb.bus_user_id = bus_user_id
            bus_amb.bind_bus_at = now_
            bus_amb.bus_refer_start_at = now_.date()
        else:
            bus_amb.bus_user_id = None
            bus_amb.bind_bus_at = None
            bus_amb.bus_refer_start_at = None
        bus_amb.mobile_num = param.get("mobile_num") or ""
        bus_amb.remark = param.get("remark") or ""
        bus_amb.status = model.Status.VALID
        bus_amb.delete_at = None
        bus_amb.delete_type = None
        if bus_amb.type == BusinessAmbassador.Type.TREE_ROOT and param.get('allow_add_child'):
            bus_amb.allow_add_child = True
        else:
            bus_amb.allow_add_child = False
        db.session.add(bus_amb)

        share_users = param["share_users"]
        for share_user in share_users:
            share_row: BusinessAmbassadorShareUser = BusinessAmbassadorShareUser.get_or_create(
                bus_amb_id=user_id, target_user_id=share_user["target_user_id"],
            )
            share_row.rate = share_user["rate"]
            share_row.status = BusinessAmbassadorShareUser.Status.VALID
            share_row.effected_at = now_
            db.session.add(share_row)

        if bus_amb.type == BusinessAmbassador.Type.TREE_ROOT:
            tree_hie: TreeAmbassadorHierarchy = TreeAmbassadorHierarchy.get_or_create(user_id=user_id)
            tree_hie.child_info = []
            tree_hie.parent_info = []
            db.session.add(tree_hie)

        ReferralHistory.query.filter(
            ReferralHistory.referrer_id == user_id,
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).update(
            {ReferralHistory.referral_type: ReferralHistory.ReferralType.AMBASSADOR},
            synchronize_session=False
        )
        db.session.commit()
        send_bus_ambassador_train_book_email.delay(user_id)
        BusSnapshotHelper.add_snapshot(bus_amb, "ADD", now_, share_users=share_users)
        return {}

    @classmethod
    def check_edit_amb_status_param(cls, param: dict) -> model:
        """ 检查修改商务大使状态的参数 """
        user_id = param["user_id"]
        model = cls.model
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在")
        new_status = param["status"]
        if param["status"] not in [i.name for i in model.Status] and param["status"] not in model.Status:
            raise InvalidArgument(f"状态{new_status}不存在")
        is_del = new_status in [model.Status.DELETED, model.Status.DELETED.name]
        if is_del and BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).first():
            raise InvalidArgument(f"商务大使{user_id}还有关联的代理，请解除关系后再关闭")
        if is_del and BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.target_user_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).first():
            raise InvalidArgument(f"{user_id}作为商务代理还有关联的商务大使，请解除关系后再关闭")
        if not is_del and Ambassador.query.filter(
            Ambassador.user_id == user_id,
            Ambassador.status == Ambassador.Status.VALID,
        ).first():
            raise InvalidArgument(f"用户{user_id}存在生效中的普通大使，不允许修改状态为正常")
        return bus_amb

    @classmethod
    def do_edit_amb_status(cls, param: dict):
        """用户-商务大使体系-编辑商务状态"""
        cls.check_edit_amb_status_param(param)
        model = cls.model

        user_id = param["user_id"]
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        new_status = model.Status[param["status"]]
        if bus_amb.status == new_status:
            return

        old_status = bus_amb.status
        bus_amb.status = new_status
        now_ = param["__op_time"]
        if new_status == model.Status.VALID:
            bus_amb.effected_at = now_
            bus_amb.bind_bus_at = now_
            bus_amb.bus_refer_start_at = next_month(now_.year, now_.month)
            ReferralHistory.query.filter(
                ReferralHistory.referrer_id == user_id,
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).update(
                {ReferralHistory.referral_type: ReferralHistory.ReferralType.AMBASSADOR},
                synchronize_session=False
            )
        else:
            db_share_rows = BusinessAmbassadorShareUser.query.filter(
                BusinessAmbassadorShareUser.bus_amb_id == user_id,
            ).all()
            for r in db_share_rows:
                r.status = BusinessAmbassadorShareUser.Status.DELETED
                db.session.add(r)
            # 失效后邀请关系变为普通邀请
            ReferralHistory.query.filter(
                ReferralHistory.referrer_id == user_id,
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).update(
                {ReferralHistory.referral_type: ReferralHistory.ReferralType.NORMAL},
                synchronize_session=False
            )
            bus_amb.delete_at = now()
            bus_amb.delete_type = model.DeleteType.MANUAL
        db.session.add(bus_amb)
        db.session.commit()
        old_data = {"status": old_status.name}
        op_type = "EDIT" if new_status == model.Status.VALID else "DEL"
        BusSnapshotHelper.add_snapshot(bus_amb, op_type, now_)
        return old_data

    @classmethod
    def check_edit_amb_type_param(cls, param: dict) -> model:
        new_type = BusinessAmbassador.Type[param["type"]]
        new_type_val = new_type.value
        model = cls.model
        user_id = param["user_id"]
        bus_amb: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        if not bus_amb.is_normal_amb:
            raise InvalidArgument(f"代理{user_id}不是普通代理")
        if BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).first():
            raise InvalidArgument(f"{user_id}还有关联的代理，不允许成为{new_type_val}，请解除关系")
        if new_type == BusinessAmbassador.Type.TREE_ROOT:
            if not TreeAmbHelper.can_become_root_tree_amb(user_id):
                raise InvalidArgument(f"检查失败，{user_id}不允许成为总代理")
        if new_type == BusinessAmbassador.Type.BROKER:
            if not Broker.query.filter(
                Broker.status == Broker.Status.VALID,
                Broker.user_id == user_id,
            ).first():
                raise InvalidArgument(f"该用户{user_id}不是Broker")
        return bus_amb

    @classmethod
    def check_and_get_root_tree_amb(cls, user_id: int) -> model:
        model = cls.model
        bus_amb: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        if bus_amb.type != model.Type.TREE_ROOT:
            raise InvalidArgument(f"代理{user_id}类型不是总代理")
        return bus_amb

    @classmethod
    def do_edit_amb_allow_add_child(cls, param: dict):
        """ 修改商务代理的是否允许拓展子代理 """
        user_id = param['user_id']
        allow_add_child = param['allow_add_child']
        bus_amb = cls.check_and_get_root_tree_amb(user_id)

        old_allow_add_child = bus_amb.allow_add_child
        if allow_add_child != bus_amb.allow_add_child:
            bus_amb.allow_add_child = allow_add_child
            db.session.add(bus_amb)
            TreeAmbHelper.edit_allow_add_child(user_id=user_id, new_allow_add_child=allow_add_child)

        return {"allow_add_child": old_allow_add_child}

    @classmethod
    def do_edit_amb_type(cls, param: dict):
        """ 修改商务代理的类型 """
        bus_amb = cls.check_edit_amb_type_param(param)

        new_type = cls.model.Type[param["type"]]
        old_type = bus_amb.type
        if new_type == cls.model.Type.TREE_ROOT:
            bus_amb.type = cls.model.Type.TREE_ROOT
            bus_amb.allow_add_child = True
        elif new_type == cls.model.Type.BROKER:
            bus_amb.type = cls.model.Type.BROKER
        db.session.add(bus_amb)
        return {"type": old_type}

    @classmethod
    def check_edit_rate_param(cls, param: dict) -> model:
        """ 检查修改返佣比例的参数 """
        user_id = param["user_id"]
        model = cls.model
        bus_amb: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        if bus_amb.is_tree_root_amb:
            max_child_rate = TreeAmbHelper.get_max_child_rate(user_id)
            if not (0 <= max_child_rate <= Decimal(param["rate"])):
                raise InvalidArgument(message=f"返佣比例不可设置小于子代理的返佣比例{amount_to_str(max_child_rate)}")

        share_users = param["share_users"]
        cls.check_share_users(user_id, param["rate"], share_users)
        return bus_amb

    @classmethod
    def do_edit_rate(cls, param: dict):
        """用户-商务大使体系-编辑商务状态"""
        cls.check_edit_rate_param(param)
        model = cls.model

        user_id = param["user_id"]
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        old_rate = bus_amb.rate
        bus_amb.rate = param["rate"]
        db.session.add(bus_amb)

        now_ = param["__op_time"]
        share_users = param["share_users"]
        db_share_rows = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()
        old_share_users = [
            {"target_user_id": i.target_user_id, "rate": i.rate}
            for i in db_share_rows
        ]
        for r in db_share_rows:
            r.status = BusinessAmbassadorShareUser.Status.DELETED
            db.session.add(r)
        for share_user in share_users:
            share_row: BusinessAmbassadorShareUser = BusinessAmbassadorShareUser.get_or_create(
                bus_amb_id=user_id, target_user_id=share_user["target_user_id"],
            )
            share_row.rate = share_user["rate"]
            share_row.status = BusinessAmbassadorShareUser.Status.VALID
            share_row.effected_at = now_
            db.session.add(share_row)
        db.session.commit()
        old_data = {"rate": old_rate, "share_users": old_share_users}
        BusSnapshotHelper.add_snapshot(bus_amb, "EDIT", now_, share_users=share_users)
        return old_data

    @classmethod
    def check_edit_bus_user_param(cls, param: dict) -> model:
        """ 检查修改绑定商务的参数 """
        user_id = param["user_id"]
        model = cls.model
        bus_amb: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(f"代理{user_id}不存在或状态关闭")
        bus_user_id = param.get("bus_user_id")
        if bus_user_id:
            if bus_user_id == bus_amb.bus_user_id:
                raise InvalidArgument(f"修改前后绑定的商务用户相同{bus_user_id}")
            bus_user = BusinessUser.query.filter(
                BusinessUser.user_id == bus_user_id,
                BusinessUser.status == BusinessUser.Status.VALID,
            ).first()
            if not bus_user:
                raise InvalidArgument(f"新绑定的商务用户{bus_user_id}不是有效的商务")
        return bus_amb

    @classmethod
    def do_edit_bus_user(cls, param: dict):
        """ 检查修改绑定商务的参数 """
        cls.check_edit_bus_user_param(param)
        model = cls.model
        user_id = param["user_id"]
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        bus_user_id = param.get("bus_user_id")
        now_ = param["__op_time"]
        old_bus_user_id = bus_amb.bus_user_id
        if bus_user_id:
            bus_amb.bus_user_id = bus_user_id
            bus_amb.bind_bus_at = now_
            bus_amb.bus_refer_start_at = next_month(now_.year, now_.month)
        else:
            bus_amb.bus_user_id = None
            bus_amb.bind_bus_at = None
            bus_amb.bus_refer_start_at = None
        db.session.add(bus_amb)
        BusSnapshotHelper.add_snapshot(bus_amb, "EDIT", now_)
        return {"bus_user_id": old_bus_user_id}

    @classmethod
    def do_edit_delay_repay_month(cls, param: dict):
        """ 修改预付金还款时间的参数 """
        model = cls.model
        user_id = param["user_id"]
        delay_repay_month = param["delay_repay_month"]
        now_ = param["__op_time"]
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        old_delay_repay_month = bus_amb.delay_repay_month
        bus_amb.delay_repay_month = delay_repay_month
        db.session.add(bus_amb)
        BusSnapshotHelper.add_snapshot(bus_amb, "EDIT", now_)
        return {"delay_repay_month": old_delay_repay_month}

    @classmethod
    def do_restore_invalid_referral(cls, param: dict):
        """ 恢复失效邀请关系 """
        user_id = param["user_id"]
        ReferralHistory.query.filter(
            ReferralHistory.referrer_id == user_id,
        ).update(
            {
                ReferralHistory.status: ReferralHistory.Status.VALID,
                ReferralHistory.referral_type: ReferralHistory.ReferralType.AMBASSADOR,
            },
            synchronize_session=False
        )
        db.session.commit()


@ns.route('/bus-ambassadors')
@respond_with_code
class BusAmbassadorsResource(Resource):
    model = BusinessAmbassador

    @classmethod
    def get_visible_bus_users(cls, user_id: int, team_id: int = None) -> list[BusinessUser]:
        # 商务在列表里默认能看到自己邀请的大使，默认能新增大使，能看到自己邀请大使的详情并对自己邀请大使相关内容进行编辑
        # 组长和PR在列表默认能看到本组商务邀请的大使，默认能新增大使，能看到本组商务邀请大使的详情并对本组商务邀请大使相关内容进行编辑
        # 可以又是组长又是PR又是商务
        model = BusinessUser
        bus_users = []
        leader_own_teams = BusTeamsResource.get_teams_by_leader(user_id)
        query = model.query
        if team_id:
            query = model.query.filter(
                model.team_id == team_id
            )
        if leader_own_teams:
            # 是组长
            bus_users = model.query.filter(
                model.team_id.in_([i.id for i in leader_own_teams]),
                # model.status == model.Status.VALID,
            ).order_by(model.id.desc()).all()
        # 判断是普通商务
        cur_bus_user = query.filter(
            model.user_id == user_id
        ).first()
        if cur_bus_user and user_id not in {i.user_id for i in bus_users}:
            bus_users.append(cur_bus_user)
        return bus_users

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "is_pending_delete", Language.ZH_HANS_CN: "是否预淘汰"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "source", Language.ZH_HANS_CN: "来源"},
        {"field": "total_ref_rate", Language.ZH_HANS_CN: "总返佣比例"},
        {"field": "rate", Language.ZH_HANS_CN: "代理返佣比例"},
        {"field": "share_ref_rate", Language.ZH_HANS_CN: "商务代理返佣比例"},
        {"field": "team_rate", Language.ZH_HANS_CN: "团队返佣系数"},
        {"field": "delay_repay_month", Language.ZH_HANS_CN: "预付金还款时间"},
        {"field": "bus_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "team_name", Language.ZH_HANS_CN: "所属团队"},
        {"field": "last_team_name", Language.ZH_HANS_CN: "之前归属团队"},
        {"field": "effected_at", Language.ZH_HANS_CN: "本次成为代理时间"},
        {"field": "bind_bus_at", Language.ZH_HANS_CN: "绑定商务时间"},
        {"field": "location_name", Language.ZH_HANS_CN: "地区"},
        {"field": "mobile_num", Language.ZH_HANS_CN: "手机号"},
        {"field": "refer_code", Language.ZH_HANS_CN: "邀请码"},
        {"field": "effect_refer_count", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "amb_refer_count", Language.ZH_HANS_CN: "refer人数（历史在职+本次在职）"},
        {"field": "amb_refer_active_user_count", Language.ZH_HANS_CN: "refer活跃人数（历史在职+本次在职）"},
        {"field": "amb_refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产（历史在职+本次在职）"},
        {"field": "amb_refer_trade_count", Language.ZH_HANS_CN: "refer交易人数（历史在职+本次在职）"},
        {"field": "this_month_deal_amount", Language.ZH_HANS_CN: "当月refer交易总额（USD）（历史在职+本次在职）"},
        {"field": "effect_refer_trade_amount", Language.ZH_HANS_CN: "refer交易总额（USD）（历史在职+本次在职）"},
        {"field": "effect_refer_spot_amount", Language.ZH_HANS_CN: "现货交易额（USD）（历史在职+本次在职）"},
        {"field": "effect_refer_perpetual_amount", Language.ZH_HANS_CN: "合约交易额（USD）（历史在职+本次在职）"},
        {"field": "total_refer_amount", Language.ZH_HANS_CN: "累计返佣（USDT）（历史在职+本次在职）"},
        {"field": "month_refer_amount", Language.ZH_HANS_CN: "当月返佣（USDT）（历史在职+本次在职）"},
        {"field": "new_refer_count", Language.ZH_HANS_CN: "refer人数（本次在职）"},
        {"field": "new_refer_active_user_count", Language.ZH_HANS_CN: "refer活跃人数（本次在职）"},
        {"field": "new_refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产（本次在职）"},
        {"field": "new_refer_trade_count", Language.ZH_HANS_CN: "refer交易人数（本次在职）"},
        {"field": "new_this_month_deal_amount", Language.ZH_HANS_CN: "当月refer交易总额（USD）（本次在职）"},
        {"field": "new_refer_trade_amount", Language.ZH_HANS_CN: "refer交易总额（USD）（本次在职）"},
        {"field": "new_refer_spot_amount", Language.ZH_HANS_CN: "现货交易额（USD）（本次在职）"},
        {"field": "new_refer_perpetual_amount", Language.ZH_HANS_CN: "合约交易额（USD）（本次在职）"},
        {"field": "new_total_refer_amount", Language.ZH_HANS_CN: "累计返佣（USDT）（本次在职）"},
        {"field": "new_month_refer_amount", Language.ZH_HANS_CN: "当月返佣（USDT）（本次在职）"},
        {"field": "delete_at", Language.ZH_HANS_CN: "关闭时间"},
        {"field": "delete_type", Language.ZH_HANS_CN: "关闭类型"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    STATUS_DICT = {
        model.Status.VALID.name: "正常",
        model.Status.DELETED.name: "关闭",
        "CREATED": "待初审",
        "FIRST_PASSED": "待复审",
        "REJECTED": "已拒绝",
    }

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(STATUS_DICT, missing="VALID"),
        user_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        type=EnumField(BusinessAmbassador.Type),
        source=EnumField(BusinessAmbassador.Source),
        check_status=EnumField(BusinessAmbassador.CheckStatus),
        delete_type=EnumField(BusinessAmbassador.DeleteType),
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
        team_id=wa_fields.Integer,
        last_team_id=wa_fields.Integer,
        location_code=wa_fields.String,
        sort_col=EnumField(["amb_refer_count",
                            "amb_refer_trade_count",
                            "this_month_deal_amount",
                            "effect_refer_trade_amount",
                            "effect_refer_spot_amount",
                            "effect_refer_perpetual_amount",
                            "total_refer_amount",
                            "month_refer_amount",
                            "new_refer_count",
                            "new_refer_trade_count",
                            "new_this_month_deal_amount",
                            "new_refer_trade_amount",
                            "new_refer_spot_amount",
                            "new_refer_perpetual_amount",
                            "new_total_refer_amount",
                            "new_month_refer_amount",
                            "amb_refer_active_user_count",
                            "amb_refer_user_balance_usd",
                            "new_refer_active_user_count",
                            "new_refer_user_balance_usd",
                            ]),
        sort_type=EnumField(["ascending", "descending"]),
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使列表"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        status = kwargs.get("status") or "VALID"
        page, limit = kwargs["page"], kwargs["limit"]

        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_AMB_LIST
        )
        visible_bus_users = cls.get_visible_bus_users(g.user.id, kwargs.get("team_id"))
        visible_bus_u_ids = [i.user_id for i in visible_bus_users]
        limit_visible = permission_limit and not is_super_user(g.user.id)

        is_export = kwargs.get('export')
        if status in [i.name for i in model.Status]:
            # 查商务大使表
            is_amb_table = True
            query = model.query.order_by(model.id.desc())
            if user_id := kwargs.get("user_id"):
                query = query.filter(model.user_id == user_id)
            if status := kwargs.get("status"):
                query = query.filter(model.status == status)
            if type_ := kwargs.get("type"):
                query = query.filter(model.type == type_)
            if delete_type := kwargs.get("delete_type"):
                query = query.filter(model.delete_type == delete_type)
            if source := kwargs.get("source"):
                query = query.filter(model.source == source)
            if check_status := kwargs.get("check_status"):
                if check_status == model.CheckStatus.CHECK_NOT_PASSED:
                    query = query.filter(model.check_status == model.CheckStatus.CHECK_NOT_PASSED)
                else:
                    query = query.filter(model.check_status != model.CheckStatus.CHECK_NOT_PASSED)
            if limit_visible:
                query = query.filter(model.bus_user_id.in_(visible_bus_u_ids))
            if bus_user_id := kwargs.get("bus_user_id"):
                query = query.filter(model.bus_user_id == bus_user_id)
            if last_team_id := kwargs.get("last_team_id"):
                query = query.filter(model.last_team_id == last_team_id)
            if team_id := kwargs.get("team_id"):
                b_model = BusinessUser
                b_rows = b_model.query.filter(
                    b_model.team_id == team_id,
                ).with_entities(b_model.user_id)
                bus_user_ids = [i.user_id for i in b_rows]
                query = query.filter(model.bus_user_id.in_(bus_user_ids))
            if location_code := kwargs.get("location_code"):
                query = query.join(User, User.id == model.user_id).filter(User.location_code == location_code)
            if is_export:
                _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
                records = _paginate.items
                total = _paginate.total
                amb_u_ids = [i.user_id for i in records]
                amb_shares_map = BusRelationUserQuerier.get_bus_ambassador_share_users_map(amb_u_ids)
                amb_share_rate_map = {k: sum([j.rate for j in v]) for k, v in amb_shares_map.items()}
            else:
                records = query.all()
                total = len(records)
                share_rows = BusinessAmbassadorShareUser.query.filter(
                    BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
                ).all()
                amb_share_rate_map = defaultdict(Decimal)
                for s in share_rows:
                    amb_share_rate_map[s.bus_amb_id] += s.rate
        else:
            # 查审核表
            is_amb_table = False
            bus_u_ids = visible_bus_u_ids if limit_visible else None
            if is_export:
                records, total = BusAmbAuditsResource.paginate_add_amb_audit_rows(
                    1, ADMIN_EXPORT_LIMIT, bus_u_ids, status
                )
            else:
                records, total = BusAmbAuditsResource.paginate_add_amb_audit_rows(page, limit, bus_u_ids, status)
            amb_share_rate_map = {}

        records: list[model]
        amb_ids = [item.user_id for item in records]
        user_rows = User.query.filter(
            User.id.in_(amb_ids)
        ).with_entities(User.id, User.email, User.location_code).all()
        amb_email_map = {i.id: i.email for i in user_rows}
        amb_loc_map = {i.id: i.location_code for i in user_rows}
        countries = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        bus_user_id = [item.bus_user_id for item in records if item.bus_user_id]
        bus_user_name_map = get_admin_user_name_map(bus_user_id)
        referral_rows = Referral.query.filter(
            Referral.user_id.in_(amb_ids),
            Referral.is_default.is_(True),
        ).with_entities(
            Referral.user_id,
            Referral.code
        ).all()
        refer_code_map = {i.user_id: i.code for i in referral_rows}

        bus_row = BusinessUser.query.filter(
            BusinessUser.user_id.in_(bus_user_id)
        ).with_entities(BusinessUser.user_id, BusinessUser.team_id)
        team_id_map = {i.user_id: i.team_id for i in bus_row}
        team_row = BusinessTeam.query.filter().with_entities(BusinessTeam.id, BusinessTeam.name)
        team_name_map = {i.id: i.name for i in team_row}
        bus_user_team_map = {k: team_name_map[v] for k, v in team_id_map.items()}

        # 商务大使的统计信息
        if is_amb_table:
            bus_amb_statics_map = {
                i.user_id: i for i in BusinessAmbassadorStatistics.query.filter(
                    BusinessAmbassadorStatistics.user_id.in_(amb_ids)
                ).all()
            }
        else:
            bus_amb_statics_map = {}

        res = []
        monthly_report_map = cls.get_monthly_report_map(amb_ids, include_bro_amb_data=False)
        effect_monthly_report_map = cls.get_effect_monthly_report_map(records)
        for item in records:
            if item.user_id not in monthly_report_map:
                monthly_report_map[item.user_id] = defaultdict(Decimal)
            if item.user_id not in effect_monthly_report_map:
                effect_monthly_report_map[item.user_id] = defaultdict(Decimal)
            d = item.to_dict(enum_to_name=True)
            if not is_amb_table:
                new_data: dict = json.loads(item.new_data)
                d["rate"] = Decimal(new_data.get("rate", 0))
                d["allow_add_child"] = bool(new_data.get("allow_add_child", False))
                _share_users = new_data.get("share_users", [])
                d["share_ref_rate"] = sum([Decimal(i['rate']) for i in _share_users])
                d["team_rate"] = Decimal(new_data.get("team_rate", 0))
                d["mobile_num"] = new_data.get("mobile_num", "")
                none_keys = ["effected_at", "bus_user_id", "bind_bus_at"]
                for k in none_keys:
                    if k not in d:
                        d[k] = None
            else:
                d["share_ref_rate"] = amb_share_rate_map.get(item.user_id, Decimal(0))
                d["allow_add_child"] = bool(d["allow_add_child"])

            d["total_ref_rate"] = d["share_ref_rate"] + d["rate"]
            d["email"] = amb_email_map.get(item.user_id, "")
            location_code = amb_loc_map.get(item.user_id, "")
            d["location_code"] = location_code
            d["location_name"] = countries.get(location_code, "")
            d["bus_name"] = bus_user_name_map.get(item.bus_user_id, "")
            d["team_name"] = bus_user_team_map.get(item.bus_user_id, "")
            if is_amb_table:
                d["last_team_name"] = team_name_map.get(item.last_team_id, "")
            else:
                d["last_team_name"] = ""
            statics: BusinessAmbassadorStatistics = bus_amb_statics_map.get(item.user_id)
            d["effect_refer_count"] = statics.effect_refer_count if statics else 0
            d["effect_refer_trade_count"] = statics.effect_refer_trade_count if statics else 0
            d["effect_refer_trade_amount"] = statics.effect_refer_trade_amount if statics else 0
            d["effect_refer_spot_amount"] = statics.effect_refer_spot_amount if statics else 0
            d["effect_refer_perpetual_amount"] = statics.effect_refer_perpetual_amount if statics else 0
            d["total_refer_amount"] = statics.total_refer_amount + statics.total_indirect_refer_amount if statics else 0
            d["month_refer_amount"] = statics.month_refer_amount + statics.month_indirect_refer_amount if statics else 0
            d["amb_refer_count"] = statics.amb_refer_count if statics else 0
            d["amb_refer_trade_count"] = statics.amb_refer_trade_count if statics else 0
            d["new_refer_count"] = statics.new_refer_count if statics else 0
            d["amb_refer_user_balance_usd"] = statics.amb_refer_user_balance_usd if statics else 0
            d["new_refer_user_balance_usd"] = statics.new_refer_user_balance_usd if statics else 0
            d["amb_refer_active_user_count"] = statics.amb_refer_active_user_count if statics else 0
            d["new_refer_active_user_count"] = statics.new_refer_active_user_count if statics else 0
            d["new_refer_trade_count"] = statics.new_refer_trade_count if statics else 0
            d["new_refer_trade_amount"] = statics.new_refer_trade_amount if statics else 0
            d["new_refer_spot_amount"] = statics.new_refer_spot_amount if statics else 0
            d["new_refer_perpetual_amount"] = statics.new_refer_perpetual_amount if statics else 0
            d["new_total_refer_amount"] = statics.new_total_refer_amount + statics.new_total_indirect_refer_amount if statics else 0
            d["new_month_refer_amount"] = statics.new_month_refer_amount + statics.new_month_indirect_refer_amount if statics else 0
            d["new_refer_month_trade_amount"] = statics.new_refer_month_trade_amount if statics else 0
            d["new_this_month_deal_amount"] = effect_monthly_report_map[item.user_id]["month_deal_amount"]
            d["this_month_deal_amount"] = monthly_report_map[item.user_id]["month_deal_amount"]
            d["refer_code"] = refer_code_map.get(item.user_id, "")
            res.append(d)

        if (sort_col := kwargs.get("sort_col")) and (sort_type := kwargs.get("sort_type")):
            res = sorted(res, key=lambda x: cls._sort(x, sort_col, sort_type))

        if is_export:
            for d in res:
                d["rate"] = f'{amount_to_str(d["rate"] * 100, 4)}%'
                d["share_ref_rate"] = f'{amount_to_str(d["share_ref_rate"] * 100, 4)}%'
                d["total_ref_rate"] = f'{amount_to_str(d["total_ref_rate"] * 100, 4)}%'
                d["team_rate"] = f'{amount_to_str(d["team_rate"] * 100, 4)}%'
                d["effected_at"] = d["effected_at"].strftime("%Y-%m-%d %H:%M:%S") if d["effected_at"] else ""
                d["bind_bus_at"] = d["bind_bus_at"].strftime("%Y-%m-%d %H:%M:%S") if d["bind_bus_at"] else ""
                d["status"] = cls.STATUS_DICT[status]
                d["type"] = model.Type[d["type"]].value
                d["source"] = model.Source[d["source"]].value
                d["is_pending_delete"] = "是" if d["check_status"] == model.CheckStatus.CHECK_NOT_PASSED.name else "否"
                d["delete_at"] = d["delete_at"].strftime("%Y-%m-%d %H:%M:%S") if d["delete_at"] else ""
                d["delete_type"] = model.DeleteType[d["delete_type"]].value if d["delete_type"] else "-"

            return export_xlsx(
                filename='business_ambassador_list',
                data_list=res,
                export_headers=cls.export_headers,
            )

        res = res[(page - 1) * limit: page * limit]
        if limit_visible:
            all_bus_name_dict = get_admin_user_name_map(visible_bus_u_ids)
        else:
            all_bus_name_dict = BusUsersResource.get_all_bus_name_map()

        return dict(
            total=total,
            items=res,
            extra=dict(
                status_dict=cls.STATUS_DICT,
                check_status_dict=BusinessAmbassador.CheckStatus,
                type_dict=BusinessAmbassador.Type,
                source_dict=BusinessAmbassador.Source,
                delete_type_dict=BusinessAmbassador.DeleteType,
                all_bus_name_dict=all_bus_name_dict,
                team_name_map=team_name_map,
                country_map=countries
            ),
        )

    @classmethod
    def get_monthly_report_map(cls, amb_ids, report_date=None, include_bro_amb_data=True):
        user_ids = list(amb_ids)
        today = datetime.today()
        cur_month = today.date().replace(day=1)
        report_date = report_date or cur_month
        model = MonthlyBusinessAmbassadorReferralReport
        rows = model.query.filter(
            model.user_id.in_(user_ids),
            model.report_date == report_date,
        ).with_entities(
            model.user_id,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
            model.bro_spot_trade_usd,
            model.bro_perpetual_trade_usd,
        ).all()

        monthly_report_map = defaultdict(lambda: defaultdict(Decimal))
        for row in rows:
            if include_bro_amb_data:
                deal_amount = row.spot_trade_usd + row.perpetual_trade_usd + row.bro_spot_trade_usd + row.bro_perpetual_trade_usd
            else:
                deal_amount = row.spot_trade_usd + row.perpetual_trade_usd
            monthly_report_map[row.user_id]['month_deal_amount'] += deal_amount
        return monthly_report_map

    @classmethod
    def get_effect_monthly_report_map(cls, amb_rows: list[BusinessAmbassador], report_date=None):
        """本次在职数据-当月refer交易总额，从日报取"""
        amb_effect_dt_map = {i.user_id: i.effected_at.date() for i in amb_rows}
        user_ids = list(amb_effect_dt_map)
        today_ = datetime.today()
        cur_month = today_.date().replace(day=1)
        report_date = report_date or cur_month
        end_dt = next_month(report_date.year, report_date.month)
        model = DailyBusinessAmbassadorReferralReport
        rows = model.query.filter(
            model.user_id.in_(user_ids),
            model.report_date >= report_date,
            model.report_date < end_dt,
        ).with_entities(
            model.user_id,
            model.report_date,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
        ).all()

        monthly_report_data_map = defaultdict(lambda: defaultdict(Decimal))
        for row in rows:
            eff_dt = amb_effect_dt_map[row.user_id]
            if eff_dt > row.report_date:
                continue
            deal_amount = row.spot_trade_usd + row.perpetual_trade_usd
            monthly_report_data_map[row.user_id]['month_deal_amount'] += deal_amount
        return monthly_report_data_map

    @staticmethod
    def _sort(item, sort_col, sort_type):
        v = item[sort_col]
        if sort_type == 'ascending':
            return v
        else:
            return -v


@ns.route('/bus-ambassadors/template')
@respond_with_code
class BusAmbassadorsTemplateResource(Resource):
    export_headers = (
        {'field': 'user', Language.ZH_HANS_CN: '大使'},
        {'field': 'biz_user', Language.ZH_HANS_CN: '所属商务'},
        {'field': 'rate', Language.ZH_HANS_CN: '大使返佣比例'},
        {'field': 'agent1', Language.ZH_HANS_CN: '关联代理1'},
        {'field': 'agent1_rate', Language.ZH_HANS_CN: '代理1返佣比例'},
        {'field': 'agent2', Language.ZH_HANS_CN: '关联代理2'},
        {'field': 'agent2_rate', Language.ZH_HANS_CN: '代理2返佣比例'},
        {'field': 'agent3', Language.ZH_HANS_CN: '关联代理3'},
        {'field': 'agent3_rate', Language.ZH_HANS_CN: '代理3返佣比例'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'},
    )

    @classmethod
    def get(cls):
        """用户-商务大使体系-商务大使列表-模板下载"""
        return export_xlsx(
            filename='batch-template',
            data_list=[],
            export_headers=cls.export_headers
        )

    @classmethod
    def post(cls):
        """用户-商务大使体系-商务大使列表-模板上传"""
        file_ = request.files.get('batch-upload')
        file_columns = [column['field'] for column in cls.export_headers]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        for row in rows:
            user_id = cls._get_user_id(row['user'])
            remark = row['remark']
            if not user_id:
                raise InvalidArgument(message='大使不存在！')
            if row['biz_user']:
                biz_uid = cls._get_user_id(row['biz_user'])
                if not biz_uid:
                    raise InvalidArgument(message='所属商务不存在！')
                EditBusAmbBusUserAuditResource.do_business(
                    user_id=user_id,
                    bus_user_id=biz_uid,
                    remark=remark,
                    file_keys=[],
                )
            if row['rate']:
                rate = Decimal(row['rate'])
                share_users = []
                for agent, agent_rate in [
                    ('agent1', 'agent1_rate'),
                    ('agent2', 'agent2_rate'),
                    ('agent3', 'agent3_rate'),
                ]:
                    if not row[agent]:
                        continue
                    agent_uid = cls._get_user_id(row[agent])
                    if not agent_uid:
                        raise InvalidArgument(message='关联代理不存在！')
                    share_users.append({
                        'target_user_id': agent_uid,
                        'rate': row[agent_rate] or Decimal('0'),
                    })
                EditBusAmbRateAuditResource.do_business(
                    user_id=user_id,
                    rate=rate,
                    share_users=share_users,
                    remark=remark,
                    file_keys=[],
                )

    @classmethod
    def _get_user_id(cls, email: str) -> int | None:
        if not email:
            return
        query = User.query.with_entities(
            User.id,
        )
        row = query.filter(
            User.email == email.lower()
        ).first()
        return row.id if row else None


@ns.route('/bus-ambassadors/export')
@respond_with_code
class BusAmbassadorsExportResource(Resource):
    model = BusinessAmbassador

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(BusAmbassadorsResource.STATUS_DICT, missing="VALID"),
        user_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使列表-导出"""
        kwargs["export"] = True
        return BusAmbassadorsResource.get_(**kwargs)


@ns.route('/bus-ambassadors/team-rate')
@respond_with_code
class BusAmbassadorTeamRateResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            team_rate=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN,
                                           required=True, allow_zero=True),
            remark=wa_fields.String(allow_none=True),
            file_keys=wa_fields.List(wa_fields.String(), validate=lambda x: len(x) <= 5, missing=[])
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-修改团队返佣系数"""
        model = BusinessAmbassador
        user_id = kwargs["user_id"]
        new_team_rate = kwargs["team_rate"]
        if not (0 <= new_team_rate <= 1):
            raise InvalidArgument(message=f"团队返佣系数不在0～1之间")

        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        old_data = bus_amb.to_dict(enum_to_name=True)
        bus_amb.team_rate = new_team_rate
        db.session.add(bus_amb)
        db.session.commit()

        remark = kwargs.get("remark") or ""
        BusSnapshotHelper.add_snapshot(bus_amb, "EDIT", now(), operate_remark=remark)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorTeam,
            old_data=old_data,
            new_data=bus_amb.to_dict(enum_to_name=True),
            target_user_id=bus_amb.user_id,
        )
        return {}


@ns.route('/bus-ambassadors/remark')
@respond_with_code
class BusAmbassadorRemarkResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使详情-新增备注"""
        model = BusinessAmbassador
        user_id = kwargs["user_id"]

        remark = kwargs.get("remark") or ""
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        old_data = bus_amb.to_dict(enum_to_name=True)
        bus_amb.remark = remark
        db.session.add(bus_amb)
        db.session.commit()

        BusSnapshotHelper.add_snapshot(bus_amb, "EDIT_REMARK", now(), operate_remark=remark)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorUser,
            old_data=old_data,
            new_data=bus_amb.to_dict(enum_to_name=True),
            target_user_id=bus_amb.user_id,
        )
        return {}


@ns.route('/bus-ambassadors/detail')
@respond_with_code
class BusAmbassadorDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            remark_page=PageField,
            remark_limit=LimitField,
            audit_page=PageField,
            audit_limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使详情"""
        model = BusinessAmbassador
        user_id = kwargs["user_id"]
        bus_amb: model = model.query.filter(model.user_id == user_id).first()
        if not bus_amb:
            raise InvalidArgument(message=f"代理{user_id}不存在")
        admin_uid = g.user.id
        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_AMB_LIST
        )
        limit_visible = permission_limit and not is_super_user(admin_uid)
        if limit_visible:
            visible_bus_users = BusAmbassadorsResource.get_visible_bus_users(admin_uid)
            visible_bus_u_ids = [i.user_id for i in visible_bus_users]
            if visible_bus_u_ids and bus_amb.bus_user_id not in visible_bus_u_ids:
                raise InvalidArgument(message=f"代理{user_id}不属于你的下级")

        share_users = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()
        share_for_me = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.target_user_id == user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()

        amb_remarks, remark_total = BusSnapshotHelper.get_amb_remarks(
            bus_amb.user_id, page=kwargs["remark_page"], limit=kwargs["remark_limit"],
        )
        amb_audits, audit_total = BusAmbAuditsResource.paginate_amb_audit_rows(
            page=kwargs["audit_page"], limit=kwargs["audit_limit"], bus_amb_id=user_id,
        )

        q_user_ids = {user_id}
        if bus_amb.bus_user_id:
            q_user_ids.add(bus_amb.bus_user_id)
        q_user_ids.update([i.target_user_id for i in share_users])
        q_user_ids.update([i.bus_amb_id for i in share_for_me])
        q_user_ids.update([i["operator"] for i in amb_remarks])
        q_user_ids.update([i["auditor"] for i in amb_audits if i["auditor"]])
        user_name_map = get_admin_user_name_map(q_user_ids)

        for rmk in amb_remarks:
            rmk["operator_name"] = user_name_map.get(rmk["operator"], "")
        for ad in amb_audits:
            ad["auditor_name"] = user_name_map.get(ad["auditor"], "")

        amb_email = user_name_map.get(bus_amb.user_id, "")
        total_ref_rate = bus_amb.rate + sum([i.rate for i in share_users])
        bus_per = quantize_amount(bus_amb.rate / total_ref_rate, 8) if total_ref_rate else Decimal(0)
        share_details = [
            dict(role="代理", rate=bus_amb.rate, percent=bus_per, user_id=bus_amb.user_id, name=amb_email)
        ]
        share_details.extend([
            dict(
                role="关联商务代理",
                rate=i.rate,
                percent=quantize_amount(i.rate / total_ref_rate, 8) if total_ref_rate else Decimal(0),
                user_id=i.target_user_id,
                name=user_name_map.get(i.target_user_id, ""),
            )
            for i in share_users
        ])
        bus_share_details = []
        if bus_amb.bus_user_id:
            bus_user = BusinessUser.query.filter(BusinessUser.user_id == bus_amb.bus_user_id).first()
            team = BusinessTeam.query.get(bus_user.team_id)
            leader_name = get_admin_user_name_map([bus_user.team_id]).get(bus_user.team_id, "")
            remain_rate = 1 - total_ref_rate
            bus_user_rate = remain_rate * bus_amb.team_rate * (1 - bus_user.leader_rate)
            leader_rate = remain_rate * bus_amb.team_rate * bus_user.leader_rate
            bus_share_details.append(
                dict(
                    role="商务",
                    rate=bus_user_rate,
                    user_id=bus_amb.bus_user_id,
                    name=user_name_map.get(bus_amb.bus_user_id, ""),
                )
            )
            bus_share_details.append(
                dict(
                    role="商务组长",
                    rate=leader_rate,
                    user_id=team.leader_id,
                    name=leader_name,
                )
            )

        recommend_details = [
            dict(
                user_id=i.bus_amb_id,
                rate=i.rate,
                effected_at=i.effected_at,
                name=user_name_map.get(i.bus_amb_id, ""),
            )
            for i in share_for_me
        ]

        info = bus_amb.to_dict(enum_to_name=True)
        info["allow_add_child"] = bool(info["allow_add_child"])
        info["email"] = amb_email
        info["total_ref_rate"] = total_ref_rate
        info["share_users"] = [dict(target_user_id=i.target_user_id, rate=i.rate) for i in share_users]
        info["bus_name"] = user_name_map.get(bus_amb.bus_user_id, "")
        return dict(
            info=info,
            share_details=share_details,
            bus_share_details=bus_share_details,
            recommend_details=recommend_details,
            remark_items=amb_remarks,
            remark_total=remark_total,
            audit_items=amb_audits,
            audit_total=audit_total,
            extra=dict(
                status_dict=model.Status,
                type_dict=model.Type,
                audit_status_dict=BusinessAmbassadorAudit.Status,
                audit_type_dict=BusinessAmbassadorAudit.Type,
            ),
        )


@ns.route('/loan-apply')
@respond_with_code
class BusAmbLoanApplyResource(Resource):
    model = BusinessAmbassadorLoanApply

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "申请ID"},
        {"field": "loan_id", Language.ZH_HANS_CN: "借币订单ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "提交时间"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "amount", Language.ZH_HANS_CN: "借款金额（USDT）"},
        {"field": "unflat_amount", Language.ZH_HANS_CN: "借款金额（USDT）"},
        {"field": "bus_amb_id", Language.ZH_HANS_CN: "商务大使ID"},
        {"field": "bus_amb_name", Language.ZH_HANS_CN: "商务大使邮箱"},
        {"field": "bus_user_id", Language.ZH_HANS_CN: "商务ID"},
        {"field": "bus_user_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "creator_name", Language.ZH_HANS_CN: "提交人"},
        {"field": "auditor_name", Language.ZH_HANS_CN: "审核人"},
        {"field": "admin_status", Language.ZH_HANS_CN: "审核状态"},
        {"field": "reason", Language.ZH_HANS_CN: "申请理由"},
    )

    STATUS_DICT = {
        BusinessAmbassadorLoanApply.Status.CREATED.name: "待审核",
        BusinessAmbassadorLoanApply.Status.REJECTED.name: "已拒绝",
        BusinessAmbassadorLoan.Status.ARREARS.name: "欠款中",
        BusinessAmbassadorLoan.Status.FINISHED.name: "已还清",
    }

    @classmethod
    def get_loanable_bus_amb(cls):
        model = BusinessAmbassador
        rows = model.query.filter(
            model.status == model.Status.VALID,
            model.bus_user_id.is_not(None),
        ).with_entities(
            model.user_id,
            model.bus_user_id,
        ).all()
        q_user_ids = {i.user_id for i in rows}
        q_user_ids.update({i.bus_user_id for i in rows})
        user_name_map = get_admin_user_name_map(q_user_ids)
        res = []
        for r in rows:
            res.append(
                {
                    "bus_amb_id": r.user_id,
                    "bus_amb_name": user_name_map.get(r.user_id),
                    "bus_user_id": r.bus_user_id,
                    "bus_user_name": user_name_map.get(r.bus_user_id),
                }
            )
        return res

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(STATUS_DICT),
        bus_amb_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        loan_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金申请列表"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        loan_model = BusinessAmbassadorLoan
        page, limit = kwargs["page"], kwargs["limit"]
        query = model.query.join(
            loan_model, model.id == loan_model.apply_id, isouter=True
        ).with_entities(
            model.id,
            model.created_at,
            model.updated_at,
            model.bus_amb_id,
            model.bus_user_id,
            model.asset,
            model.amount,
            model.creator,
            model.auditor,
            model.audited_at,
            model.reason,
            model.status,
            model.transfer_status,
            loan_model.id.label("loan_id"),
            loan_model.repay_amount,
            loan_model.status.label("loan_status"),
        ).order_by(model.id.desc())

        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_AMB_LOAN_LIST
        )
        visible_bus_users = BusAmbassadorsResource.get_visible_bus_users(g.user.id)
        visible_bus_u_ids = [i.user_id for i in visible_bus_users]
        limit_visible = permission_limit and not is_super_user(g.user.id)

        if limit_visible:
            query = query.filter(model.bus_user_id.in_(visible_bus_u_ids))
        if bus_amb_id := kwargs.get("bus_amb_id"):
            query = query.filter(model.bus_amb_id == bus_amb_id)
        if bus_user_id := kwargs.get("bus_user_id"):
            query = query.filter(model.bus_user_id == bus_user_id)
        if status := kwargs.get("status"):
            if status in ["CREATED", "REJECTED"]:
                query = query.filter(model.status == status)
            elif status in ["ARREARS", "FINISHED"]:
                query = query.filter(loan_model.status == status)
        if loan_id := kwargs.get("loan_id"):
            query = query.filter(loan_model.id == loan_id)

        is_export = kwargs.get('export', 0)
        if is_export:
            _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
            total = _paginate.total
            records = _paginate.items
        else:
            _paginate = query.paginate(page, limit, error_out=False)
            total = _paginate.total
            records = _paginate.items

        records: list[model]
        q_user_ids = {item.bus_amb_id for item in records}
        q_user_ids.update([item.bus_user_id for item in records])
        q_user_ids.update([item.creator for item in records])
        q_user_ids.update([item.auditor for item in records if item.auditor])
        user_name_map = get_admin_user_name_map(q_user_ids)

        fields = [
            'id', 'created_at', 'updated_at', 'bus_amb_id', 'bus_user_id',
            'asset', 'amount', 'creator', 'auditor', 'audited_at', 'reason',
            'status', 'transfer_status', 'admin_status'
            'loan_id', 'repay_amount', 'unflat_amount', 'loan_status',
        ]
        res = []
        for item in records:
            d = {k: getattr(item, k, "") for k in fields}
            for k in ["status", "transfer_status"]:
                d[k] = d[k].name if d[k] else d[k]  # enum name
            d["bus_amb_name"] = user_name_map.get(item.bus_amb_id, "")
            d["bus_user_name"] = user_name_map.get(item.bus_user_id, "")
            d["creator_name"] = user_name_map.get(item.creator, "")
            d["auditor_name"] = user_name_map.get(item.auditor, "")
            loan_id = item.loan_id or 0
            d["loan_id"] = loan_id
            d["repay_amount"] = item.repay_amount if loan_id else 0
            d["unflat_amount"] = item.amount - item.repay_amount if loan_id else 0
            if item.status == model.Status.FINISHED:
                d["admin_status"] = item.loan_status.value
            else:
                d["admin_status"] = item.status.value
            res.append(d)

        if is_export:
            for d in res:
                d["amount"] = f'{amount_to_str(d["amount"])}'
                d["unflat_amount"] = f'{amount_to_str(d["unflat_amount"])}'
                d["created_at"] = d["created_at"].strftime("%Y-%m-%d %H:%M:%S") if d["created_at"] else ""
                d["audited_at"] = d["audited_at"].strftime("%Y-%m-%d %H:%M:%S") if d["audited_at"] else ""

            return export_xlsx(
                filename='business_ambassador_loan_apply_list',
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
            extra=dict(
                status_dict=cls.STATUS_DICT,
                transfer_status_dict=model.TransferStatus,
                loanable_bus_amb_list=cls.get_loanable_bus_amb(),
            ),
        )

    @classmethod
    @ns.use_kwargs(dict(
        bus_amb_id=wa_fields.Integer(required=True),
        amount=PositiveDecimalField(places=PrecisionEnum.RATE_PLACES, rounding=ROUND_DOWN, required=True),
        reason=wa_fields.String(allow_none=True),
    ))
    def post(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金申请-新增"""
        asset = "USDT"
        bus_amb_id = kwargs["bus_amb_id"]
        bus_amb = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id == bus_amb_id,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).first()
        if not bus_amb:
            raise InvalidArgument(message=f"代理{bus_amb_id}不存在或无效")
        if not bus_amb.bus_user_id:
            raise InvalidArgument(message=f"代理{bus_amb_id}未绑定商务")

        admin_uid = g.user.id
        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_AMB_LOAN_LIST
        )
        if permission_limit and not is_super_user(admin_uid):
            visible_bus_users = BusAmbassadorsResource.get_visible_bus_users(admin_uid)
            visible_bus_u_ids = [i.user_id for i in visible_bus_users]
            if bus_amb.bus_user_id not in visible_bus_u_ids:
                raise InvalidArgument(message=f"该用户不是你名下的代理")

        bus_user = BusinessUser.query.filter(
            BusinessUser.user_id == bus_amb.bus_user_id,
            BusinessUser.status == BusinessUser.Status.VALID,
        ).first()
        if not bus_user:
            raise InvalidArgument(message=f"代理{bus_amb_id}绑定商务的{bus_amb.bus_user_id}无效")

        apply = BusinessAmbassadorLoanApply(
            bus_amb_id=bus_amb_id,
            bus_user_id=bus_amb.bus_user_id,
            asset=asset,
            amount=kwargs["amount"],
            creator=g.user.id,
            reason=kwargs.get("reason") or "",
        )
        db.session.add(apply)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorLoanApply,
            detail=kwargs,
            target_user_id=bus_amb_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        apply_id=wa_fields.Integer(required=True),
        status=EnumField([model.Status.REJECTED.name, model.Status.AUDITED.name], required=True),
    ))
    def patch(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金申请-审核"""
        model = cls.model
        row: model = model.query.get(kwargs["apply_id"])
        if row.status != model.Status.CREATED:
            raise InvalidArgument(message="状态不是待审核")

        new_status = kwargs["status"]
        is_audited = new_status == model.Status.AUDITED.name
        if is_audited:
            balances = ServerClient().get_user_balances(row.bus_user_id)
            bus_user_ba = Decimal(balances.get(row.asset, {}).get("available", "0"))
            if bus_user_ba < row.amount:
                raise InvalidArgument(message=f"商务{row.bus_user_id}可用余额{bus_user_ba}小于借币数{row.amount}")

        op_time = now()
        row.status = new_status
        row.auditor = g.user.id
        row.audited_at = op_time
        if is_audited:
            row.transfer_status = model.TransferStatus.CREATED
        db.session.add(row)
        db.session.commit()
        if is_audited:
            cls.do_apply_transfer(row)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorLoanApply,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.bus_amb_id,
        )

    @classmethod
    def do_apply_transfer(cls, row: model):
        client = ServerClient()
        try:
            client.batch_add_user_balance(
                [
                    dict(
                        user_id=row.bus_user_id,  # 扣商务
                        asset=row.asset,
                        business=BalanceBusiness.BUS_AMB_LOAN,
                        business_id=row.id,
                        amount=str(-row.amount),
                        detail={'remark': f'bus_amb loan_apply {row.id}'},
                        account_id=SPOT_ACCOUNT_ID,
                    ),
                    dict(
                        user_id=row.bus_amb_id,  # 加商务
                        asset=row.asset,
                        business=BalanceBusiness.BUS_AMB_LOAN,
                        business_id=row.id,
                        amount=str(row.amount),
                        detail={'remark': f'bus_amb loan_apply {row.id}'},
                        account_id=SPOT_ACCOUNT_ID,
                    ),
                ]
            )
        except Exception as e:
            current_app.logger.error(f"do_apply_transfer error {e!r}")
            raise InvalidArgument(f"申请{row.id} 划转资产失败：{e!r}")

        row.status = cls.model.Status.FINISHED
        row.transfer_status = cls.model.TransferStatus.FINISHED
        loan = BusinessAmbassadorLoan(
            apply_id=row.id,
            bus_amb_id=row.bus_amb_id,
            bus_user_id=row.bus_user_id,
            asset=row.asset,
            amount=row.amount,
        )
        db.session.add(loan)
        db.session.commit()


@ns.route('/loan-apply/export')
@respond_with_code
class BusAmbLoanApplyExportResource(Resource):
    model = BusinessAmbassadorLoanApply

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status),
        bus_amb_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        loan_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金申请列表-导出"""
        return BusAmbLoanApplyResource.get_(**kwargs)


@ns.route('/repay-history')
@respond_with_code
class BusAmbRepayHistoryResource(Resource):
    model = BusinessAmbassadorRepayHistory

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status),
        bus_amb_id=wa_fields.Integer,
        bus_user_id=wa_fields.Integer,
        loan_id=wa_fields.Integer,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Integer,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金-还款记录"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        query = model.query.order_by(model.id.desc())
        if bus_amb_id := kwargs.get("bus_amb_id"):
            query = query.filter(model.bus_amb_id == bus_amb_id)
        if bus_user_id := kwargs.get("bus_user_id"):
            query = query.filter(model.bus_user_id == bus_user_id)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if (loan_id := kwargs.get("loan_id")) is not None:
            query = query.filter(model.loan_id == loan_id)

        page, limit = kwargs["page"], kwargs["limit"]
        is_export = kwargs.get('export')
        if is_export:
            _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
            records = _paginate.items
            total = _paginate.total
        else:
            _paginate = query.paginate(page, limit, error_out=False)
            records = _paginate.items
            total = _paginate.total

        records: list[model]
        q_user_ids = {item.bus_amb_id for item in records}
        q_user_ids.update([item.bus_user_id for item in records])
        user_name_map = get_admin_user_name_map(q_user_ids)

        res = []
        for item in records:
            d = item.to_dict(enum_to_name=True)
            d["bus_amb_name"] = user_name_map.get(item.bus_amb_id, "")
            d["bus_user_name"] = user_name_map.get(item.bus_user_id, "")
            res.append(d)

        loan_info = {}
        if not is_export:
            loan_ids = {i["loan_id"] for i in res}
            if len(loan_ids) == 1:
                loan_id = list(loan_ids)[0]
                loan_row = BusinessAmbassadorLoan.query.get(loan_id)
                loan_info = {
                    "amount": loan_row.amount,
                    "repay_amount": loan_row.repay_amount,
                    "unflat_amount": loan_row.unflat_amount,
                }

        return dict(
            total=total,
            items=res,
            extra=dict(
                status_dict=model.Status,
                loan_info=loan_info,
            ),
        )


@ns.route('/one-repay-history')
@respond_with_code
class BusAmbOneRepayHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        loan_id=wa_fields.Integer(required=True),
        page=PageField,
        limit=LimitField(missing=200),
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-商务大使预付金申请列表-查看还款详情"""
        return BusAmbRepayHistoryResource.get_(**kwargs)


@ns.route('/permission')
@respond_with_code
class BusAmbPermissionResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        keyword=wa_fields.String(),
        page=PageField,
        limit=LimitField(missing=200),
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-权限设置"""
        page, limit = kwargs['page'], kwargs['limit']

        query = BusinessAmbassadorPermission.query.order_by(
            BusinessAmbassadorPermission.id,
        )
        if keyword := kwargs.get('keyword'):
            keyword_results = User.search_for_users(keyword)
            query = query.filter(
                BusinessAmbassadorPermission.user_id.in_(keyword_results)
            )
        _paginate = query.paginate(page, limit, error_out=False)
        records = _paginate.items
        total = _paginate.total

        user_name_map = {
            item.id: item.name if item.name else item.email
            for item in User.query.filter(
                User.id.in_([item.user_id for item in records]),
            ).with_entities(
                User.id,
                User.name,
                User.email,
            ).all()
        }

        data = []
        for item in records:
            show_list, limit_list = [], []
            if item.permission_list:
                for p in json.loads(item.permission_list):
                    t = BusinessAmbassadorPermission.Type(p)
                    show_list.append(t.value)
                    limit_list.append(t.name)
            data.append({
                'user_id': item.user_id,
                'user_name': user_name_map.get(item.user_id, item.user_id),
                'show': '、'.join(show_list),
                'limit_list': limit_list,
            })

        return dict(
            total=total,
            items=data,
        )

    @classmethod
    def update_limit(cls, permission: BusinessAmbassadorPermission, limit_list: list[str]):
        p_list = []
        for _type in BusinessAmbassadorPermission.Type:
            if _type.name in limit_list:
                p_list.append(_type.value)
        permission.permission_list = json.dumps(p_list)

    @classmethod
    @ns.use_kwargs(
        dict(
            user_keyword=wa_fields.String(required=True),
            limit_list=wa_fields.List(wa_fields.String),
        )
    )
    def post(cls, **kwargs):
        """用户-商务大使体系-权限设置-添加"""
        user_keyword: str = kwargs['user_keyword']
        if user_keyword.isdigit():
            users = User.query.filter(User.id == int(user_keyword)).with_entities(
                User.id
            ).all()
            if not users:
                raise InvalidArgument(f'未找到用户ID')
            user_id = users[0].id
        else:
            user_id = User.full_match_user(user_keyword)
            if not user_id:
                raise InvalidArgument(f'未找到该邮箱指定ID')

        if not AdminUser.query.filter(
                AdminUser.user_id == user_id,
                AdminUser.status == AdminUser.Status.PASSED
        ).first():
            raise InvalidArgument(f'该用户不是Admin管理员，不允许添加')

        permission = BusinessAmbassadorPermission.get_or_create(user_id=user_id)
        cls.update_limit(permission, kwargs.get('limit_list', []))
        db.session.add(permission)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorPermission,
            detail=kwargs,
            target_user_id=user_id,
        )

        return dict()

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            limit_list=wa_fields.List(wa_fields.String),
        )
    )
    def put(cls, **kwargs):
        """用户-商务大使体系-权限设置-编辑"""
        permission = BusinessAmbassadorPermission.query.filter(
            BusinessAmbassadorPermission.user_id == kwargs['user_id'],
        ).first()
        if not permission:
            raise InvalidArgument(f'未找到指定用户')
        old_data = permission.to_dict(enum_to_name=True)

        cls.update_limit(permission, kwargs.get('limit_list', []))
        db.session.add(permission)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorPermission,
            old_data=old_data,
            new_data=permission.to_dict(enum_to_name=True),
            target_user_id=permission.user_id,
        )

        return dict()

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """用户-商务大使体系-权限设置-删除"""
        permission = BusinessAmbassadorPermission.query.filter(
            BusinessAmbassadorPermission.user_id == kwargs['user_id'],
        ).first()
        if not permission:
            raise InvalidArgument(f'未找到指定用户')
        user_id = permission.user_id
        log_data = permission.to_dict(enum_to_name=True)

        db.session.delete(permission)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusAmbassadorPermission,
            detail=log_data,
            target_user_id=user_id,
        )

        return dict()


@ns.route("/change-log")
@respond_with_code
class BusRelationChangeLogResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            biz_id=wa_fields.Integer,
            admin_user_id=wa_fields.Integer,
            application_id=wa_fields.Integer,
            change_type=EnumField(BusRelationChangelog.ChangeType),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-变更记录"""
        model = BusRelationChangelog
        query = model.query.order_by(model.id.desc())
        if biz_id := kwargs.get("biz_id"):
            query = query.filter(model.biz_id == biz_id)
        if start_time := kwargs.get("start_time"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(model.created_at <= end_time)
        if change_type := kwargs.get("change_type"):
            query = query.filter(model.change_type == change_type.name)
        if admin_user_id := kwargs.get('admin_user_id'):
            query = query.filter(model.admin_user_id == admin_user_id)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        admin_user_name_map = get_admin_user_name_map({i.admin_user_id for i in rows if i.admin_user_id})

        items = []
        for row in rows:
            row: model
            d = row.to_dict(enum_to_name=True)
            d["admin_user_name"] = admin_user_name_map.get(row.admin_user_id) or ""
            old = row.detail["old"]
            new = row.detail["new"]
            if row.change_type == model.ChangeType.EDIT_BUS_USER_PR_RATE:
                old_desc = {i['user_id']: i['rate'] for i in old}
                new_desc = {i['user_id']: i['rate'] for i in new}
            else:
                old_desc = repr(old)
                new_desc = repr(new)
            d["detail_str"] = f"{row.change_type.value} {old_desc} 变为 {new_desc}"
            items.append(d)

        return dict(
            items=items,
            total=pagination.total,
            extra=dict(
                change_type_dict={i.name: i.value for i in model.ChangeType},
            ),
        )


@ns.route('/tree-ambassadors')
@respond_with_code
class TreeAmbassadorsResource(Resource):
    model = TreeAmbassador

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "rate", Language.ZH_HANS_CN: "返佣比例"},
        {"field": "allow_add_child", Language.ZH_HANS_CN: "是否允许拓展子代理"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "bus_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "team_name", Language.ZH_HANS_CN: "所属团队"},
        {"field": "tree_height", Language.ZH_HANS_CN: "代理级别"},
        {"field": "tree_paths", Language.ZH_HANS_CN: "代理链路"},
        {"field": "effected_at", Language.ZH_HANS_CN: "成为代理时间"},
        {"field": "effect_refer_count", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "effect_refer_active_user_count", Language.ZH_HANS_CN: "refer活跃人数"},
        {"field": "effect_refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产"},
        {"field": "effect_refer_trade_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "effect_refer_trade_amount", Language.ZH_HANS_CN: "refer交易总额（USD）"},
        {"field": "this_month_deal_amount", Language.ZH_HANS_CN: "当月refer交易总额（USD）"},
        {"field": "total_refer_amount", Language.ZH_HANS_CN: "累计返佣（USDT）"},
        {"field": "month_refer_amount", Language.ZH_HANS_CN: "当月返佣（USDT）"},
        {"field": "admin_remark", Language.ZH_HANS_CN: "备注"},
    )

    STATUS_DICT = {
        model.Status.VALID.name: "正常",
        model.Status.DELETED.name: "删除",
    }

    @classmethod
    def get_tree_root_ids_under_bus_users(cls, bus_user_ids: list[int]) -> set[int]:
        rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.bus_user_id.in_(bus_user_ids),
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
            BusinessAmbassador.type == BusinessAmbassador.Type.TREE_ROOT,
        ).with_entities(
            BusinessAmbassador.user_id,
        )
        return {i.user_id for i in rows}

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(STATUS_DICT, missing="VALID"),
            user_id=wa_fields.Integer,
            parent_id=wa_fields.Integer,
            root_id=wa_fields.Integer,
            bus_user_id=wa_fields.Integer,
            team_id=wa_fields.Integer,
            tree_height=wa_fields.Integer,
            page=PageField,
            limit=LimitField,
            export=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-子代理列表"""
        return cls.get_(**kwargs)

    @classmethod
    def get_(cls, **kwargs):
        model = cls.model
        page, limit = kwargs["page"], kwargs["limit"]

        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.TREE_AMB_LIST,
        )
        limit_visible = permission_limit and not is_super_user(g.user.id)
        if limit_visible:
            visible_bus_users = BusAmbassadorsResource.get_visible_bus_users(g.user.id, kwargs.get("team_id"))
            visible_bus_u_ids = [i.user_id for i in visible_bus_users]
            visible_root_ids = cls.get_tree_root_ids_under_bus_users(visible_bus_u_ids)
        else:
            visible_root_ids = set()

        is_export = kwargs.get('export')
        query = model.query.order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if parent_id := kwargs.get("parent_id"):
            q_child_ids = [i['user_id'] for i in TreeAmbassadorHierarchy.get_child_info(parent_id)]
            query = query.filter(model.user_id.in_(q_child_ids))
        if root_id := kwargs.get("root_id"):
            query = query.filter(model.root_id == root_id)
        if bus_user_id := kwargs.get("bus_user_id"):
            q_root_ids = cls.get_tree_root_ids_under_bus_users([bus_user_id])
            query = query.filter(model.root_id.in_(q_root_ids))
        if team_id := kwargs.get("team_id"):
            b_model = BusinessUser
            b_rows = b_model.query.filter(
                b_model.team_id == team_id,
            ).with_entities(b_model.user_id)
            bus_user_ids = [i.user_id for i in b_rows]
            q_root_ids = cls.get_tree_root_ids_under_bus_users(bus_user_ids)
            query = query.filter(model.root_id.in_(q_root_ids))
        if tree_height := kwargs.get("tree_height"):
            query = query.filter(model.tree_height == tree_height)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if limit_visible:
            query = query.filter(model.root_id.in_(visible_root_ids))

        if is_export:
            _paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
            records = _paginate.items
            total = _paginate.total
        else:
            records = query.all()
            total = len(records)

        records: list[model]
        amb_ids = {item.user_id for item in records}

        root_ids = {i.root_id for i in records}
        root_bus_amb_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id.in_(root_ids),
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.bus_user_id,
            BusinessAmbassador.rate,
        ).all()
        root_row_map = {i.user_id: i for i in root_bus_amb_rows}
        root_id_bus_id_map = {i.user_id: i.bus_user_id for i in root_bus_amb_rows}
        bus_user_id = set(root_id_bus_id_map.values())
        bus_user_name_map = get_admin_user_name_map(bus_user_id)

        bus_user_rows = BusinessUser.query.filter(
            BusinessUser.user_id.in_(bus_user_id)
        ).with_entities(
            BusinessUser.user_id, BusinessUser.team_id
        ).all()
        team_id_map = {i.user_id: i.team_id for i in bus_user_rows}
        team_row = BusinessTeam.query.filter().with_entities(BusinessTeam.id, BusinessTeam.name)
        team_name_map = {i.id: i.name for i in team_row}
        bus_user_team_map = {k: team_name_map[v] for k, v in team_id_map.items()}

        amb_statics_map = {
            i.user_id: i for i in TreeAmbassadorStatistics.query.filter(
                TreeAmbassadorStatistics.user_id.in_(amb_ids),
            ).all()
        }

        parent_info_map = TreeAmbassadorHierarchy.batch_get_parent_info_map(list(amb_ids))
        parent_ids = {j['user_id'] for i in parent_info_map.values() for j in i}
        parent_amb_rows = model.query.filter(
            model.user_id.in_(parent_ids),
        ).with_entities(
            model.user_id,
            model.rate,
        ).all()
        parent_amb_row_map = {i.user_id: i for i in parent_amb_rows}

        q_email_ids = amb_ids | parent_ids
        user_rows = User.query.filter(
            User.id.in_(q_email_ids),
        ).with_entities(
            User.id,
            User.email,
        ).all()
        email_map = {i.id: i.email for i in user_rows}

        table_items = []
        monthly_report_map = BusAmbassadorsResource.get_monthly_report_map(amb_ids, include_bro_amb_data=False)
        for item in records:
            amb_id = item.user_id
            if amb_id not in monthly_report_map:
                monthly_report_map[amb_id] = defaultdict(Decimal)

            parent_info = parent_info_map.get(amb_id, [])
            tree_paths = []
            for p in parent_info:
                puid = p['user_id']
                if p['tree_height']:
                    level_str = f"{p['tree_height']}级代理"
                    p_row = parent_amb_row_map.get(puid)
                    p_rate = p_row.rate if p_row else None
                else:
                    level_str = '总代理'
                    r_row = root_row_map.get(puid)
                    p_rate = r_row.rate if r_row else None
                d = [p['tree_height'], level_str, puid, email_map.get(puid, puid), p_rate]
                tree_paths.append(d)

            d = item.to_dict(enum_to_name=True)
            d["email"] = email_map.get(amb_id, "")
            bus_id = root_id_bus_id_map.get(item.root_id)
            d["bus_user_id"] = bus_id
            d["bus_name"] = bus_user_name_map.get(bus_id, "")
            d["team_name"] = bus_user_team_map.get(bus_id, "")
            d['tree_paths'] = tree_paths
            statics: TreeAmbassadorStatistics = amb_statics_map.get(amb_id)
            d["effect_refer_count"] = statics.effect_refer_count if statics else 0
            d["effect_refer_trade_count"] = statics.effect_refer_trade_count if statics else 0
            d["effect_refer_trade_amount"] = statics.effect_refer_trade_amount if statics else 0
            d["effect_refer_user_balance_usd"] = statics.effect_refer_user_balance_usd if statics else 0
            d["effect_refer_active_user_count"] = statics.effect_refer_active_user_count if statics else 0
            d["total_refer_amount"] = statics.total_refer_amount + statics.total_indirect_refer_amount if statics else 0
            d["month_refer_amount"] = statics.month_refer_amount + statics.month_indirect_refer_amount if statics else 0
            d["this_month_deal_amount"] = monthly_report_map[item.user_id]["month_deal_amount"]
            table_items.append(d)

        if is_export:
            for d in table_items:
                d["rate"] = f'{amount_to_str(d["rate"] * 100, 4)}%'
                d["effected_at"] = d["effected_at"].strftime("%Y-%m-%d %H:%M:%S") if d["effected_at"] else ""
                d["status"] = cls.STATUS_DICT[status]
                d["allow_add_child"] = "是" if d["allow_add_child"] else "否"
                d["tree_height"] = f'{d["tree_height"]}级'
                d["tree_paths"] = "\n".join([f'{p[1]} {p[3]} {amount_to_str(p[4] * 100, 4)}%' for p in d['tree_paths']])

            return export_xlsx(
                filename='child_ambassador_list',
                data_list=table_items,
                export_headers=cls.export_headers,
            )

        table_items = table_items[(page - 1) * limit: page * limit]
        return dict(
            total=total,
            items=table_items,
            extra=dict(
                status_dict=cls.STATUS_DICT,
                team_name_map=team_name_map,
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            rate=PositiveDecimalField(required=True, allow_zero=True),
            allow_add_child=wa_fields.Boolean(required=True),
            admin_remark=wa_fields.String(allow_none=True),
        )
    )
    def put(cls, **kwargs):
        """用户-商务大使体系-子代理编辑"""
        user_id = kwargs["user_id"]
        rate = kwargs["rate"]
        allow_add_child = kwargs["allow_add_child"]
        admin_remark = kwargs.get("admin_remark") or ""
        amb = TreeAmbHelper.get_ambassador(user_id, need_valid=True)
        if not amb:
            raise InvalidArgument(message=f"子代理不存在或已删除")
        max_child_rate = TreeAmbHelper.get_max_child_rate(user_id)
        if not (0 <= max_child_rate <= rate):
            raise InvalidArgument(message=f"返佣比例不可设置小于子代理的返佣比例{amount_to_str(max_child_rate)}")
        TreeAmbHelper.edit_amb(
            user_id=user_id,
            rate=rate,
            admin_remark=admin_remark,
        )
        if allow_add_child != amb.allow_add_child:
            TreeAmbHelper.edit_allow_add_child(user_id=user_id, new_allow_add_child=allow_add_child)

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """用户-商务大使体系-子代理删除"""
        user_id = kwargs["user_id"]
        amb = TreeAmbHelper.get_ambassador(user_id, need_valid=True)
        if not amb:
            raise InvalidArgument(message=f"子代理不存在或已删除")
        TreeAmbHelper.delete_ambassador(user_id=user_id)


@ns.route('/tree-ambassadors/export')
@respond_with_code
class TreeAmbassadorsExportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(TreeAmbassadorsResource.STATUS_DICT, missing="VALID"),
            user_id=wa_fields.Integer,
            parent_id=wa_fields.Integer,
            root_id=wa_fields.Integer,
            bus_user_id=wa_fields.Integer,
            team_id=wa_fields.Integer,
            tree_height=wa_fields.Integer,
            page=PageField,
            limit=LimitField,
            export=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """用户-商务大使体系-子代理列表-导出"""
        kwargs["export"] = True
        return TreeAmbassadorsResource.get_(**kwargs)
