# -*- coding: utf-8 -*-

from .base import ErrorWithResponseCode
from flask_babel import gettext as _


class CommonError(ErrorWithResponseCode):

    response_code = 1
    message_template = _('错误')


class InvalidArgument(ErrorWithResponseCode):

    response_code = 2
    message_template = _('参数错误')


class InternalServerError(ErrorWithResponseCode):

    response_code = 3
    message_template = _('系统繁忙，请稍后再试')


class RequireLangError(ErrorWithResponseCode):

    response_code = 4
    message_template = _('表头缺失支持的语言')


class CaptchaRequired(ErrorWithResponseCode):

    response_code = 5
    message_template = _('请输入验证码')


class TwoFactorAuthenticationFailed(ErrorWithResponseCode):

    response_code = 7
    message_template = _('2FA验证失败，请重试')


# app兼容，错误码必须是7
class InvalidTotpVerificationCode(TwoFactorAuthenticationFailed):

    message_template = _('TOTP验证码错误')


class InvalidMobileVerificationCode(TwoFactorAuthenticationFailed):

    message_template = _('短信验证码错误')
    
    
class WebauthnVerificationFailed(TwoFactorAuthenticationFailed):

    message_template = _('通行密钥验证错误')


class AuthenticationTimeout(ErrorWithResponseCode):

    response_code = 132
    message_template = _('操作已超时，请刷新重试')


class TwoFactorAuthenticationRequired(ErrorWithResponseCode):

    response_code = 9
    message_template = _('请先绑定手机或谷歌验证器或通行密钥')


class InvalidAccount(ErrorWithResponseCode):

    response_code = 33
    message_template = _('错误账号')


class ServiceTimeout(ErrorWithResponseCode):

    response_code = 36
    message_template = _('服务超时')


class ServiceUnavailable(ErrorWithResponseCode):

    response_code = 35
    message_template = _('服务不可用')


class VerificationCodeHasBeenUsed(ErrorWithResponseCode):

    response_code = 27
    message_template = _('验证码已经被使用。请等待下一个验证码。')


class WithdrawalAmountTooSmall(ErrorWithResponseCode):

    response_code = 104
    message_template = _('提现金额过小')


class ForbidTrading(ErrorWithResponseCode):

    response_code = 115
    message_template = _('暂无法交易，如需更多帮助请提交工单咨询。')


class PerpetualTradingLimited(ErrorWithResponseCode):

    response_code = 130
    message_template = _("暂无法开仓或加仓，可补充保证金，如需减仓/平仓，可使用“一键平仓“功能，更多帮助请提交工单咨询。")


class TransferOutNotAllowed(ErrorWithResponseCode):

    response_code = 131
    message_template = _("暂无法划出资金，仅可划入资金，如需更多帮助请提交工单咨询。")


class CreditRiskForbidSpotTrading(ErrorWithResponseCode):

    response_code = 128
    message_template = _("禁止现货交易：您的授信账户当前风险率已低于可交易风险率，系统已关闭现货交易功能，请尽快补充非授信资产")


class OrderPlacementForbidden(ErrorWithResponseCode):

    response_code = 117
    message_template = _('当前市场不可下单，请稍后再试。')


class OrderCancellationForbidden(ErrorWithResponseCode):

    response_code = 118
    message_template = _('当前市场不可撤单，请稍后再试。')


class InsufficientBalance(ErrorWithResponseCode):

    response_code = 107
    message_template = _('可用资产不足')


class DepositsSuspended(ErrorWithResponseCode):

    response_code = 114
    message_template = _('停止充值。')


class WalletUnderMaintenance(ErrorWithResponseCode):

    response_code = 122
    message_template = _('钱包维护中')


class WithdrawalPrecisionExceeded(ErrorWithResponseCode):

    response_code = 123
    message_template = _('超过提现精度限制')


class LocalTransfersSuspended(ErrorWithResponseCode):

    response_code = 124
    message_template = _('暂停站内转账')


class TotpAuthKeyDoesNotExist(ErrorWithResponseCode):

    response_code = 210
    message_template = _('请绑定TOTP认证')


class InvalidKYC(ErrorWithResponseCode):

    response_code = 248
    message_template = _('请先完成实名认证')


class DuplicateSecurityResetApplication(ErrorWithResponseCode):

    response_code = 59
    message_template = _('Already submitted reset application')


class InvalidSecurityResetToken(ErrorWithResponseCode):

    response_code = 62
    message_template = _('已超时，请重试。')

class SubAccountNotAllowed(ErrorWithResponseCode):

    response_code = 63
    message_template = _('该业务不支持子账号')

class TransferNotAllowed(ErrorWithResponseCode):

    response_code = 57
    message_template = _('暂无法划转，如需更多帮助请提交工单咨询。')


class CreditRiskForbidTransfer(ErrorWithResponseCode):
    response_code = 129
    message_template = _('授信风险风险率过低或者授信余额不足')


class InvalidWithdrawalAddress(ErrorWithResponseCode):

    response_code = 310
    message_template = _('提现地址错误。')


class DepositAddressRenewalDisabled(ErrorWithResponseCode):

    response_code = 311
    message_template = _('该地址不支持更换。')


class ServerInMaintainMode(ErrorWithResponseCode):
    response_code = 5000
    message_template = _("in maintain mode")


class OfflineFeature(ErrorWithResponseCode):
    response_code = 98
    message_template = _("该功能已下架")


class NotSupported(ErrorWithResponseCode):
    response_code = 99
    message_template = _("Not Supported")


class OutOfRange(ErrorWithResponseCode):
    response_code = 97
    message_template = _("Out Of Range")


class ConfirmationRequired(ErrorWithResponseCode):
    response_code = 96
    message_template = _("Confirmation Required")


class DataNotReady(ErrorWithResponseCode):
    response_code = 95
    message_template = _("Data Not Ready")


class NotSupportedByCountryInOnlyWithdrawal(ErrorWithResponseCode):

    response_code = 94
    message_template = _(
        "很抱歉，根据您所在地区的监管要求，我们无法为您提供该服务。"
    )


class NotSupportedRegionInOnlyWithdrawal(ErrorWithResponseCode):

    response_code = 262
    message_template = _(
        "很抱歉，根据您所在地区（%(region)s）的监管要求，我们无法为您提供该服务。"
    )


class NotSupportedByCountryNotKYC(ErrorWithResponseCode):

    response_code = 134
    message_template = _(
        "我们无法为您IP所在地区未完成实名认证的用户提供服务，请先前往实名认证。"
    )


class NotSupportedByApp(ErrorWithResponseCode):

    response_code = 93
    message_template = _("当前版本不支持该功能，请升级App后使用")

class HistoryDataNotReady(ErrorWithResponseCode):
    response_code = 92
    message_template = _("暂无数据")


class NotSupportedAsPolicyRequires(ErrorWithResponseCode):

    response_code = 91
    message_template = _(
        "很抱歉，根据反洗钱政策要求，我们无法为您提供该功能服务。"
    )

class WebAuthnValidateError(ErrorWithResponseCode):

    response_code = 90
    message_template = _("验证失败")


class WalletSignFailed(ErrorWithResponseCode):

    response_code = 89
    message_template = _("签名失败")


class WithdrawalLimit30DaysExceeded(ErrorWithResponseCode):

    response_code = 312
    message_template = _('近30天可提现额度不足')


class GeneralUploadImageError(ErrorWithResponseCode):

    response_code = 140
    message_template = _('仅支持上传png/jpg/jpeg/webp图片格式。')


class WalletDuplicateSubmission(ErrorWithResponseCode):

    response_code = 135
    message_template = _('重复提交')
    