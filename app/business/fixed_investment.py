# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import timedelta, datetime, date
from decimal import Decimal
from typing import Any

from flask import current_app
from flask_babel import gettext
from sqlalchemy import func

from app.common.constants import ServerBalanceType
from app.common import BalanceBusiness, CeleryQueues, MessageTitle, MessageContent
from app.business import (
    ServerClient,
    SPOT_ACCOUNT_ID,
    ServerResponseCode,
    CacheLock,
    LockKeys,
    lock_call,
    TradeLogDB,
)
from app.exceptions import InvalidArgument, InsufficientBalance, TransferNotAllowed
from app.models import db, Message, User
from app.models.investment import AssetInvestmentConfig
from app.models.fixed_investment import (
    FixedInvestmentProduct, FixedInvestmentRecord,
    FixedInvestmentOperateHistory, FixedInvestmentInterestHistory,
    UserFixedInvestmentSummary
)
from app.utils import batch_iter, celery_task, route_module_to_celery_queue, quantize_amount, amount_to_str
from app.utils.parser import JsonEncoder
from app.utils.date_ import now


route_module_to_celery_queue(__name__, CeleryQueues.INVESTMENT)


class FixedInvestTransferHelper:
    @classmethod
    def do_subscribe_transfer(cls, row: FixedInvestmentOperateHistory, on_finished_commit: bool):
        """申购的划转（先扣现货账户，再增加&锁定到理财账户）"""
        assert row.type == FixedInvestmentOperateHistory.Type.SUBSCRIBE

        from_account_id, to_account_id = SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID
        from_business = to_business = BalanceBusiness.FIXED_INVEST_SUBSCRIBE
        biz_id = row.record_id  # 不用row.id，保持lock 和 unlock的biz_id一致

        client = ServerClient()
        remark = f"fixed_invest trans {row.id} {row.record_id} {row.type.name}"
        if row.status == FixedInvestmentOperateHistory.Status.CREATED:
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(-row.amount),
                    business=from_business,
                    business_id=biz_id,
                    detail={"remark": remark},
                    account_id=from_account_id,
                )
                if not result:
                    current_app.logger.error(
                        f"fixed_invest do_subscribe_transfer {row.id} {row.record_id} {row.type.name}"
                        f"{row.asset} {row.amount} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"fixed_invest do_subscribe_transfer {row.id} {row.record_id} {row.type.name}"
                    f"{row.asset} {row.amount} failed {e!r}"
                )
                if getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                    row.status = FixedInvestmentOperateHistory.Status.FAILED
                    db.session.commit()
                    return
                raise
            row.status = FixedInvestmentOperateHistory.Status.DEDUCTED
            row.deducted_at = now()
            db.session.commit()

        if row.status == FixedInvestmentOperateHistory.Status.DEDUCTED:
            result = client.add_and_lock_user_balance(
                user_id=row.user_id,
                asset=row.asset,
                amount=str(row.amount),
                business=to_business,
                business_id=biz_id,
                detail={"remark": remark},
                account_id=to_account_id,
            )
            if not result:
                current_app.logger.error(
                    f"fixed_invest do_subscribe_transfer {row.id} {row.record_id} {row.type.name}"
                    f"{row.asset} {row.amount} add DUPLICATE_BALANCE_UPDATE"
                )
            row.status = FixedInvestmentOperateHistory.Status.FINISHED
            row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def do_redeem_transfer(cls, row: FixedInvestmentOperateHistory, on_finished_commit: bool):
        """赎回的划转（先解锁&扣减理财账户，再增加现货账户）"""
        if row.type == FixedInvestmentOperateHistory.Type.MATURITY_REDEEM:
            from_business = to_business = BalanceBusiness.FIXED_INVEST_MATURITY_REDEEM
        elif row.type == FixedInvestmentOperateHistory.Type.EARLY_REDEEM:
            from_business = to_business = BalanceBusiness.FIXED_INVEST_EARLY_REDEEM
        else:
            raise ValueError(f"do_redeem_transfer not support type {row.type.name}")

        from_account_id, to_account_id = AssetInvestmentConfig.ACCOUNT_ID, SPOT_ACCOUNT_ID
        unlock_bus = BalanceBusiness.FIXED_INVEST_SUBSCRIBE
        biz_id = row.record_id  # 不用row.id，保持lock 和 unlock的biz_id一致

        client = ServerClient()
        remark = f"fixed_invest trans {row.id} {row.record_id} {row.type.name}"
        if row.status == FixedInvestmentOperateHistory.Status.CREATED:
            try:
                result = client.unlock_and_sub_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(row.amount),
                    business_id=biz_id,  # 虽然biz_id都是存单id，但from_business是REDEEM类型，所以不会和申购时的资产变更 出现重复
                    detail={"remark": remark},
                    account_id=from_account_id,
                    unlock_bus=unlock_bus,
                    sub_bus=from_business,
                )
                if not result:
                    current_app.logger.error(
                        f"fixed_invest do_redeem_transfer {row.id} {row.record_id} {row.type.name}"
                        f"{row.asset} {row.amount} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"fixed_invest do_redeem_transfer {row.id} {row.record_id} {row.type.name}"
                    f"{row.asset} {row.amount} failed {e!r}"
                )
                raise
            row.status = FixedInvestmentOperateHistory.Status.DEDUCTED
            row.deducted_at = now()
            db.session.commit()

        if row.status == FixedInvestmentOperateHistory.Status.DEDUCTED:
            result = client.add_user_balance(
                user_id=row.user_id,
                asset=row.asset,
                amount=str(row.amount),
                business=to_business,
                business_id=biz_id,
                detail={"remark": remark},
                account_id=to_account_id,
            )
            if not result:
                current_app.logger.error(
                    f"fixed_invest do_redeem_transfer {row.id} {row.record_id} {row.type.name}"
                    f"{row.asset} {row.amount} add DUPLICATE_BALANCE_UPDATE"
                )
            row.status = FixedInvestmentOperateHistory.Status.FINISHED
            row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def do_interest_transfer(cls, row: FixedInvestmentInterestHistory, on_finished_commit: bool):
        """利息的划转（只增加现货账户）"""

        to_account_id = SPOT_ACCOUNT_ID
        to_business = BalanceBusiness.FIXED_INVEST_INTEREST
        biz_id = row.id  # 一个存单可能有多条类型的利息，用利息记录id

        client = ServerClient()
        remark = f"fixed_invest interest {row.id} {row.product_id} {row.record_id} {row.type.name}"
        if row.status == FixedInvestmentInterestHistory.Status.CREATED:
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(row.amount),
                    business=to_business,
                    business_id=biz_id,
                    detail={"remark": remark},
                    account_id=to_account_id,
                )
                if not result:
                    current_app.logger.error(
                        f"fixed_invest do_interest_transfer {row.id} {row.record_id} {row.type.name}"
                        f"{row.asset} {row.amount} add DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"fixed_invest do_interest_transfer {row.id} {row.record_id} {row.type.name}"
                    f"{row.asset} {row.amount} failed {e!r}"
                )
                raise
            row.finished_at = now()
            row.status = FixedInvestmentInterestHistory.Status.FINISHED
            if on_finished_commit:
                db.session.commit()


class FixedInvestOperator:
    """定期理财的申购和赎回逻辑"""

    @classmethod
    def get_user_cur_holding_amount(cls, user_id: int, product_id: int) -> Decimal:
        """获取用户在某个产品中的当前申购金额"""
        total_holding_amount = FixedInvestmentRecord.query.filter(
            FixedInvestmentRecord.product_id == product_id,
            FixedInvestmentRecord.user_id == user_id,
            FixedInvestmentRecord.status == FixedInvestmentRecord.Status.HOLDING,
        ).with_entities(
            func.sum(FixedInvestmentRecord.amount),
        ).scalar() or Decimal()
        return total_holding_amount

    @classmethod
    def get_user_remaining_subscription_amount(cls, user_id: int, product: FixedInvestmentProduct) -> Decimal:
        """获取用户在某个产品的剩余可申购金额"""
        cur_holding_amount = cls.get_user_cur_holding_amount(user_id=user_id, product_id=product.id)
        user_remaining_subscription_amount = max(product.max_user_total_amount - cur_holding_amount, Decimal())
        return user_remaining_subscription_amount

    @classmethod
    def get_user_available_subscription_amount(cls, user_id: int, product: FixedInvestmentProduct) -> Decimal:
        """获取用户在某个产品的可申购金额，min(产品剩余额度, 用户剩余额度)"""
        user_remaining_subscription_amount = cls.get_user_remaining_subscription_amount(user_id, product)
        remaining_subscription_amount = min(product.remaining_subscription_amount, user_remaining_subscription_amount)
        return remaining_subscription_amount

    @classmethod
    def check_user_subscribe(cls, user_id: int, amount: Decimal, product: FixedInvestmentProduct):
        """申购相关check"""
        if amount < product.min_subscription_amount:
            raise InvalidArgument(message=gettext("未达到最小申购金额"))
        if amount > product.remaining_subscription_amount:
            raise InvalidArgument(message=gettext("该产品已售罄，暂无法申购"))

        user_available_subscription_amount = cls.get_user_available_subscription_amount(user_id, product)
        if amount > user_available_subscription_amount:
            raise InvalidArgument(message=gettext("你已达到个人申购总限额"))

        client = ServerClient()
        asset = product.asset
        asset_balances = client.get_user_balances(user_id=user_id, account_id=SPOT_ACCOUNT_ID, asset=asset)
        available = asset_balances.get(asset, {}).get("available", Decimal())
        if available < amount:
            raise InsufficientBalance

    @classmethod
    def check_subscribe_user_status(cls, user: User):
        from app.business.credit import credit_user_has_unflat_asset
        if credit_user_has_unflat_asset(user):
            raise TransferNotAllowed(message=gettext("授信用户不允许划转资产到理财账户。"))

    @classmethod
    def subscribe(
        cls,
        user: User,
        amount: Decimal,
        product: FixedInvestmentProduct,
    ) -> tuple[FixedInvestmentRecord, FixedInvestmentOperateHistory]:
        """申购"""
        cls.check_subscribe_user_status(user)

        user_id = user.id
        now_ = now()
        maturity_time = now_ + timedelta(days=product.lock_days)
        product_id = product.id
        with CacheLock(key=LockKeys.fixed_invest_product(product_id=product_id), wait=False):
            db.session.rollback()

            cls.check_user_subscribe(user_id, amount, product)

            product.subscribed_amount += amount

            record = FixedInvestmentRecord(
                created_at=now_,
                user_id=user_id,
                product_id=product_id,
                asset=product.asset,
                amount=amount,
                base_apr=product.base_apr,
                lock_days=product.lock_days,
                maturity_time=maturity_time,
            )
            db.session.add(record)
            db.session.flush()

            op_his = FixedInvestmentOperateHistory(
                user_id=user_id,
                record_id=record.id,
                product_id=product_id,
                type=FixedInvestmentOperateHistory.Type.SUBSCRIBE,
                asset=record.asset,
                amount=record.amount,
            )
            db.session.add(op_his)
            db.session.flush()
            db.session.commit()
            cls._do_subscribe(product, record, op_his)

        return record, op_his

    @classmethod
    def _do_subscribe(
        cls,
        product: FixedInvestmentProduct,
        record: FixedInvestmentRecord,
        op_his: FixedInvestmentOperateHistory,
    ) -> bool:
        FixedInvestTransferHelper.do_subscribe_transfer(op_his, on_finished_commit=False)
        if op_his.status == FixedInvestmentOperateHistory.Status.FAILED:
            # 转出账户（现货）扣减失败
            record.status = FixedInvestmentRecord.Status.FAILED
            product.subscribed_amount -= record.amount
            db.session.commit()
            return False
        elif op_his.status == FixedInvestmentOperateHistory.Status.FINISHED:
            record.status = FixedInvestmentRecord.Status.HOLDING
            db.session.commit()
            return True
        else:
            raise

    @classmethod
    def early_redeem(
        cls,
        record: FixedInvestmentRecord,
        product: FixedInvestmentProduct,
    ) -> tuple[FixedInvestmentRecord, FixedInvestmentOperateHistory]:
        """提前赎回"""
        now_ = now()
        user_id = record.user_id
        product_id = product.id
        redeem_amount = record.amount
        redeem_type = FixedInvestmentOperateHistory.Type.EARLY_REDEEM
        with CacheLock(key=LockKeys.fixed_invest_product(product_id=product_id), wait=False):
            db.session.rollback()
            assert record.status == FixedInvestmentRecord.Status.HOLDING

            record.redeem_time = now_
            record.status = FixedInvestmentRecord.Status.REDEEMING

            op_his = FixedInvestmentOperateHistory(
                user_id=user_id,
                record_id=record.id,
                product_id=product_id,
                type=redeem_type,
                asset=record.asset,
                amount=redeem_amount,
            )
            db.session.add(op_his)
            db.session.flush()
            db.session.commit()
            cls._do_early_redeem(product, record, op_his)
        return record, op_his

    @classmethod
    def _do_early_redeem(
        cls,
        product: FixedInvestmentProduct,
        record: FixedInvestmentRecord,
        op_his: FixedInvestmentOperateHistory,
     ):
        FixedInvestTransferHelper.do_redeem_transfer(op_his, on_finished_commit=False)
        assert op_his.status == FixedInvestmentOperateHistory.Status.FINISHED  # 理财账户的余额一定足够
        record.status = FixedInvestmentRecord.Status.EARLY_REDEEMED
        product.subscribed_amount -= op_his.amount
        db.session.commit()
        FixedInvestMsg.send_early_redeemed(record)

    @classmethod
    def maturity_redeem(
        cls,
        record: FixedInvestmentRecord,
        product: FixedInvestmentProduct,
        now_: datetime = None,
    ) -> tuple[FixedInvestmentRecord, FixedInvestmentOperateHistory, list[FixedInvestmentInterestHistory]]:
        """到期赎回，外部统一加锁"""
        assert record.status == FixedInvestmentRecord.Status.HOLDING

        now_ = now_ or now()
        user_id = record.user_id
        product_id = product.id
        redeem_amount = record.amount

        op_his = FixedInvestmentOperateHistory(
            user_id=user_id,
            record_id=record.id,
            product_id=product_id,
            type=FixedInvestmentOperateHistory.Type.MATURITY_REDEEM,
            asset=record.asset,
            amount=redeem_amount,
        )
        db.session.add(op_his)
        db.session.flush()

        interest_his_rows = FixedInvestInterestHelper.new_interest_records(record, now_)

        record.redeem_time = now_
        record.status = FixedInvestmentRecord.Status.REDEEMING
        record.interest_amount = sum([i.amount for i in interest_his_rows])
        db.session.commit()

        cls._do_maturity_redeem(product, record, op_his, interest_his_rows)
        return record, op_his, interest_his_rows

    @classmethod
    def _do_maturity_redeem(
        cls,
        product: FixedInvestmentProduct,
        record: FixedInvestmentRecord,
        op_his: FixedInvestmentOperateHistory,
        interest_his_rows: list[FixedInvestmentInterestHistory],
    ):
        FixedInvestTransferHelper.do_redeem_transfer(op_his, on_finished_commit=False)
        assert op_his.status == FixedInvestmentOperateHistory.Status.FINISHED  # 理财账户的余额一定足够
        for interest_his in interest_his_rows:
            FixedInvestTransferHelper.do_interest_transfer(interest_his, on_finished_commit=False)
            assert interest_his.status == FixedInvestmentInterestHistory.Status.FINISHED
        record.status = FixedInvestmentRecord.Status.MATURITY_REDEEMED
        product.subscribed_amount -= op_his.amount
        db.session.commit()
        FixedInvestMsg.send_maturity_redeemed(record)

    @classmethod
    def retry_subscribe_by_op_his(cls, op_his_id: int):
        """申购重试"""
        op_his: FixedInvestmentOperateHistory = FixedInvestmentOperateHistory.query.get(op_his_id)
        assert op_his.type == FixedInvestmentOperateHistory.Type.SUBSCRIBE
        assert op_his.status in FixedInvestmentOperateHistory.PENDING_STATUES
        record: FixedInvestmentRecord = FixedInvestmentRecord.query.get(op_his.record_id)
        assert record.status == FixedInvestmentRecord.Status.CREATED
        product: FixedInvestmentProduct = FixedInvestmentProduct.query.get(record.product_id)
        assert product.status == FixedInvestmentProduct.Status.ONLINE

        with CacheLock(key=LockKeys.fixed_invest_product(product_id=product.id), wait=False):
            db.session.rollback()
            cls._do_subscribe(product, record, op_his)

    @classmethod
    def retry_early_redeem_by_op_his(cls, op_his_id: int):
        """提前赎回重试"""
        op_his: FixedInvestmentOperateHistory = FixedInvestmentOperateHistory.query.get(op_his_id)
        assert op_his.type == FixedInvestmentOperateHistory.Type.EARLY_REDEEM
        assert op_his.status in FixedInvestmentOperateHistory.PENDING_STATUES
        record: FixedInvestmentRecord = FixedInvestmentRecord.query.get(op_his.record_id)
        assert record.status == FixedInvestmentRecord.Status.REDEEMING
        product: FixedInvestmentProduct = FixedInvestmentProduct.query.get(record.product_id)

        with CacheLock(key=LockKeys.fixed_invest_product(product_id=product.id), wait=False):
            db.session.rollback()
            cls._do_early_redeem(product, record, op_his)

    @classmethod
    def retry_maturity_redeem_by_op_his(cls, op_his_id: int):
        """到期赎回重试"""
        op_his: FixedInvestmentOperateHistory = FixedInvestmentOperateHistory.query.get(op_his_id)
        assert op_his.type == FixedInvestmentOperateHistory.Type.MATURITY_REDEEM
        assert op_his.status in FixedInvestmentOperateHistory.PENDING_STATUES
        record: FixedInvestmentRecord = FixedInvestmentRecord.query.get(op_his.record_id)
        assert record.status == FixedInvestmentRecord.Status.REDEEMING
        product: FixedInvestmentProduct = FixedInvestmentProduct.query.get(record.product_id)
        interest_his_rows: list[FixedInvestmentInterestHistory] = FixedInvestmentInterestHistory.query.filter(
            FixedInvestmentInterestHistory.record_id == record.id,
            FixedInvestmentInterestHistory.status == FixedInvestmentInterestHistory.Status.CREATED,
        ).all()
        with CacheLock(key=LockKeys.fixed_invest_product(product_id=product.id), wait=False):
            db.session.rollback()
            cls._do_maturity_redeem(product, record, op_his, interest_his_rows)


class FixedInvestInterestHelper:
    """定期理财的利息逻辑"""

    @classmethod
    def new_interest_records(cls, record: FixedInvestmentRecord, now_: datetime = None) -> list[FixedInvestmentInterestHistory]:
        """生成存单的利息记录"""
        rows = []
        now_ = now_ or now()
        base_interest_amount, subsidy_interest_amount = record.real_interest_amounts
        interest_types = [
            FixedInvestmentInterestHistory.Type.BASE,
            FixedInvestmentInterestHistory.Type.SUBSIDY,
        ]
        for interest_type, interest_amount in zip(
            interest_types,
            [base_interest_amount, subsidy_interest_amount],
        ):
            if interest_amount > 0:
                interest_his = FixedInvestmentInterestHistory(
                    created_at=now_,
                    report_date=now_.date(),
                    record_id=record.id,
                    product_id=record.product_id,
                    user_id=record.user_id,
                    type=interest_type,
                    asset=record.asset,
                    amount=interest_amount,
                )
                db.session.add(interest_his)
                rows.append(interest_his)
        return rows

    @classmethod
    def update_user_summary_data(cls, end_date: date):
        """更新 定期理财-汇总收益"""
        last_summary: UserFixedInvestmentSummary = UserFixedInvestmentSummary.query.order_by(
            UserFixedInvestmentSummary.last_update_at.desc()
        ).with_entities(UserFixedInvestmentSummary.last_update_at).first()
        last_update_at = last_summary.last_update_at if last_summary else None
        last_update_date = last_update_at.date() if last_update_at else None
        if last_update_date and last_update_date >= end_date:
            return

        interest_q = FixedInvestmentInterestHistory.query.filter(
            FixedInvestmentInterestHistory.status == FixedInvestmentInterestHistory.Status.FINISHED,
            FixedInvestmentInterestHistory.report_date <= end_date,
        )
        if last_update_date:
            interest_q = interest_q.filter(FixedInvestmentInterestHistory.report_date > last_update_date)

        interest_rows = interest_q.group_by(
            FixedInvestmentInterestHistory.user_id,
            FixedInvestmentInterestHistory.asset
        ).with_entities(
            FixedInvestmentInterestHistory.user_id,
            FixedInvestmentInterestHistory.asset,
            func.sum(FixedInvestmentInterestHistory.amount).label("total_amount"),
        ).all()

        user_ids = set()
        interest_inc_amount_map = defaultdict(Decimal)
        for v in interest_rows:
            user_ids.add(v.user_id)
            interest_inc_amount_map[(v.user_id, v.asset)] += v.total_amount

        exist_sum_row_map = {}
        for ch_user_ids in batch_iter(user_ids, 5000):
            ch_sum_rows = UserFixedInvestmentSummary.query.filter(
                UserFixedInvestmentSummary.user_id.in_(ch_user_ids)
            ).all()
            exist_sum_row_map.update({(i.user_id, i.asset): i for i in ch_sum_rows})

        for _idx, (_key, _inc_amount) in enumerate(interest_inc_amount_map.items()):
            if _key not in exist_sum_row_map:
                _uid, _asset = _key
                _record = UserFixedInvestmentSummary(
                    user_id=_uid,
                    asset=_asset,
                    total_interest_amount=_inc_amount,
                    last_update_at=end_date,
                )
            else:
                _record: UserFixedInvestmentSummary = exist_sum_row_map[_key]
                _record.total_interest_amount += _inc_amount
                _record.last_update_at = end_date
            db.session.add(_record)

            if _idx and _idx % 5000 == 0:
                db.session.flush()

        db.session.commit()


class FixedInvestDataProc:
    """定期理财的资产余额数据"""

    @classmethod
    def get_dt_balance_snapshot(cls, report_hour: datetime) -> tuple[tuple]:
        """获取指定小时0分的用户理财余额快照"""
        ts = int(report_hour.replace(minute=0, second=0, microsecond=0).timestamp())
        try:
            table = TradeLogDB.slice_balance_table(ts, interval=3600)
        except Exception:  # noqa
            table = TradeLogDB.slice_balance_table(ts - 1800, interval=1800)
        if not table:
            current_app.logger.error(f"快照表不存在: {ts}")
            raise ValueError(f"快照表不存在: {ts}")

        invalid_result = table.select(
            "user_id",
            where=f"account={AssetInvestmentConfig.ACCOUNT_ID} and t in ({ServerBalanceType.FROZEN.value})",
        )
        if invalid_result:
            raise ValueError(f"理财账户存在冻结: {ts}")

        # 查询理财账户lock余额
        results = table.select(
            "user_id",
            "asset",
            "SUM(`balance`) `balance`",
            where=f"account={AssetInvestmentConfig.ACCOUNT_ID} and t = {ServerBalanceType.LOCK.value}",
            group_by="`user_id`, `asset`",
        )
        return results

    @classmethod
    def get_dt_asset_user_snap_map(cls, report_hour: datetime) -> dict[str, dict[int, Decimal]]:
        """获取指定小时0分的用户理财余额快照"""
        rows = cls.get_dt_balance_snapshot(report_hour)
        valid_asset = FixedInvestmentProduct.get_valid_assets()
        asset_user_balance = defaultdict(lambda: defaultdict(Decimal))
        for user_id, asset, balance in rows:
            if balance > 0 and asset in valid_asset:
                asset_user_balance[asset][user_id] = quantize_amount(Decimal(balance), 8)
        return asset_user_balance


class FixedInvestMsg:
    """触达相关"""

    @classmethod
    def _send_message(
        cls,
        user_id: int,
        title: MessageTitle,
        content: MessageContent,
        params: dict[str, Any],
        web_link: str = '',
        android_link: str = '',
        ios_link: str = '',
        expire_days: int = 3,
        display_type: Message.DisplayType = Message.DisplayType.POPUP_WINDOW,
        channel: Message.Channel = Message.Channel.SYSTEM,
    ) -> Message:
        message = Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=json.dumps(
                dict(
                    web_link=web_link,
                    android_link=android_link,
                    ios_link=ios_link,
                )
            ),
            display_type=display_type,
            expired_at=now() + timedelta(days=expire_days),
            channel=channel,
        )
        db.session.add(message)
        db.session.commit()
        return message

    @classmethod
    def send_early_redeemed(cls, record: FixedInvestmentRecord):
        """提前赎回通知"""
        msg_params = {
            "name": gettext("%(asset)s-%(lock_days)s天", asset=record.asset, lock_days=record.lock_days),
            "asset": record.asset,
            "amount": amount_to_str(record.amount),
            "need_translates": [
                {
                    'name': 'name',
                    'text': '%(asset)s-%(lock_days)s天',
                    'params': {'asset': record.asset, 'lock_days': record.lock_days},
                    'is_translate': True,
                }
            ]
        }
        cls._send_message(
            user_id=record.user_id,
            title=MessageTitle.FIXED_INVESTMENT_EARLY_REDEEMED,
            content=MessageContent.FIXED_INVESTMENT_EARLY_REDEEMED,
            params=msg_params,
        )

    @classmethod
    def send_maturity_redeemed(cls, record: FixedInvestmentRecord):
        """到期赎回通知"""
        msg_params = {
            "name": gettext("%(asset)s-%(lock_days)s天", asset=record.asset, lock_days=record.lock_days),
            "asset": record.asset,
            "amount": amount_to_str(record.amount),
            "interest_amount": amount_to_str(record.interest_amount),
            "interest_asset": record.asset,
            "need_translates": [
                {
                    'name': 'name',
                    'text': '%(asset)s-%(lock_days)s天',
                    'params': {'asset': record.asset, 'lock_days': record.lock_days},
                    'is_translate': True,
                }
            ]
        }
        cls._send_message(
            user_id=record.user_id,
            title=MessageTitle.FIXED_INVESTMENT_MATURITY_REDEEMED,
            content=MessageContent.FIXED_INVESTMENT_MATURITY_REDEEMED,
            params=msg_params,
        )


@celery_task
@lock_call(with_args=True)
def send_fixed_investment_product_interest_task(product_id: int):
    product: FixedInvestmentProduct = FixedInvestmentProduct.query.get(product_id)
    if not product:
        raise ValueError(f"product {product_id} not exist")

    now_ = now()
    q = FixedInvestmentRecord.query.filter(
        FixedInvestmentRecord.product_id == product_id,
        FixedInvestmentRecord.status == FixedInvestmentRecord.Status.HOLDING,
        FixedInvestmentRecord.maturity_time <= now_,
    )
    records: list[FixedInvestmentRecord] = q.with_entities(
        FixedInvestmentRecord.id,
    ).all()
    if not records:
        return

    record_ids = [i.id for i in records]
    with CacheLock(key=LockKeys.fixed_invest_product(product_id=product_id), wait=10):
        for record_id in record_ids:
            try:
                db.session.rollback()
                record = FixedInvestmentRecord.query.get(record_id)
                FixedInvestOperator.maturity_redeem(record, product, now_)
                db.session.commit()
            except Exception as _e:
                db.session.rollback()
                current_app.logger.exception(f"send_fixed_investment_product_interest_task {product_id} {record_id} error: {_e!r}")
