# -*- coding: utf-8 -*-
from enum import Enum
from decimal import Decimal
from typing import Optional, NamedTuple, Callable, Dict

from app.common import TradeType, OrderSideType, TradeBusinessType
from app.exceptions import InvalidArgument
from app.business import PerpetualServerClient, ORDER_BOTH_SIDE


class OrderTypeToBeCancelled(Enum):
    LIMIT = 'LIMIT'
    STOP = 'STOP'


class CancelRequestCollection(NamedTuple):
    cancel_batch_method: Callable
    cancel_one_method: Callable


class CancelOrderTool(object):

    @classmethod
    def build_order_type_request_collection(cls, _cancel_order_type: OrderTypeToBeCancelled) -> CancelRequestCollection:
        client = PerpetualServerClient()
        match _cancel_order_type:
            case OrderTypeToBeCancelled.LIMIT:
                return CancelRequestCollection(
                    cancel_batch_method=client.cancel_all,
                    cancel_one_method=client.cancel_order,
                )
            case OrderTypeToBeCancelled.STOP:
                return CancelRequestCollection(
                    cancel_batch_method=client.cancel_stop_all,
                    cancel_one_method=client.cancel_stop,
                )
        raise InvalidArgument

    def __init__(self, order_type: OrderTypeToBeCancelled, user_id: int, market: Optional[str],
                 side: Optional[OrderSideType]):
        self.order_type = order_type
        self.user_id = user_id
        self.order_type = order_type
        self.collection = self.build_order_type_request_collection(order_type)
        self.user_id = user_id
        self.market = market
        self.side = side or ORDER_BOTH_SIDE

    def cancel_all(self):
        self.collection.cancel_batch_method(self.user_id, self.market, self.side)

    def cancel_one(self, order_id):
        if not self.market:
            raise InvalidArgument
        self.collection.cancel_one_method(user_id=self.user_id,
                                          market=self.market,
                                          order_id=order_id)


def fetch_cal_fee(user_id: int, market: str) -> Dict[TradeType, Decimal]:
    """
    获取用户在合约市场的费率
    """
    from app.business.fee_constant import (
        DEFAULT_MIN_CONTRACT_TAKER_FEE,
        DEFAULT_MIN_CONTRACT_MAKER_FEE,
    )
    from app.business.fee import FeeFetcher

    fee_result = FeeFetcher(user_id).fetch(TradeBusinessType.PERPETUAL, market)
    return {
        TradeType.MAKER:
            max(fee_result[TradeType.MAKER],
                DEFAULT_MIN_CONTRACT_MAKER_FEE),
        TradeType.TAKER:
            max(fee_result[TradeType.TAKER],
                DEFAULT_MIN_CONTRACT_TAKER_FEE),
    }
