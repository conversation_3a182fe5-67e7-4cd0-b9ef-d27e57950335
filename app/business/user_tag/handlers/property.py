import copy
from collections import defaultdict
from datetime import date
from functools import partial
from typing import Optional, Dict, Set

from sqlalchemy import and_

from app.business import UserPreferences
from app.business.admin_tag import TagType as AdminTagType, AdminTagHelper
from app.business.user import UserRepository
from app.business.user_tag.handlers.base import (
    TagHandler, update_single_user_tag_data
)
from app.business.user_tag.helper import (
    get_disabled_user_ids, get_sub_account_mapping,
    get_all_user_ids, data_dumps,
    get_user_latest_data
)
from app.business.utils import yield_query_records_by_time_range
from app.common import Platform, list_country_codes_3_to_area, ShareUserTag
from app.models import (
    UserPreference, UserFavoriteAsset, ApiAuth, AmbassadorAgent, Ambassador, AmbassadorAgentHistory,
    MarketMaker, User, ReferralHistory, LoginHistory, ShortLinkInfo,
    PublicityChannel, db, ViaBTCPoolOrder, LoginRelationHistory,
    UserEx<PERSON>, <PERSON>ed<PERSON><PERSON>, SignOffUser
)
from app.models.admin_tag import Admin<PERSON>agUser
from app.models.copy_trading import CopyTraderUser
from app.models.user import UserWebAuthn
from app.models.user_tag import UserTag
from app.utils import batch_iter
from app.utils.config_ import _convert


class UserPreferencesTagHandler(TagHandler):
    priority = 998

    read_slot = frozenset([20])
    write_slot = frozenset([20])

    impl_tags = {
        UserTag.FAVORITE_ASSETS,
        UserTag.EMAIL_PUSH_TYPES,
        UserTag.LANGUAGE,
        UserTag.CET_DISCOUNT,
        UserTag.ENABLE_MARGIN,
        UserTag.ENABLE_PERPETUAL,
        UserTag.ENABLE_PERPETUAL_TIME,
        UserTag.ENABLE_PROFIT_LOSS,
        UserTag.ENABLE_API,
        UserTag.VIEW_PERPETUAL_TUTORIAL_VIDEO,
        UserTag.VIEW_PERPETUAL_TUTORIAL_QUESTION
    }

    def flush(self, report_date: Optional[date]):

        func_q = partial(
            yield_query_records_by_time_range,
            UserPreference,
            None,
            None,
            [
                UserPreference.created_at,
                UserPreference.user_id,
                UserPreference.key,
                UserPreference.value
            ],
            and_(
                UserPreference.key.in_(
                [
                    UserPreferences.language.name,
                    UserPreferences.allows_announcement_emails.name,
                    UserPreferences.allows_activity_emails.name,
                    UserPreferences.allows_blog_emails.name,
                    UserPreferences.opening_margin_function.name,
                    UserPreferences.opening_perpetual_trading.name,
                    UserPreferences.opening_account_profit_loss.name,
                    UserPreferences.cet_discount_enabled.name,
                    UserPreferences.view_perpetual_tutorial_video.name,
                    UserPreferences.view_perpetual_tutorial_question.name,
                ]
                ),
                UserPreference.status == UserPreference.Status.VALID
            ),
            50000
            )
        disabled_user_ids = get_disabled_user_ids()
        sub_user_mapping = get_sub_account_mapping()
        all_user_ids = get_all_user_ids()
        insert_user_ids = all_user_ids - disabled_user_ids - set(sub_user_mapping.keys())
        email_push_types_mapping = dict(zip([
            UserPreferences.allows_announcement_emails,
            UserPreferences.allows_activity_emails,
            UserPreferences.allows_blog_emails
        ],
            ["announcement", "activity", "blog"]))
        email_push_type_key_mapping = {k.name: v for k, v in email_push_types_mapping.items()}
        default_email_push_types = {v for k, v in email_push_types_mapping.items()
                                    if k.default is True}
        tag_default_value_mapping = {
            UserTag.EMAIL_PUSH_TYPES: default_email_push_types,
            UserTag.LANGUAGE: UserPreferences.language.default.name,
        }
        only_true_tags = {
            UserTag.CET_DISCOUNT,
            UserTag.ENABLE_MARGIN,
            UserTag.ENABLE_PERPETUAL,
            UserTag.ENABLE_PROFIT_LOSS,
            UserTag.ENABLE_API,
            UserTag.VIEW_PERPETUAL_TUTORIAL_VIDEO,
            UserTag.VIEW_PERPETUAL_TUTORIAL_QUESTION
        }
        flush_data = {
            _tag: {}
            for _tag in only_true_tags
        }
        flush_data[UserTag.ENABLE_PERPETUAL_TIME] = {}
        p_time_data = defaultdict(list)
        for _tag in (UserTag.EMAIL_PUSH_TYPES, UserTag.LANGUAGE):
            flush_data[_tag] = {
                    _uid: tag_default_value_mapping[_tag]
                    if isinstance(tag_default_value_mapping[_tag], str)
                    else copy.copy(tag_default_value_mapping[_tag]) for _uid in insert_user_ids
            }
        for v in func_q():
            if sub_user_mapping.get(v.user_id, v.user_id) not in insert_user_ids:
                continue
            simple_tag_mapping = {
                UserPreferences.cet_discount_enabled.name: UserTag.CET_DISCOUNT,
                UserPreferences.opening_margin_function.name: UserTag.ENABLE_MARGIN,
                UserPreferences.opening_perpetual_trading.name: UserTag.ENABLE_PERPETUAL,
                UserPreferences.opening_account_profit_loss.name: UserTag.ENABLE_PROFIT_LOSS,
                UserPreferences.view_perpetual_tutorial_video.name: UserTag.VIEW_PERPETUAL_TUTORIAL_VIDEO,
                UserPreferences.view_perpetual_tutorial_question.name: UserTag.VIEW_PERPETUAL_TUTORIAL_QUESTION
            }
            _value = v.value
            if v.key in simple_tag_mapping:
                tag = simple_tag_mapping[v.key]
                main_user_id = sub_user_mapping.get(v.user_id, v.user_id)
                convert_value = _convert(getattr(UserPreferences, v.key).type, v.value)
                if convert_value is True:
                    flush_data[tag][main_user_id] = convert_value
                    if tag == UserTag.ENABLE_PERPETUAL:
                        p_time_data[main_user_id].append(v.created_at.date())
            if v.key == UserPreferences.language.name:
                tag = UserTag.LANGUAGE
                if v.user_id in sub_user_mapping:
                    continue
                if v.value != tag_default_value_mapping[tag]:
                    flush_data[tag][v.user_id] = _value
            if v.key in [
                UserPreferences.allows_announcement_emails.name,
                UserPreferences.allows_activity_emails.name,
                UserPreferences.allows_blog_emails.name,
            ]:
                _mapping = {
                    UserPreferences.allows_announcement_emails.name: UserPreferences.allows_announcement_emails.default,
                    UserPreferences.allows_activity_emails.name: UserPreferences.allows_activity_emails.default,
                    UserPreferences.allows_blog_emails.name: UserPreferences.allows_blog_emails.default,
                }
                _enable_mapping = {
                    UserPreferences.allows_announcement_emails.name: True,
                    UserPreferences.allows_activity_emails.name: True,
                    UserPreferences.allows_blog_emails.name: True
                }

                if v.user_id in sub_user_mapping:
                    # 子账号无此设置
                    continue
                tag = UserTag.EMAIL_PUSH_TYPES
                _convert_value = _convert(getattr(UserPreferences, v.key).type, v.value)
                if _convert_value != _mapping[v.key] and _convert_value != _enable_mapping[v.key]:
                    flush_data[tag][v.user_id].discard(email_push_type_key_mapping[v.key])
        for _uid, _date_list in p_time_data.items():
            flush_data[UserTag.ENABLE_PERPETUAL_TIME][_uid] = min(_date_list)

        # UserFavoriteAsset
        user_favorite_asset_data = defaultdict(set)
        asset_query = UserFavoriteAsset.query.filter(
            UserFavoriteAsset.status == UserFavoriteAsset.StatusType.PASSED
        ).with_entities(UserFavoriteAsset.user_id, UserFavoriteAsset.asset)
        for v in asset_query:
            if v.user_id in disabled_user_ids:
                continue
            if sub_user_mapping.get(v.user_id, v.user_id) in disabled_user_ids:
                continue
            main_user_id = sub_user_mapping.get(v.user_id, v.user_id)
            user_favorite_asset_data[main_user_id].add(v.asset)
        flush_data[UserTag.FAVORITE_ASSETS] = dict()
        for user_id, assets in user_favorite_asset_data.items():
            flush_data[UserTag.FAVORITE_ASSETS][user_id] = assets
        # User API
        enable_api_users = {
            v.u_id for v in ApiAuth.query.with_entities(
                ApiAuth.user_id.distinct().label("u_id")).all()
        }
        for api_user_id in enable_api_users:
            if api_user_id in disabled_user_ids:
                continue
            main_user_id = sub_user_mapping.get(api_user_id, api_user_id)
            if main_user_id in disabled_user_ids:
                continue
            flush_data[UserTag.ENABLE_API][main_user_id] = True
        # convert tag enum to str
        convert_data = {
            _tag.name: [(_user_id, data_dumps(_value)) for _user_id, _value in _all_tag_data.items()]
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        only_true_tags = {
            UserTag.CET_DISCOUNT,
            UserTag.ENABLE_MARGIN,
            UserTag.ENABLE_PERPETUAL,
            UserTag.ENABLE_PROFIT_LOSS,
            UserTag.ENABLE_API,
            UserTag.VIEW_PERPETUAL_TUTORIAL_VIDEO,
            UserTag.VIEW_PERPETUAL_TUTORIAL_QUESTION
        }
        for tag_name, tag_data in new_data.items():
            if tag_name in (_tag.name for _tag in only_true_tags):
                continue
            update_single_user_tag_data(tag_name, tag_data)
        insert_records = []
        write_model = self.get_write_model()
        for tag_name, tag_data in new_data.items():
            if tag_name in (_tag.name for _tag in only_true_tags):
                for (_user_id, _value) in tag_data:
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=tag_name,
                            value=data_dumps(_value)
                        )
                    )
        write_model.query.filter(
            write_model.tag.in_([_tag.name for _tag in only_true_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        if report_date:
            self.mark_finished(report_date)


class UserIdentityTagHandler(TagHandler):
    priority = 999

    read_slot = frozenset([21])
    write_slot = frozenset([21])

    impl_tags = {
        UserTag.AMBASSADOR_AGENT,
        UserTag.AMBASSADOR,
        UserTag.BUSINESS_AMBASSADOR,
        UserTag.COPY_TRADER,
        UserTag.AMBASSADOR_SOURCE,
        UserTag.MARKET_MAKER,
        UserTag.INTERNAL_STAFF,
        UserTag.PLATFORM_USER,
        UserTag.INVALID_USER,
        UserTag.REFER_SOURCE,
        UserTag.HAS_KYC,
        UserTag.IS_REFERER,
        UserTag.PLATFORM,
        UserTag.AREA,
        UserTag.COUNTRY,
        UserTag.REGISTER_TIME,
        UserTag.USER_EMAIL,
        UserTag.PUBLICITY_CHANNEL,
        UserTag.SHARE_POP_WIN,
        UserTag.IS_POOL_USER,
        UserTag.IS_DEVICE_ID_UNIQUE_FOR_USER,
    }

    only_true_user_tags = (
        UserTag.AMBASSADOR_AGENT,
        UserTag.AMBASSADOR,
        UserTag.BUSINESS_AMBASSADOR,
        UserTag.MARKET_MAKER,
        UserTag.INTERNAL_STAFF,
        UserTag.COPY_TRADER,
        UserTag.HAS_KYC,
        UserTag.IS_REFERER,
        UserTag.IS_POOL_USER,
        UserTag.IS_DEVICE_ID_UNIQUE_FOR_USER,
        UserTag.PLATFORM_USER,
        UserTag.INVALID_USER,
    )

    def flush(self, report_date: Optional[date]):
        valid_main_user_ids = self.valid_main_user_ids()
        flush_data = self.init_flush_data(valid_main_user_ids)
        self.update_ambassador_tag_data(flush_data, valid_main_user_ids)
        self.update_ambassador_agent_tag_data(flush_data, valid_main_user_ids)
        self.update_market_maker_tag_data(flush_data, valid_main_user_ids)
        self.update_kyc_user_tag_data(flush_data, valid_main_user_ids)
        self.update_viabtc_pool_user_tag_data(flush_data, valid_main_user_ids)
        self.update_platform_user_tag_data(flush_data, valid_main_user_ids)
        self.update_invalid_user_tag_data(flush_data)

        self.update_referral_tags_data(flush_data, valid_main_user_ids)
        self.update_user_info_tags_data(flush_data, valid_main_user_ids)
        self.update_user_platform_tag_data(flush_data, valid_main_user_ids)
        self.update_device_id_unique_for_user_tag_data(flush_data, valid_main_user_ids)
        self.update_copy_trader_user_tag_data(flush_data, valid_main_user_ids)
        # convert tag enum to str
        convert_data = {
            _tag.name: {_user_id: data_dumps(_value) for _user_id, _value in _all_tag_data.items()}
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    @staticmethod
    def valid_main_user_ids() -> Set:
        disabled_user_ids = get_disabled_user_ids()
        sub_user_mapping = get_sub_account_mapping()
        all_user_ids = get_all_user_ids()
        return all_user_ids - disabled_user_ids - set(sub_user_mapping.keys())

    def init_flush_data(self, valid_main_user_ids) -> Dict:
        tag_default_value_mapping = {
            UserTag.REFER_SOURCE: "NONE",
            UserTag.PLATFORM: Platform.WEB.name,
            UserTag.USER_EMAIL: '',
        }
        flush_data = {
            _tag: {
                _uid: tag_default_value_mapping[_tag]
                for _uid in valid_main_user_ids
            }
            for _tag in tag_default_value_mapping.keys()
        }
        for _tag in self.only_true_user_tags:
            flush_data[_tag] = {}
        return flush_data

    @staticmethod
    def update_ambassador_tag_data(flush_data, valid_main_user_ids):
        from app.business.bus_referral import BusRelationUserQuerier
        from app.business.referral import TreeAmbHelper

        user_ids = set()
        normal_user_ids = set()  # 自然申请
        ambassador_user_ids = set()  # 大使推荐
        bus_amb_user_ids = {i.user_id for i in BusRelationUserQuerier.get_all_valid_bus_ambassadors()}  # 商务推荐
        tree_amb_user_ids = {i.user_id for i in TreeAmbHelper.get_all_valid_ambassadors()}
        bus_amb_user_ids.update(tree_amb_user_ids)
        invalid_user_ids = set()
        q = Ambassador.query.with_entities(Ambassador.user_id, Ambassador.type, Ambassador.status).all()
        agent_ambassador_ids = {
            row.ambassador_id
            for row in AmbassadorAgentHistory.query.filter(
                AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID
            ).with_entities(AmbassadorAgentHistory.ambassador_id).all()
        }
        for v in q:
            if v.status == Ambassador.Status.VALID:
                user_ids.add(v.user_id)
                if v.type == Ambassador.Type.BUSINESS:
                    continue
                else:
                    if v.user_id in agent_ambassador_ids:
                        ambassador_user_ids.add(v.user_id)
                    else:
                        normal_user_ids.add(v.user_id)
            else:
                invalid_user_ids.add(v.user_id)

        user_ids &= valid_main_user_ids
        normal_user_ids &= valid_main_user_ids
        ambassador_user_ids &= valid_main_user_ids
        bus_amb_user_ids &= valid_main_user_ids
        flush_data[UserTag.AMBASSADOR_SOURCE] = dict()
        for uid in user_ids:
            flush_data[UserTag.AMBASSADOR][uid] = True
            if uid in normal_user_ids:
                flush_data[UserTag.AMBASSADOR_SOURCE][uid] = "NORMAL"
            elif uid in ambassador_user_ids:
                flush_data[UserTag.AMBASSADOR_SOURCE][uid] = "AMBASSADOR"
            elif uid in bus_amb_user_ids:
                flush_data[UserTag.AMBASSADOR_SOURCE][uid] = "BUSINESS"
        for uid in invalid_user_ids:
            flush_data[UserTag.AMBASSADOR_SOURCE][uid] = ""
        bus_amb_user_ids &= valid_main_user_ids
        for uid in bus_amb_user_ids:
            flush_data[UserTag.BUSINESS_AMBASSADOR][uid] = True

    @staticmethod
    def update_ambassador_agent_tag_data(flush_data, valid_main_user_ids):
        am_agent_user_ids = {v.user_id for v in AmbassadorAgent.query.filter(
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID
        ).all()} & valid_main_user_ids
        for uid in am_agent_user_ids:
            flush_data[UserTag.AMBASSADOR_AGENT][uid] = True

    @staticmethod
    def update_market_maker_tag_data(flush_data, valid_main_user_ids):
        mm_user_ids = {v.user_id for v in MarketMaker.query.filter(
                MarketMaker.status == MarketMaker.StatusType.PASS
            ).all()
                      } & valid_main_user_ids
        for uid in mm_user_ids:
            flush_data[UserTag.MARKET_MAKER][uid] = True

    @staticmethod
    def update_kyc_user_tag_data(flush_data, valid_main_user_ids):
        kyc_user_ids = {v.id for v in User.query.filter(
                User.kyc_status == User.KYCStatus.PASSED
            ).with_entities(User.id).all()
                       } & valid_main_user_ids
        for uid in kyc_user_ids:
            flush_data[UserTag.HAS_KYC][uid] = True

    @staticmethod
    def update_viabtc_pool_user_tag_data(flush_data, valid_main_user_ids):
        pool_user_ids = {v.uid for v in ViaBTCPoolOrder.query.with_entities(
            ViaBTCPoolOrder.user_id.distinct().label("uid")).all()
        }
        pool_user_ids = pool_user_ids & valid_main_user_ids
        for uid in pool_user_ids:
            flush_data[UserTag.IS_POOL_USER][uid] = True

    @staticmethod
    def update_referral_tags_data(flush_data, valid_main_user_ids):
        am_refer_user_ids = set()  # 大使推荐
        not_am_refer_user_ids = set()  # 普通推荐
        referer_user_ids = set()  # 邀请人
        q = ReferralHistory.query.with_entities(ReferralHistory.referrer_id,
                                                ReferralHistory.referree_id,
                                                ReferralHistory.referral_type).all()
        for v in q:
            if v.referral_type == ReferralHistory.ReferralType.AMBASSADOR:
                am_refer_user_ids.add(v.referree_id)
            else:
                not_am_refer_user_ids.add(v.referree_id)
            referer_user_ids.add(v.referrer_id)
        am_refer_user_ids &= valid_main_user_ids
        not_am_refer_user_ids &= valid_main_user_ids
        referer_user_ids &= valid_main_user_ids
        for uid in am_refer_user_ids:
            flush_data[UserTag.REFER_SOURCE][uid] = "AMBASSADOR"
        for uid in not_am_refer_user_ids:
            flush_data[UserTag.REFER_SOURCE][uid] = "NORMAL"
        for uid in referer_user_ids:
            flush_data[UserTag.IS_REFERER][uid] = True

    @staticmethod
    def update_user_info_tags_data(flush_data, valid_main_user_ids):
        user_q = User.query.with_entities(
            User.id,
            User.created_at,
            User.location_code,
            User.email,
            User.channel
        ).all()
        publicity_channel_dic = PublicityChannel.get_all_pub_name_map()
        publicity_channels = list(publicity_channel_dic.values())
        short_link_mapping = {v.id: v for v in ShortLinkInfo.query.all()}
        code_3_to_area = list_country_codes_3_to_area()
        flush_data[UserTag.COUNTRY] = dict()
        flush_data[UserTag.AREA] = dict()
        flush_data[UserTag.REGISTER_TIME] = dict()
        flush_data[UserTag.PUBLICITY_CHANNEL] = dict()
        flush_data[UserTag.SHARE_POP_WIN] = dict()
        flush_data[UserTag.INTERNAL_STAFF] = dict()
        user_tags = ShareUserTag.get_share_tags()

        tag_users_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id == AdminTagType.INTERNAL_STAFF.value,
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).with_entities(AdminTagUser.user_id).all()
        for v in tag_users_query:
            if v.user_id not in valid_main_user_ids:
                continue
            flush_data[UserTag.INTERNAL_STAFF][v.user_id] = True

        for v in user_q:
            if v.id not in valid_main_user_ids:
                continue
            flush_data[UserTag.REGISTER_TIME][v.id] = v.created_at.date()
            if v.location_code:
                flush_data[UserTag.COUNTRY][v.id] = v.location_code
                if area := code_3_to_area.get(v.location_code):
                    flush_data[UserTag.AREA][v.id] = area.name
            if v.email and "@" in v.email:
                flush_data[UserTag.USER_EMAIL][v.id] = v.email
            if v.channel:
                channel = v.channel
                if channel in user_tags:
                    flush_data[UserTag.SHARE_POP_WIN][v.id] = channel

                if channel in publicity_channels:
                    flush_data[UserTag.PUBLICITY_CHANNEL][v.id] = channel
                else:
                    channel_lis = channel.split('a')
                    if len(channel_lis) > 1 and channel_lis[1].isdigit():
                        short_link_rec = short_link_mapping.get(int(channel_lis[1]))
                        if not short_link_rec:
                            continue
                        flush_data[UserTag.PUBLICITY_CHANNEL][v.id] = \
                            publicity_channel_dic[short_link_rec.publicity_channel_id]

    @staticmethod
    def update_platform_user_tag_data(flush_data, valid_main_user_ids):
        internal_user_ids = set(AdminTagHelper.get_tag_users(AdminTagType.INTERNAL_ACCOUNT)) & valid_main_user_ids
        for uid in internal_user_ids:
            flush_data[UserTag.PLATFORM_USER][uid] = True

    @staticmethod
    def update_invalid_user_tag_data(flush_data):
        # 清退用户
        cleared_user_ids = {u_id for u_id, in ClearedUser.query.filter(
            ClearedUser.valid.is_(True)
        ).with_entities(ClearedUser.user_id).all()}
        # 注销用户
        sign_off_user_ids = {u_id for u_id, in SignOffUser.query.with_entities(SignOffUser.user_id).all()}
        invalid_ids = (cleared_user_ids | sign_off_user_ids)
        for uid in invalid_ids:
            flush_data[UserTag.INVALID_USER][uid] = True
        # 风控标签中的失效用户
        invalid_user_ids = set(AdminTagHelper.get_tag_users(AdminTagType.INACTIVE_USER))
        for uid in invalid_user_ids:
            flush_data[UserTag.INVALID_USER][uid] = True

    @staticmethod
    def update_user_platform_tag_data(flush_data, valid_main_user_ids):
        records = get_user_latest_data(LoginHistory,
                                       [LoginHistory.user_id, LoginHistory.platform],
                                       )
        for v in records:
            if v["user_id"] not in valid_main_user_ids:
                continue
            _platform = v["platform"]
            if _platform in ["Android", "Android_GooglePlay"]:
                _platform = Platform.ANDROID
            elif _platform in ["iOS", "iOS_appstore", "iOSLite", "iOSLite_appstore"]:
                _platform = Platform.IOS
            else:
                _platform = Platform.WEB
            flush_data[UserTag.PLATFORM][v["user_id"]] = _platform.name

    @staticmethod
    def update_device_id_unique_for_user_tag_data(flush_data, valid_main_user_ids):
        sub_main_acc_dic = UserRepository.get_sub_main_acc_dic()
        duplicated_device_users = LoginRelationHistory.analysis_duplicate_device_by(sub_main_acc_dic)
        unique_device_id_users = valid_main_user_ids - duplicated_device_users
        for user_id in unique_device_id_users:
            flush_data[UserTag.IS_DEVICE_ID_UNIQUE_FOR_USER][user_id] = True

    @staticmethod
    def update_copy_trader_user_tag_data(flush_data, valid_main_user_ids):
        q = CopyTraderUser.query.with_entities(CopyTraderUser.user_id.distinct().label("u_id")).all()
        u_ids = {v.u_id for v in q}
        valid_u_ids = valid_main_user_ids & u_ids
        for user_id in valid_u_ids:
            flush_data[UserTag.COPY_TRADER][user_id] = True

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        self.update_normal_val_user_tag_data(new_data)
        self.update_only_true_user_tag_data(new_data)
        if report_date:
            self.mark_finished(report_date)

    def update_normal_val_user_tag_data(self, new_data):
        for tag_name, tag_data in new_data.items():
            if tag_name in (_tag.name for _tag in self.only_true_user_tags):
                continue
            update_single_user_tag_data(tag_name, tag_data)

    def update_only_true_user_tag_data(self, new_data):
        insert_records = []
        write_model = self.get_write_model()
        for tag_name, tag_data in new_data.items():
            if tag_name in (_tag.name for _tag in self.only_true_user_tags):
                for _user_id, _value in tag_data.items():
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=tag_name,
                            value=data_dumps(_value)
                        )
                    )
        write_model.query.filter(
            write_model.tag.in_([_tag.name for _tag in self.only_true_user_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()


class UserSafetyHandler(TagHandler):

    read_slot = frozenset([12])
    write_slot = frozenset([12])

    impl_tags = {
        UserTag.HAS_MOBILE,
        UserTag.HAS_TOTP,
        UserTag.HAS_WITHDRAWAL_PASSWORD,
        UserTag.HAS_TRADING_PASSWORD,
        UserTag.HAS_WEBAUTHN,
        UserTag.HAS_ANTI_PHISHING_CODE
    }

    def flush(self, report_date: Optional[date]):
        disabled_user_ids = get_disabled_user_ids()
        sub_user_mapping = get_sub_account_mapping()
        all_user_ids = get_all_user_ids()
        insert_user_ids = all_user_ids - disabled_user_ids - set(sub_user_mapping.keys())
        has_totp_user_ids = set()
        has_mobile_user_ids = set()
        has_withdrawal_password_user_ids = set()
        has_trading_password_user_ids = set()
        has_webauthn_user_ids = set()
        has_anti_phishing_code_user_ids = set()
        
        users = User.query.with_entities(User.id, User.mobile_num, User.totp_auth_key).all()
        user_extras = UserExtra.query.with_entities(
            UserExtra.user_id,
            UserExtra.withdraw_password_hash,
            UserExtra.trade_password_hash).all()
        user_preferences = UserPreference.query.filter(
                UserPreference.status == UserPreference.Status.VALID,
                UserPreference.key == UserPreferences.anti_phishing_code.name,
                ).with_entities(
                    UserPreference.user_id,
                    UserPreference.value,
                    UserPreference.key,
                ).all()
        user_webauthn_list = UserWebAuthn.query.filter(
            UserWebAuthn.status == UserWebAuthn.Status.VALID,
        ).with_entities(
            UserWebAuthn.user_id.distinct().label("user_id"),
        ).all()
        
        user_preference_map = {up.user_id: up for up in user_preferences}
        user_webauthn_map = {uw.user_id: uw for uw in user_webauthn_list}
        user_extra_map = {ue.user_id: ue for ue in user_extras}
        
        for user in users:
            if user.mobile_num:
                has_mobile_user_ids.add(user.id)
            if user.totp_auth_key:
                has_totp_user_ids.add(user.id)
            
            if user.id in user_preference_map:
                up = user_preference_map[user.id]
                if up.value:
                    has_anti_phishing_code_user_ids.add(user.id)
            
            if user.id in user_extra_map:
                ue = user_extra_map[user.id]
                if ue.trade_password_hash:
                    has_trading_password_user_ids.add(user.id)
                if ue.withdraw_password_hash:
                    has_withdrawal_password_user_ids.add(user.id)
            
            if user.id in user_webauthn_map:
                has_webauthn_user_ids.add(user.id)
        
        has_mobile_user_ids &= insert_user_ids
        has_totp_user_ids &= insert_user_ids
        has_withdrawal_password_user_ids &= insert_user_ids
        has_trading_password_user_ids &= insert_user_ids
        has_webauthn_user_ids &= insert_user_ids
        has_anti_phishing_code_user_ids &= insert_user_ids

        flush_data = {
            _tag: {}
            for _tag in self.impl_tags
        }
        for user_id in has_totp_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_TOTP][_main_user_id] = True
        for user_id in has_mobile_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_MOBILE][_main_user_id] = True
        for user_id in has_withdrawal_password_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_WITHDRAWAL_PASSWORD][_main_user_id] = True
        for user_id in has_trading_password_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_TRADING_PASSWORD][_main_user_id] = True
        for user_id in has_webauthn_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_WEBAUTHN][_main_user_id] = True
        for user_id in has_anti_phishing_code_user_ids:
            _main_user_id = sub_user_mapping.get(user_id, user_id)
            flush_data[UserTag.HAS_ANTI_PHISHING_CODE][_main_user_id] = True
        # convert tag enum to str
        convert_data = {
            _tag.name: [(_user_id, data_dumps(_value)) for _user_id, _value in _all_tag_data.items()]
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        write_model = self.get_write_model()
        insert_records = []
        for tag_name, tag_data in new_data.items():
            for (_user_id, _value) in tag_data:
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=tag_name,
                        value=data_dumps(_value)
                    )
                )
        write_model.query.filter(
            write_model.tag.in_([_tag.name for _tag in self.impl_tags])
        ).delete(synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        if report_date:
            self.mark_finished(report_date)
