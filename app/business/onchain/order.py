import contextlib
import json
from datetime import timedelta
from decimal import Decimal, ROUND_UP

from flask import current_app

from app import config
from app.business import ServerClient, ServerResponseCode, CacheLock, LockKeys, LockAssetHelper, PriceManager, \
    WalletClient, SiteSettings, UserSettings, UserPreferences, Locked, BusinessSettings
from app.business.clients.wallet import WalletOnchainTxStatus
from app.business.onchain.base import OnchainSettings
from app.business.onchain.client import OnchainSwapClient
from app.business.onchain.balance import OnchainTokenBalanceBiz
from app.business.onchain.fee import get_fee, get_gas_fee, get_fee_ratio
from app.business.onchain.message import send_order_finish_notice, send_stop_order_finish_notice
from app.business.onchain.token import get_token, is_support_swap
from app.business.onchain.token_blocklist import is_token_tradable
from app.business.onchain.utils import send_onchain_alert
from app.caches.onchain import OnchainUserRecent24HBuyCache, OnchainTokenQuoteCache
from app.common import BalanceBusiness
from app.common.onchain import Chain, OrderSide, CHAIN_MAIN_COIN_MAPPER, CHAIN_MONEY_MAPPING, CHAIN_EXCHANGER_ID_MAPPING
from app.exceptions import InvalidArgument, InsufficientAvailableBalance
from app.exceptions.onchain import OnchainExceptionMap, OnchainExceptionCode, OnchainTradeAmountTooSmall, \
    OnchainTradeAmountTooBig
from app.models import LockedAssetBalance, SubAccount
from app.models.onchain import db, OnchainOrder, OnchainOrderTransHistory, OnchainStopOrder, OnchainToken
from app.models.wallet import WithdrawalApprover
from app.utils import now
from app.utils.onchain import quantize_amount
from app.utils.parser import JsonEncoder


ZERO = Decimal(0)


@contextlib.contextmanager
def trans_lock_ctx(
        user_id: int, side: OrderSide, chain: Chain,
        token_id: int, token_amount: Decimal, money_asset: str, money_amount: Decimal,
        fee: Decimal, gas_fee: Decimal,
):
    his = OnchainTransBiz.lock(user_id, side, chain, token_id, token_amount, money_asset, money_amount, fee, gas_fee)
    try:
        yield his
    except Exception:
        db.session.rollback()
        OnchainTransBiz.to_unlock(his.id)
        raise


class OnchainOrderBiz:
    """
    链上交易市价订单
    """
    model = OnchainOrder
    his_model = OnchainOrderTransHistory

    EQUIPMENT_NO = 'coinexweb'

    @classmethod
    def _convert_params_to_swap_client(
            cls, side: OrderSide, chain: Chain, token_id: int, money_asset: str
    ):
        def chain_to_swap_client(c: str):
            chain_to_swap_client_mapper = {
                Chain.BSC.name: 'BNB',
                Chain.ERC20.name: 'ETH',
            }
            return chain_to_swap_client_mapper.get(c, c)

        convert_chain = chain_to_swap_client(chain.name)
        if money_asset != CHAIN_MAIN_COIN_MAPPER[chain]:
            money_contract = get_money_asset_contract(chain, money_asset)
            money_asset = f"{convert_chain}.{money_contract}"
        token = OnchainToken.query.get(token_id)
        if side == OrderSide.BUY:
            from_coin = money_asset
            to_coin = f"{convert_chain}.{token.contract}"
        else:
            from_coin = f"{convert_chain}.{token.contract}"
            to_coin = money_asset
        return from_coin, to_coin

    @classmethod
    def quote(
            cls, user_id: int, side: OrderSide, chain: Chain, token: OnchainToken, money_asset: str,
            from_amount: Decimal, slippage_limit: Decimal, type_: model.Type = model.Type.MARKET
    ):
        token_id = token.id
        if side == OrderSide.BUY:
            available = get_money_asset_available(user_id, chain, money_asset)
        else:
            available = OnchainTokenBalanceBiz.get_user_balance(user_id, token_id)["available"]
        gas_fee = get_gas_fee(chain, money_asset)
        fee_ratio = get_fee_ratio()
        daily_limit_buy_amount, daily_limit_buy_asset = get_daily_buy_limit(user_id)
        if not from_amount:
            return {
                'available': available,
                'expect_receive_amount': None,
                'min_receive_amount': None,
                'exchanger_id': None,
                'fee': None,
                'gas_fee': gas_fee,
                'fee_ratio': fee_ratio,
                'daily_limit_buy_asset': daily_limit_buy_asset,
                'daily_limit_buy_amount': daily_limit_buy_amount,
            }
        check_onchain_site_setting()
        check_token_enable(token)
        if type_ == cls.model.Type.MARKET:
            if side == OrderSide.BUY:
                check_money_trade_amount(chain, money_asset, from_amount, gas_fee)
            else:
                check_token_trade_amount(token_id, token.symbol, token.decimals, from_amount, money_asset, gas_fee)
        if not is_balance_enough(user_id, side, chain, token_id, money_asset, from_amount):
            raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]
        from_coin, to_coin = cls._convert_params_to_swap_client(side, chain, token_id, money_asset)
        fee = ZERO
        if side == OrderSide.BUY:
            from_decimals = get_money_asset_decimals(chain, money_asset)
            to_decimals = token.decimals
            fee = get_fee(chain, money_asset, from_amount)
            if not is_money_balance_enough(user_id, chain, money_asset, from_amount + fee + gas_fee):
                from_amount, fee = calc_real_money_amount(from_amount, gas_fee, fee_ratio, from_decimals)
        else:
            from_decimals = token.decimals
            to_decimals = get_money_asset_decimals(chain, money_asset)
        res = OnchainSwapClient().quote(
            cls.EQUIPMENT_NO, from_coin, to_coin, from_amount, from_decimals, slippage_limit,
            CHAIN_EXCHANGER_ID_MAPPING[chain]
        )
        if not res:
            raise OnchainExceptionMap[OnchainExceptionCode.TOKEN_NOT_SUPPORT_SWAP]
        res = sorted(res, key=lambda x: CHAIN_EXCHANGER_ID_MAPPING[chain].index(x['exchanger']['id']))
        exchanger_id = res[0]['exchanger']['id']
        to_amount = Decimal(res[0]['quote']['to_amount'])
        if side == OrderSide.SELL:
            fee = get_fee(chain, money_asset, to_amount)
            to_amount -= (gas_fee + fee)
            if to_amount < ZERO:
                to_amount = ZERO
        min_to_amount = cls._calc_min_to_amount(
            side, from_amount, to_amount, slippage_limit, to_decimals)
        return {
            'available': available,
            'expect_receive_amount': to_amount,
            'min_receive_amount': min_to_amount,
            'exchanger_id': exchanger_id,
            'fee': fee,
            'gas_fee': gas_fee,
            'fee_ratio': fee_ratio,
            'daily_limit_buy_asset': daily_limit_buy_asset,
            'daily_limit_buy_amount': daily_limit_buy_amount,
        }

    @classmethod
    def _calc_min_to_amount(
            cls, side: OrderSide, from_amount: Decimal, to_amount: Decimal, slippage_limit: Decimal,
            to_decimals: int):
        if side == OrderSide.BUY:
            price = from_amount / to_amount if to_amount else ZERO
            max_price = price * (Decimal(100) + slippage_limit) / Decimal(100)
            min_to_amount = from_amount / max_price if max_price else ZERO
        else:
            price = to_amount / from_amount if from_amount else ZERO
            min_price = price * (Decimal(100) - slippage_limit) / Decimal(100)
            min_to_amount = from_amount * min_price
        return quantize_amount(min_to_amount, to_decimals)

    @classmethod
    def create(
            cls, user_id: int, side: OrderSide, type_: model.Type,
            exchanger_id: str, chain: Chain, token: OnchainToken, money_asset: str,
            from_amount: Decimal, want_amount: Decimal, slippage_limit: Decimal,
            stop_order: OnchainStopOrder = None,
    ):
        """
        创建市价单
        1. 资金锁定，资金流水 history
        2. 创建兑换订单，获取兑换订单id、兑换合约地址、兑换参数
        3. 创建order并与history互相关联
        4. 创建链上订单
        """
        check_onchain_site_setting()
        check_user_onchain_enable(user_id)
        check_token_enable(token)
        token_id = token.id
        if not is_balance_enough(user_id, side, chain, token_id, money_asset, from_amount):
            raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]
        fee = get_fee(chain, money_asset, from_amount) if side == OrderSide.BUY else ZERO
        gas_fee = get_gas_fee(chain, money_asset)
        money_asset_price = PriceManager.asset_to_usd(money_asset)
        if side == OrderSide.BUY:
            token_amount, money_amount = ZERO, from_amount
            from_decimals = get_money_asset_decimals(chain, money_asset)
            to_decimals = token.decimals
        else:
            token_amount, money_amount = from_amount, ZERO
            from_decimals = token.decimals
            to_decimals = get_money_asset_decimals(chain, money_asset)
        if type_ == cls.model.Type.MARKET:
            if side == OrderSide.BUY:
                check_money_trade_amount(chain, money_asset, from_amount, gas_fee)
            else:
                check_token_trade_amount(token_id, token.symbol, token.decimals, from_amount, money_asset, gas_fee)
        with CacheLock(LockKeys.create_onchain_order(user_id), wait=True), \
                trans_lock_ctx(user_id, side, chain, token_id, token_amount, money_asset, money_amount, fee, gas_fee) as trans_his:
            if side == OrderSide.BUY:
                from_amount = Decimal(trans_his.money_amount)    # 内扣时需修正
                fee = trans_his.fee
            swap_order_id, to_amount, tx_data = cls._prepare_order(
                exchanger_id, side, chain, token_id, money_asset, from_amount, from_decimals, slippage_limit
            )
            min_to_amount = cls._calc_min_to_amount(
                side, from_amount, want_amount, slippage_limit, to_decimals)
            if to_amount < min_to_amount:
                raise OnchainExceptionMap[OnchainExceptionCode.PRICE_CHANGE_QUOTE_EXPIRED]
            order = cls._get_base_order(
                user_id, side, type_, chain, token_id, token_amount,
                money_asset, trans_his.money_amount, money_asset_price,
                want_amount, slippage_limit, fee, gas_fee,
                swap_order_id, json.dumps(tx_data, cls=JsonEncoder),
            )
            order.trans_id = trans_his.id
            trans_his.order_id = order.id
            if stop_order:
                stop_order.order_id = order.id
                stop_order.status = OnchainStopOrder.Status.ACTIVE
                stop_order.effected_at = now()
            db.session.commit()
            if order.side == OrderSide.BUY:
                add_buy_amount_to_cache(order.user_id, order.id, money_asset, Decimal(order.money_amount))
            if stop_order:
                send_stop_order_finish_notice.delay(stop_order.id)

        cls.submit_order(order, token, exchanger_id, to_amount)

    @classmethod
    def submit_order(cls, order: model, token: OnchainToken = None,
                     exchanger_id: str = None, to_amount: Decimal = None):
        if order.status != cls.model.Status.CREATED:
            return
        with CacheLock(LockKeys.onchain_order(order.id)):
            db.session.rollback()
            order = cls.model.query.get(order.id)
            if order.status != cls.model.Status.CREATED:
                return
            if not token:
                token = get_token(order.token_id)
            try:
                if not exchanger_id:
                    swap_order = OnchainSwapClient().get_order(order.swap_order_id)
                    exchanger_id = swap_order['exchanger_id']
                    to_amount = Decimal(swap_order['to_amt'])
                from_amount = order.money_amount if order.side == OrderSide.BUY else order.token_amount
                cls._submit_order(
                    order.id, exchanger_id, order.side, order.chain,
                    token.contract, order.money_asset, from_amount, to_amount,
                    order.slippage_limit, order.swap_order_id, json.loads(order.tx_data)
                )
                order.status = OnchainOrder.Status.PROCESSING
                db.session.commit()
            except Exception as e:
                current_app.logger.error('onchain submit order failed, e', e)

    @classmethod
    def _get_swap_from_to_address(cls, chain: Chain):
        chain_config = config['ONCHAIN_CONFIGS']['chain_config'][chain.name]
        return chain_config['swap_from_address'], chain_config['swap_to_address']

    @classmethod
    def _prepare_order(
            cls, exchanger_id: str, side: OrderSide,
            chain: Chain, token_id: int, money_asset: str,
            from_amount: Decimal, from_decimals: int, slippage_limit: Decimal
    ):
        """
        创建web3钱包订单
        """
        from_coin, to_coin = cls._convert_params_to_swap_client(
            side, chain, token_id, money_asset
        )
        swap_from_address, swap_to_address = cls._get_swap_from_to_address(chain)
        res = OnchainSwapClient().create_order(
            cls.EQUIPMENT_NO, exchanger_id, from_coin, to_coin, from_amount, from_decimals,
            swap_from_address, swap_to_address, slippage_limit
        )
        return res['id'], Decimal(res['to_amt']), res['tx_data']

    @classmethod
    def _get_base_order(
            cls, user_id: int, side: OrderSide, type_: model.Type,
            chain: Chain, token_id: int, token_amount: Decimal,
            money_asset: str, money_amount: Decimal, money_asset_price: Decimal,
            want_amount: Decimal, slippage_limit: Decimal, fee: Decimal, gas_fee: Decimal,
            swap_order_id: str, tx_data: str
    ) -> model:
        order = cls.model(
            user_id=user_id,
            side=side,
            type=type_,
            chain=chain,
            token_id=token_id,
            token_amount=token_amount,
            money_asset=money_asset,
            money_amount=money_amount,
            money_asset_price=money_asset_price,
            want_amount=want_amount,
            fee=fee,
            gas_fee=gas_fee,
            slippage_limit=slippage_limit,
            swap_order_id=swap_order_id,
            tx_data=tx_data,
        )
        db.session_add_and_flush(order)
        return order

    @classmethod
    def _submit_order(
            cls, order_id: int, exchanger_id: str, side: OrderSide, chain: Chain,
            token_contract: str, money_asset: str, from_amount: Decimal, to_amount: Decimal,
            slippage_limit: Decimal, swap_order_id: str, tx_data: dict):
        """提交订单到钱包"""
        swap_from_address, swap_to_address = cls._get_swap_from_to_address(chain)
        if money_asset == CHAIN_MAIN_COIN_MAPPER[chain]:
            convert_money_asset = ''
        else:
            convert_money_asset = get_money_asset_contract(chain, money_asset)
        if side == OrderSide.BUY:
            from_identity, to_identity = convert_money_asset, token_contract
        else:
            from_identity, to_identity = token_contract, convert_money_asset
        WalletClient().send_onchain_order(
            str(order_id),
            exchanger_id,
            chain.name,
            from_identity,
            from_amount,
            to_identity,
            to_amount,
            swap_from_address,
            swap_to_address,
            slippage_limit,
            swap_order_id,
            tx_data,
        )

    @classmethod
    def finish(cls, order_id: int):
        order: cls.model = cls.model.query.get(order_id)
        if order.status == cls.model.Status.PROCESSING:
            cls.sync_wallet_order_status(order)
        elif order.status == cls.model.Status.TO_FINISH:
            cls.change_balance(order)

    @classmethod
    def sync_wallet_order_status(cls, order: model):
        """同步钱包订单状态"""
        res = WalletClient().get_onchain_orders([str(order.id)])
        if not res:
            return
        res = res[0]
        cls.handle_wallet_order_status(
            order, res['tx_id'], res['received_amount'], res['confirmations'], getattr(WalletOnchainTxStatus, res['status'])
        )

    @classmethod
    def handle_wallet_order_status(
            cls, order: model, tx_id: str, received_amount: Decimal, confirmations: int, tx_status: WalletOnchainTxStatus):
        if order.status != cls.model.Status.PROCESSING:
            return
        if tx_status not in [WalletOnchainTxStatus.FINISHED, WalletOnchainTxStatus.CANCELLED]:
            return
        if received_amount is not None:
            received_amount = Decimal(received_amount)
        with CacheLock(LockKeys.onchain_order(order.id)):
            db.session.rollback()
            order: cls.model = cls.model.query.get(order.id)
            if order.status != cls.model.Status.PROCESSING:
                return
            order.status = cls.model.Status.TO_FINISH
            order.tx_id = tx_id
            if tx_status == WalletOnchainTxStatus.FINISHED:
                tx_result = cls.model.TxResult.SUCCESS
            else:
                if not confirmations:
                    tx_result = cls.model.TxResult.CANCELLED
                else:
                    tx_result = cls.model.TxResult.FAILED
            order.tx_result = tx_result
            history = cls.his_model.query.get(order.trans_id)
            if order.side == OrderSide.BUY:
                cls._handle_buy_order_status(order, history, received_amount)
            else:
                cls._handle_sell_order_status(order, history, received_amount)
            db.session.commit()

        cls.change_balance(order)

    @classmethod
    def _handle_buy_order_status(cls, order, history, received_amount):
        if order.tx_result == cls.model.TxResult.FAILED:
            order.fee = ZERO
        elif order.tx_result == cls.model.TxResult.CANCELLED:
            order.fee = ZERO
            order.gas_fee = ZERO
        else:
            order.token_amount = received_amount
            history.token_amount = received_amount

    @classmethod
    def _handle_sell_order_status(cls, order, history, received_amount):
        if order.tx_result in [cls.model.TxResult.FAILED, cls.model.TxResult.CANCELLED]:
            order.fee = ZERO
            order.gas_fee = ZERO
        else:
            if received_amount <= order.gas_fee:
                order.gas_fee = received_amount
                order.fee = ZERO
            else:
                fee = get_fee(order.chain, order.money_asset, received_amount)
                if received_amount <= order.gas_fee + fee:
                    order.fee = received_amount - order.gas_fee
                else:
                    order.fee = fee
            order.money_amount = received_amount - order.fee - order.gas_fee
            history.money_amount = received_amount - order.fee - order.gas_fee

    @classmethod
    def _finish_order(cls, order: model, status: model.Status):
        if status not in [cls.model.Status.FINISHED, cls.model.Status.FAILED, cls.model.Status.CANCELLED]:
            raise InvalidArgument(message='not onchain order final status')
        order.status = status
        if status == cls.model.Status.FINISHED:
            order.slippage = cls._calc_slippage(order)
        order.finished_at = now()
        db.session.commit()
        if order.side == OrderSide.BUY and order.status != cls.model.Status.FINISHED:
            cancel_buy_amount_to_cache(order.user_id, order.id)
        send_order_finish_notice.delay(order.id)

    @classmethod
    def _calc_slippage(cls, order: model):
        """计算实际滑点"""
        if order.side == OrderSide.BUY:
            want_price = Decimal(order.money_amount) / Decimal(order.want_amount) if Decimal(order.want_amount) else ZERO
        else:
            want_price = Decimal(order.want_amount) / Decimal(order.token_amount) if Decimal(order.token_amount) else ZERO
        real_price = Decimal(order.money_amount) / Decimal(order.token_amount) if Decimal(order.token_amount) else ZERO
        return (real_price - want_price) / want_price if want_price else ZERO

    @classmethod
    def _get_his_status(cls, his_id) -> his_model:
        return cls.his_model.query.get(his_id).status

    @classmethod
    def change_balance(cls, order: model):
        if order.status != cls.model.Status.TO_FINISH:
            return
        with CacheLock(LockKeys.onchain_order(order.id)):
            db.session.rollback()
            order = cls.model.query.get(order.id)
            if order.status != cls.model.Status.TO_FINISH:
                return
            if order.tx_result == cls.model.TxResult.SUCCESS:
                cls.unlock_and_sub_balance(order)
                cls.add_balance(order)
                if cls._get_his_status(order.trans_id) == cls.his_model.Status.FINISHED:
                    cls._finish_order(order, cls.model.Status.FINISHED)
            elif order.tx_result == cls.model.TxResult.FAILED:
                if order.side == OrderSide.BUY:
                    cls.unlock_and_sub_balance(order)   # sub gas fee
                else:
                    cls.unlock_balance(order)
                if cls._get_his_status(order.trans_id) == cls.his_model.Status.UNLOCKED:
                    cls._finish_order(order, cls.model.Status.FAILED)
            elif order.tx_result == cls.model.TxResult.CANCELLED:
                cls.unlock_balance(order)
                if cls._get_his_status(order.trans_id) == cls.his_model.Status.UNLOCKED:
                    cls._finish_order(order, cls.model.Status.CANCELLED)

    @classmethod
    def unlock_balance(cls, order: model):
        """"解锁"""
        OnchainTransBiz.to_unlock(order.trans_id)
        OnchainTransBiz.unlock(order.trans_id)

    @classmethod
    def unlock_and_sub_balance(cls, order: model):
        """"解锁并扣除"""
        OnchainTransBiz.to_unlock_and_sub(order.trans_id)
        OnchainTransBiz.unlock_and_sub(order.trans_id)

    @classmethod
    def add_balance(cls, order: model):
        """"
        增加资产（交易上链并成功；买入时增加token, 卖出时扣除平台手续费和gas费后再增加money）
        """
        OnchainTransBiz.add(order.trans_id)

    @classmethod
    def cancel_order(cls, order: model):
        """发送订单到钱包失败，取消"""
        if order.status != cls.model.Status.CREATED:
            return
        with CacheLock(LockKeys.onchain_order(order.id)):
            db.session.rollback()
            order = cls.model.query.get(order.id)
            if order.status != cls.model.Status.CREATED:
                return
            res = WalletClient().get_onchain_orders([str(order.id)])
            if res:
                order.status = cls.model.Status.PROCESSING
                db.session.commit()
            else:
                order.status = cls.model.Status.TO_FINISH
                order.tx_result = cls.model.TxResult.CANCELLED
                history = cls.his_model.query.get(order.trans_id)
                if order.side == OrderSide.BUY:
                    cls._handle_buy_order_status(order, history, None)
                else:
                    cls._handle_sell_order_status(order, history, None)
                db.session.commit()

        cls.finish(order.id)


class OnchainTransBiz:
    """
    链上交易订单资金流水
    """
    model = OnchainOrderTransHistory

    class RiskContent:
        UNLOCK_FAIL = "用户资金解锁失败"
        UNLOCK_SUB_FAIL = "用户资金解锁扣除失败"

    @classmethod
    def get_his(cls, his_id) -> model:
        return cls.model.query.get(his_id)

    @classmethod
    def get_order(cls, order_id) -> OnchainOrder:
        return OnchainOrder.query.get(order_id)

    @classmethod
    def lock(cls, user_id: int, side: OrderSide, chain: Chain,
             token_id: int, token_amount: Decimal, money_asset: str, money_amount: Decimal,
             fee: Decimal, gas_fee: Decimal):
        """
        买入时，参数money_amount是用户输入的数量，不是实际用来购买token的数量
        """
        balance_enough = True
        if side == OrderSide.BUY:
            if not is_money_balance_enough(user_id, chain, money_asset, money_amount + fee + gas_fee):
                if not is_money_balance_enough(user_id, chain, money_asset, money_amount):
                    balance_enough = False
                else:
                    fee_ratio = get_fee_ratio()
                    money_decimals = get_money_asset_decimals(chain, money_asset)
                    money_amount, fee = calc_real_money_amount(money_amount, gas_fee, fee_ratio, money_decimals)
        else:
            if not is_token_balance_enough(user_id, token_id, token_amount):
                balance_enough = False
        if not balance_enough:
            raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]

        his = cls.model(
            user_id=user_id,
            side=side,
            money_asset=money_asset,
            money_amount=money_amount,
            token_id=token_id,
            token_amount=token_amount,
            fee=fee,
            gas_fee=gas_fee
        )
        db.session.add(his)
        db.session.flush()
        if side == OrderSide.BUY:
            try:
                LockAssetHelper.lock(
                    business=LockedAssetBalance.Business.ONCHAIN,
                    business_id=his.id,
                    user_id=his.user_id,
                    asset=his.money_asset,
                    amount=his.money_amount + his.fee + his.gas_fee,
                )
                his.status = cls.model.Status.LOCKED
                db.session.commit()
            except InsufficientAvailableBalance:
                raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]
            except ServerClient.BadResponse as e:
                current_app.logger.error(f"onchain trans lock server error: {e}")
                if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                    his.status = cls.model.Status.FAILED
                    db.session.commit()
                    raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]
                else:
                    raise
        else:
            his.status = cls.model.Status.LOCKED
            OnchainTokenBalanceBiz.lock(his.user_id, his.token_id, Decimal(his.token_amount), commit=True)
        return his

    @classmethod
    def to_unlock(cls, his_id):
        """状态修改为TO_UNLOCK，并创建一笔资金解锁记录"""
        with CacheLock(LockKeys.onchain_order_trans(his_id)):
            db.session.rollback()
            his = cls.get_his(his_id)
            if his.status not in [cls.model.Status.CREATED, cls.model.Status.LOCKED]:
                return
            his.status = cls.model.Status.TO_UNLOCK
            if his.side == OrderSide.BUY:
                LockAssetHelper.unlock(
                    LockedAssetBalance.Business.ONCHAIN, his.id, his.user_id,
                    retry_type=LockedAssetBalance.RetryType.RETRY,
                )
            else:
                db.session.commit()

    @classmethod
    def unlock(cls, his_id):
        """资金解锁记录状态变更为已解锁后，变更资金流水状态"""
        with CacheLock(LockKeys.onchain_order_trans(his_id)):
            db.session.rollback()
            his = cls.get_his(his_id)
            if his.status != cls.model.Status.TO_UNLOCK:
                return
            if his.side == OrderSide.BUY:
                balance_row: LockedAssetBalance = LockAssetHelper.get_uni_row(
                    LockedAssetBalance.Business.ONCHAIN, his.id, his.user_id)
                if balance_row.status == LockedAssetBalance.Status.UNLOCKED:
                    his.status = cls.model.Status.UNLOCKED
                elif balance_row.status == LockedAssetBalance.Status.UNLOCK_FAIL:
                    his.status = cls.model.Status.FAILED
                    cls.handle_unlock_failed(his, cls.RiskContent.UNLOCK_FAIL)
                db.session.commit()
            else:
                his.status = cls.model.Status.UNLOCKED
                OnchainTokenBalanceBiz.unlock(his.user_id, his.token_id, Decimal(his.token_amount), commit=True)

    @classmethod
    def to_unlock_and_sub(cls, his_id):
        with CacheLock(LockKeys.onchain_order_trans(his_id)):
            db.session.rollback()
            his = cls.get_his(his_id)
            if his.status != cls.model.Status.LOCKED:
                return
            his.status = cls.model.Status.TO_UNLOCK_SUB
            if his.side == OrderSide.BUY:
                order = cls.get_order(his.order_id)
                if order.tx_result in [OnchainOrder.TxResult.SUCCESS, OnchainOrder.TxResult.FAILED]:
                    sub_amount = None if order.tx_result == OnchainOrder.TxResult.SUCCESS else his.gas_fee
                    LockAssetHelper.unlock_and_sub(
                        LockedAssetBalance.Business.ONCHAIN, his.id, his.user_id,
                        sub_amount=sub_amount,
                        retry_type=LockedAssetBalance.RetryType.RETRY
                    )
            else:
                db.session.commit()

    @classmethod
    def unlock_and_sub(cls, his_id):
        with CacheLock(LockKeys.onchain_order_trans(his_id)):
            db.session.rollback()
            his = cls.get_his(his_id)
            order = cls.get_order(his.order_id)
            if his.status != cls.model.Status.TO_UNLOCK_SUB:
                return
            if his.side == OrderSide.BUY:
                balance_row: LockedAssetBalance = LockAssetHelper.get_uni_row(
                    LockedAssetBalance.Business.ONCHAIN, his.id, his.user_id)
                if balance_row.status == LockedAssetBalance.Status.UNLOCKED:
                    if order.tx_result == OnchainOrder.TxResult.SUCCESS:
                        his.status = cls.model.Status.TO_ADD
                    else:
                        his.status = cls.model.Status.UNLOCKED
                elif balance_row.status == LockedAssetBalance.Status.UNLOCK_FAIL:
                    his.status = cls.model.Status.FAILED
                    cls.handle_unlock_failed(his, cls.RiskContent.UNLOCK_SUB_FAIL)
                db.session.commit()
            else:
                his.status = cls.model.Status.TO_ADD
                OnchainTokenBalanceBiz.unlock_and_sub(
                    his.user_id, his.token_id, Decimal(his.token_amount), Decimal(his.token_amount), commit=True)

    @classmethod
    def add(cls, his_id):
        """
        新增资产
        """
        with CacheLock(LockKeys.onchain_order_trans(his_id)):
            db.session.rollback()
            his = cls.get_his(his_id)
            if his.status != cls.model.Status.TO_ADD:
                return
            if his.side == OrderSide.BUY:
                his.status = cls.model.Status.FINISHED
                OnchainTokenBalanceBiz.add(his.user_id, his.token_id, Decimal(his.token_amount))
                db.session.commit()
            else:
                try:
                    ServerClient().add_user_balance(
                        his.user_id, his.money_asset, his.money_amount,
                        BalanceBusiness.ONCHAIN, his.id,
                    )
                    his.status = cls.model.Status.FINISHED
                    db.session.commit()
                except ServerClient.BadResponse as e:
                    current_app.logger.error(f"onchain trans lock server error: {e}")
                    if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                        his.status = cls.model.Status.FINISHED
                        db.session.commit()
                    else:
                        raise

    @classmethod
    def handle_unlock_failed(cls, his, base_content):
        content = f'{base_content}, trans_id: {his.id}, order_id: {his.order_id}'
        send_onchain_alert(content)


class OnchainStopOrderBiz:
    """
    链上交易计划市价订单
    """
    model = OnchainStopOrder

    @classmethod
    def create(
            cls, user_id: int, side: OrderSide,
            chain: Chain, token: OnchainToken, money_asset: str,
            from_amount: Decimal, want_amount: Decimal,
            trigger_type: model.TriggerType, trigger_amount: Decimal, trigger_unit: str,
            slippage_limit: Decimal, operator: model.Operator,
    ):
        """
        创建计划市价单
        """
        check_onchain_site_setting()
        check_user_onchain_enable(user_id)
        check_token_enable(token)
        token_id = token.id
        gas_fee = get_gas_fee(chain, money_asset)
        if side == OrderSide.BUY:
            check_money_trade_amount(chain, money_asset, from_amount, gas_fee)
        else:
            check_token_trade_amount(token_id, token.symbol, token.decimals, from_amount, money_asset, gas_fee)
        if not is_balance_enough(user_id, side, chain, token_id, money_asset, from_amount):
            raise OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]
        row = cls.model(
            user_id=user_id,
            side=side,
            chain=chain,
            token_id=token_id,
            money_asset=money_asset,
            from_amount=from_amount,
            want_amount=want_amount,
            trigger_type=trigger_type,
            trigger_amount=trigger_amount,
            trigger_unit=trigger_unit,
            operator=operator,
            slippage_limit=slippage_limit,
            expired_at=now() + timedelta(days=30)
        )
        db.session_add_and_commit(row)

    @classmethod
    def cancel(cls, stop_order_id: int):
        """
        取消计划市价单
        """
        with CacheLock(LockKeys.onchain_stop_order(stop_order_id)):
            db.session.rollback()
            row = cls.model.query.filter(
                cls.model.id == stop_order_id,
                cls.model.status == cls.model.Status.CREATED
            ).first()
            if not row:
                return
            row.status = cls.model.Status.CANCELLED
            row.cancel_type = cls.model.CancelType.USER
            db.session.commit()
            send_stop_order_finish_notice.delay(stop_order_id)

    @classmethod
    def batch_cancel(cls, user_id: int, token_id: int = None):
        query = cls.model.query.filter(
            cls.model.user_id == user_id,
            cls.model.status == cls.model.Status.CREATED
        )
        if token_id:
            query = query.filter(
                cls.model.token_id == token_id
            )
        rows = query.with_entities(
            cls.model.id
        ).all()
        for row in rows:
            cls.cancel(row.id)

    @classmethod
    def expire(cls, stop_order_id: int):
        """
        计划市价单过期
        """
        with CacheLock(LockKeys.onchain_stop_order(stop_order_id)):
            db.session.rollback()
            row = cls.model.query.filter(
                cls.model.id == stop_order_id,
                cls.model.status == cls.model.Status.CREATED
            ).first()
            if not row:
                raise InvalidArgument
            row.status = cls.model.Status.CANCELLED
            row.cancel_type = cls.model.CancelType.TIMEOUT
            db.session.commit()
            send_stop_order_finish_notice.delay(stop_order_id)

    @classmethod
    def trigger(cls, stop_order_id: int):
        """
        触发计划市价单
        """
        with CacheLock(LockKeys.onchain_stop_order(stop_order_id)):
            db.session.rollback()
            row: cls.model = cls.model.query.filter(
                cls.model.id == stop_order_id,
                cls.model.status == cls.model.Status.CREATED
            ).first()
            if not row:
                return

            from_amount, want_amount = Decimal(row.from_amount), Decimal(row.want_amount)
            try:
                token = get_token(row.token_id)
                quote_res = OnchainOrderBiz.quote(
                    row.user_id, row.side, row.chain, token, row.money_asset, from_amount,
                    row.slippage_limit, OnchainOrder.Type.LIMIT
                )
                if not quote_res:
                    return
                if quote_res['expect_receive_amount'] < cls._calc_min_to_amount(row):
                    raise OnchainExceptionMap[OnchainExceptionCode.LIMIT_RECEIVE_TOO_SMALL]
                OnchainOrderBiz.create(
                    row.user_id,
                    row.side,
                    OnchainOrder.Type.LIMIT,
                    quote_res['exchanger_id'],
                    row.chain,
                    token,
                    row.money_asset,
                    from_amount,
                    want_amount,
                    row.slippage_limit,
                    row,
                )
            except Locked as e:
                if e.data != LockKeys.create_onchain_order(row.user_id):
                    current_app.logger.warning('onchain trigger failed, e:', e)
                    row.status = cls.model.Status.FAILED
                    db.session.commit()
                    send_stop_order_finish_notice.delay(stop_order_id)
            except Exception as e:
                fail_type = cls._handle_exception(e)
                if not fail_type:
                    current_app.logger.warning('onchain trigger failed, e:', e)
                row.status = cls.model.Status.FAILED
                row.fail_type = fail_type
                db.session.commit()
                send_stop_order_finish_notice.delay(stop_order_id)

    @classmethod
    def _handle_exception(cls, exception: Exception):
        exception_handlers = {
            OnchainExceptionMap[OnchainExceptionCode.ONCHAIN_SITE_DISABLED]: cls.model.FailType.SITE_DISABLED,
            OnchainExceptionMap[OnchainExceptionCode.NOT_OPEN_ONCHAIN_FUNCTION]: cls.model.FailType.FORBIDDEN,
            OnchainExceptionMap[OnchainExceptionCode.FORBID_ONCHAIN_TRADING]: cls.model.FailType.FORBIDDEN,
            OnchainExceptionMap[OnchainExceptionCode.SET_WITHDRAWAL_APPROVER]: cls.model.FailType.FORBIDDEN,
            OnchainExceptionMap[OnchainExceptionCode.BALANCE_NOT_ENOUGH]: cls.model.FailType.NOT_ENOUGH,
            OnchainExceptionMap[OnchainExceptionCode.TOKEN_NOT_SUPPORT_SWAP]: cls.model.FailType.TOKEN_FORBIDDEN,
            OnchainExceptionMap[OnchainExceptionCode.TOKEN_IN_BLOCKLIST]: cls.model.FailType.TOKEN_FORBIDDEN,
            OnchainExceptionMap[OnchainExceptionCode.PRICE_CHANGE_QUOTE_EXPIRED]: cls.model.FailType.QUOTE_EXPIRED,
            OnchainExceptionMap[OnchainExceptionCode.LIMIT_RECEIVE_TOO_SMALL]: cls.model.FailType.RECEIVE_TOO_SMALL,
            OnchainTradeAmountTooSmall: cls.model.FailType.AMOUNT_TOO_SMALL,
            OnchainTradeAmountTooBig: cls.model.FailType.AMOUNT_TOO_BIG,
        }
        for exc_type, fail_type in exception_handlers.items():
            if isinstance(exception, exc_type):
                return fail_type
        return None

    @classmethod
    def _calc_min_to_amount(cls, stop_order: model):
        want_amount = Decimal(stop_order.want_amount)
        return want_amount * (Decimal(100) - stop_order.slippage_limit) / Decimal(100)


def check_onchain_site_setting():
    if not SiteSettings.onchain_trading_enabled:
        raise OnchainExceptionMap[OnchainExceptionCode.ONCHAIN_SITE_DISABLED]


def check_user_onchain_enable(user_id: int):
    if not is_user_onchain_trade_enable(user_id):
        raise OnchainExceptionMap[OnchainExceptionCode.FORBID_ONCHAIN_TRADING]
    if not is_user_open_onchain_function(user_id):
        raise OnchainExceptionMap[OnchainExceptionCode.NOT_OPEN_ONCHAIN_FUNCTION]
    if has_user_set_withdrawal_approval(user_id):
        raise OnchainExceptionMap[OnchainExceptionCode.SET_WITHDRAWAL_APPROVER]
    if not is_user_onchain_trade_enable(get_main_user_id(user_id)):
        raise OnchainExceptionMap[OnchainExceptionCode.FORBID_ONCHAIN_TRADING]
    if has_user_set_withdrawal_approval(get_main_user_id(user_id)):
        raise OnchainExceptionMap[OnchainExceptionCode.SET_WITHDRAWAL_APPROVER]


def is_user_open_onchain_function(user_id: int):
    return UserPreferences(user_id).opening_onchain_trade_function


def is_user_onchain_trade_enable(user_id: int):
    return not UserSettings(user_id).onchain_trading_disabled_by_admin


def has_user_set_withdrawal_approval(user_id: int):
    # 是否设置了多人提现
    row = WithdrawalApprover.query.filter(
        WithdrawalApprover.user_id == user_id,
        WithdrawalApprover.is_self.is_(False),
        WithdrawalApprover.status == WithdrawalApprover.Status.VALID,
    ).first()
    return bool(row)


def check_token_enable(token: OnchainToken):
    if not is_support_swap(token.chain, token.contract):
        raise OnchainExceptionMap[OnchainExceptionCode.TOKEN_NOT_SUPPORT_SWAP]
    tradable, _ = is_token_tradable(token.chain, token.contract)
    if not tradable:
        raise OnchainExceptionMap[OnchainExceptionCode.TOKEN_IN_BLOCKLIST]


def check_money_trade_amount(chain: Chain, money_asset: str, money_amount: Decimal, gas_fee: Decimal):
    # 买入时，最小买入金额为 min_trade_usdt USDT+gas费
    min_trade_usdt = Decimal(config['ONCHAIN_CONFIGS']['min_trade_usdt'])
    decimals = get_money_asset_decimals(chain, money_asset)
    min_trade_amount = quantize_amount(
        min_trade_usdt * PriceManager.asset_to_asset('USDT', money_asset), decimals, rounding=ROUND_UP) + gas_fee
    if money_amount < min_trade_amount:
        raise OnchainTradeAmountTooSmall({"amount": min_trade_amount, 'asset': money_asset})
    max_money_amount = OnchainSettings.get_quantity_per_order_limit_value(chain, money_asset)
    if money_amount > max_money_amount:
        raise OnchainTradeAmountTooBig({"amount": max_money_amount, 'asset': money_asset})


def check_token_trade_amount(token_id: int, token_symbol: str, token_decimals: int,
                             token_amount: Decimal, money_asset: str, gas_fee: Decimal):
    token_price = OnchainTokenQuoteCache().get_one(token_id).get('price', ZERO)
    min_trade_usd = gas_fee * PriceManager.asset_to_usd(money_asset)
    min_trade_amount = min_trade_usd / token_price if token_price else ZERO
    min_trade_amount = quantize_amount(min_trade_amount, token_decimals, rounding=ROUND_UP)
    if token_amount < min_trade_amount:
        raise OnchainTradeAmountTooSmall({"amount": min_trade_amount, 'asset': token_symbol})


def is_balance_enough(user_id: int, side: OrderSide, chain: Chain, token_id: int, money_asset: str, from_amount: Decimal):
    balance_enough = True
    if side == OrderSide.BUY:
        if not is_money_balance_enough(user_id, chain, money_asset, from_amount):
            balance_enough = False
    else:
        if not is_token_balance_enough(user_id, token_id, from_amount):
            balance_enough = False
    return balance_enough


def is_token_balance_enough(user_id: int, token_id: int, amount: Decimal):
    balance = OnchainTokenBalanceBiz.get_user_balance(user_id, token_id)
    return balance["available"] >= amount


def is_money_balance_enough(user_id: int, chain: Chain, money_asset: str, amount: Decimal):
    available = get_money_asset_available(user_id, chain, money_asset)
    return available >= amount


def get_daily_buy_limit(user_id: int):
    limit = BusinessSettings.onchain_daily_buy_default_limit
    if user_id:
        if user_limit := UserPreferences(user_id).onchain_daily_buy_limit:
            limit = user_limit
    return limit, 'USDT'


def get_money_asset_available(user_id, chain: Chain, money_asset: str):
    """可用数量：min(每日链上买入额度1000USDT，该money币可用数量），不同money币需要换算不同数量"""
    decimals = get_money_asset_decimals(chain, money_asset)
    balance = ServerClient().get_user_balances(user_id, money_asset)[money_asset]
    limit_buy_amount, limit_buy_asset = get_daily_buy_limit(user_id)
    main_user_id = get_main_user_id(user_id)
    consumed = OnchainUserRecent24HBuyCache(main_user_id).get_consumed()
    left_usd = limit_buy_amount - consumed if limit_buy_amount > consumed else ZERO
    price = PriceManager.asset_to_asset(money_asset, limit_buy_asset)
    left_amount = left_usd / price
    available = min(left_amount, balance["available"])
    return quantize_amount(available, decimals)


def add_buy_amount_to_cache(user_id, order_id, asset, amount):
    main_user_id = get_main_user_id(user_id)
    _, limit_buy_asset = get_daily_buy_limit(user_id)
    amount = amount * PriceManager.asset_to_asset(asset, limit_buy_asset)
    OnchainUserRecent24HBuyCache(main_user_id).add_amount(order_id, amount)


def cancel_buy_amount_to_cache(user_id, order_id):
    main_user_id = get_main_user_id(user_id)
    OnchainUserRecent24HBuyCache(main_user_id).cancel_amount(order_id)


def get_main_user_id(user_id: int):
    row = SubAccount.query.filter(
        SubAccount.user_id == user_id
    ).first()
    return row.main_user_id if row else user_id


def get_money_asset_decimals(chain: Chain, money_asset: str):
    return CHAIN_MONEY_MAPPING[chain][money_asset]['decimals']


def get_money_asset_contract(chain: Chain, money_asset: str):
    return CHAIN_MONEY_MAPPING[chain][money_asset]['contract']


def calc_real_money_amount(money_amount: Decimal, gas_fee: Decimal, fee_ratio: Decimal, money_decimals: int):
    """内扣时修正买入数量和费用"""
    raw_money_amount = money_amount
    money_amount = quantize_amount((money_amount - gas_fee) / (1 + fee_ratio), money_decimals)
    fee = raw_money_amount - money_amount - gas_fee
    return money_amount, fee
