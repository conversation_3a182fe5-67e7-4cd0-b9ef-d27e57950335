import datetime
import json
from decimal import Decimal
from functools import wraps

from flask import current_app

from app import config
from app.business import PerpetualServerClient, \
    send_alert_notice, CacheLock, LockKeys, SiteSettings, UserSettings
from app.utils.logs import log_func_consume
from app.business.copy_trading.message import FollowerMessageSender, \
    send_follower_add_position_success_notice, send_follower_close_position_success_notice
from app.business.copy_trading.trader import CopyTraderManager
from app.business.order import Order, VerifyPriceTool
from app.caches import PerpetualMarketCache
from app.common import TradeType, OrderSideType, OrderBusinessType, OrderType, PositionSide
from app.exceptions import PerpetualResponseCode, InvalidArgument, \
    PerpetualTradingLimited, ErrorWithResponseCode, OrderException
from app.models import db, User
from app.models.copy_trading import CopyFollowerOrderOperation, \
    CopyTraderPositionChangeRecord, CopyFollowerHistory, CopyTradingMarket, \
    CopyTraderProfitShareDetail, CopyTraderUser
from app.utils import amount_to_str, now, quantize_amount, WhyNot
from app.utils.parser import JsonEncoder


def validate_and_update_status(failure_status):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            fail_reason, fail_reason_msg = func(self, *args, **kwargs)
            if not fail_reason:
                self.follower_op.status = failure_status
                self.follower_op.fail_reason = fail_reason_msg
                db.session.commit()
                return False
            return fail_reason
        return wrapper
    return decorator


class PositionOperation:
    source = Order.OrderSourceType.SYSTEM.value
    client = PerpetualServerClient()

    def __init__(self, op_id: int):
        self.op_id = op_id
        self.follower_op = self.get_follower_op()
        self.follower_his = self.get_follower_his()
        self.sub_user_id = self.follower_op.sub_user_id
        self.main_user_id = self.follower_op.user_id
        self.trader_op = self.get_trader_op()
        self.trader_user_balance = self.get_user_balance(self.trader_op.sub_user_id)
        self.follower_user_balance = self.get_user_balance(self.follower_op.sub_user_id)
        self.position_pending_list = self.get_position_pending_list()
        self.market_setting = self.get_market_setting()
        self.market_info = self.get_market_info()

    def validate(self):
        raise NotImplementedError

    def process(self):
        raise NotImplementedError
    
    def notice_success_msg(self):
        raise NotImplementedError

    def is_operation_finished(self):
        if self.follower_op.status != CopyFollowerOrderOperation.Status.CREATED:
            return True
        return False

    @log_func_consume
    def get_follower_op(self):
        row = CopyFollowerOrderOperation.query.get(self.op_id)
        return row

    @log_func_consume
    def get_trader_op(self):
        row = CopyTraderPositionChangeRecord.query.get(self.follower_op.change_record_id)
        return row

    @log_func_consume
    def get_follower_his(self):
        follower_his = CopyFollowerHistory.query.get(self.follower_op.follow_history_id)
        return follower_his

    def get_trader_cur_follower_num(self):
        row = CopyTraderUser.query.filter(
            CopyTraderUser.user_id == self.trader_op.user_id
        ).with_entities(
            CopyTraderUser.cur_follower_num
        ).first()
        return row.cur_follower_num

    @log_func_consume
    def get_user_balance(self, user_id):
        _asset = CopyTraderProfitShareDetail.PROFIT_ASSET
        return self.client.get_user_balances(user_id)[_asset]

    @log_func_consume
    def get_balance_rate(self):
        trader_user_balance = self.trader_user_balance
        follower_user_balance = self.follower_user_balance
        total_trader_user_balance = trader_user_balance['available'] + \
                                    trader_user_balance['margin'] + \
                                    trader_user_balance['profit_unreal']
        total_follower_user_balance = follower_user_balance['available'] + \
                                      follower_user_balance['margin'] + \
                                      follower_user_balance['profit_unreal']
        rate = total_follower_user_balance / total_trader_user_balance if total_trader_user_balance else 0
        return quantize_amount(rate, 4)

    @log_func_consume
    def get_position_pending_list(self):
        return self.client.position_pending(self.sub_user_id, self.follower_op.market)

    def get_market_setting(self):
        market_setting = CopyTradingMarket.query.filter(
            CopyTradingMarket.market == self.trader_op.market,
            CopyTradingMarket.status == CopyTradingMarket.Status.ONLINE,
        ).first()
        if not market_setting:
            raise InvalidArgument(message=f'CopyTradingMarket not get {self.trader_op.market}')
        return market_setting

    def get_market_info(self):
        market_info = PerpetualMarketCache().get_market_info(self.trader_op.market)
        if not market_info:
            raise InvalidArgument(message=f'PerpetualMarketCache not get {self.trader_op.market}')
        return market_info

    @log_func_consume
    def get_user_fee(self, user_id, market):
        from app.business.perpetual.order import fetch_cal_fee

        return fetch_cal_fee(user_id, market)

    @property
    def sell_or_buy(self):
        if self.trader_op.side == PositionSide.SHORT:
            return OrderSideType.BUY if self.trader_op.amount_delta < 0 else OrderSideType.SELL
        else:
            return OrderSideType.BUY if self.trader_op.amount_delta > 0 else OrderSideType.SELL

    @property
    def slippage_price(self):
        slippage_limit = self.market_setting.slippage_limit
        avg_price = self.trader_op.avg_price
        if self.trader_op.side == PositionSide.SHORT:
            return avg_price * (1-slippage_limit)
        else:
            return avg_price * (1+slippage_limit)

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_min_order_amount(self, order_amount):
        amount_min = Decimal(self.market_info['amount_min'])
        return amount_min <= order_amount, PerpetualResponseCode.CONTRACT_LEAST_AMOUNT.name

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_market_setting(self):
        return bool(self.market_setting), 'INVALID_MARKET_SETTING'

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_follower_status(self):
        return self.follower_his.status == CopyFollowerHistory.Status.FOLLOWING, 'INVALID_FOLLOWER_STATUS'

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_operation_expired(self):
        cur_follower_num = self.get_trader_cur_follower_num()
        threshold = 100
        second_limit = (cur_follower_num//threshold+1) * 60
        msg_sent_at = self.trader_op.msg_sent_at
        now_ = now()
        is_not_expired = msg_sent_at + datetime.timedelta(seconds=second_limit) > now_
        if not is_not_expired:
            msg = (f"跟单交易超时告警：用户main_user_id:{self.main_user_id};sub_uer_id:{self.sub_user_id}"
                   f"存在市场{self.trader_op.market}操作超时大于{second_limit}秒"
                   f"msg_sent_at：{msg_sent_at}, now:{now_}")
            send_alert_notice(msg, config["ADMIN_CONTACTS"].get('copy_trading', ''))
        return is_not_expired, 'CONTRACT_OPERATION_EXPIRED'

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_trade_permission(self, is_closing_or_reducing_position=False):
        user_setting = UserSettings(self.main_user_id)
        condition = [SiteSettings.trading_enabled,
                     SiteSettings.perpetual_trading_enable,
                     user_setting.perpetual_trading_enabled]
        if not is_closing_or_reducing_position:
            condition.append(WhyNot(not user_setting.perpetual_limited,
                                    PerpetualTradingLimited))
        for c in condition:
            if not c:
                return False, 'CONTRACT_FORBID_TRADING'
        return True, ''

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_position_existed(self):
        return bool(self.position_pending_list), PerpetualResponseCode.CONTRACT_POSITION_NOT_EXISTS.name

    def get_follower_order_amount(self):
        balance_rate = self.get_balance_rate()
        amount = balance_rate * self.trader_op.amount_delta
        return abs(amount)

    @log_func_consume
    def find_order_by_client_id(self, client_id):
        """ 根据client_id查询对应订单 """
        # 取交易员仓位创建的时间作为开始时间
        start_ts = int(self.trader_op.position_created_at.timestamp())
        end_ts = 0
        page = 1
        user_id = self.sub_user_id
        market = self.trader_op.market
        while True:
            page_orders = self.client.order_finished(
                user_id=user_id,
                market=market,
                side=self.sell_or_buy,
                start_time=start_ts,
                end_time=end_ts,
                page=page,
                limit=100,
                stop_order_id=None,
            )
            for order_info in page_orders:
                if order_info.get("client_id") == client_id:
                    return order_info

            if page_orders.has_next:
                page += 1
            else:
                break

    @log_func_consume
    def sync_position_type_and_leverage(self):
        user_id = self.sub_user_id
        client = self.client
        market = self.trader_op.market
        follower_his = self.follower_his
        trader_pref = client.get_preference(follower_his.copy_trader_sub_user_id, market)
        follower_pref = client.get_preference(user_id, market)

        if not self.position_pending_list:  # 只有新开的仓位才需要同步保证金模式
            position_type = trader_pref['position_type']
        else:
            position_type = follower_pref['position_type']
        leverage = trader_pref['leverage']
        need_sync = (position_type != follower_pref['position_type']
                     or leverage != follower_pref['leverage'])
        if need_sync:
            try:
                client.adjust_leverage(
                    user_id, market,
                    position_type,
                    leverage)
            except Exception as _e:
                current_app.logger.exception(
                    f"execute_trader_market_operation_task ADJUST_LEVERAGE {self.main_user_id} {self.sub_user_id}"
                    f" error {_e!r}")


    @log_func_consume
    def finish_order_status(self, order_info):
        op = self.follower_op
        op.order_id = order_info['order_id']
        op.amount = Decimal(order_info['amount']) - Decimal(order_info['left'])
        op.deal_stock = Decimal(order_info['deal_stock'])
        op.order_detail = json.dumps(order_info, cls=JsonEncoder)
        op.executed_at = now()
        op.status = CopyFollowerOrderOperation.Status.FINISHED
        db.session.commit()

    def notice_fail_msg_result(self):
        if self.follower_op.status != CopyFollowerOrderOperation.Status.FAILED:
            return
        if self.follower_op.fail_reason == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH.name:
            trader = CopyTraderManager.get_trader(self.trader_op.user_id)
            follower_pref = self.client.get_preference(self.sub_user_id, self.trader_op.market)
            FollowerMessageSender.send_open_position_failed(
                self.follower_op.user_id,
                trader,
                self.trader_op.market,
                follower_pref['leverage'],
            )

    @log_func_consume
    def reduce(self):
        user_id = self.sub_user_id
        market = self.trader_op.market
        client = self.client
        op = self.follower_op

        is_need_close = self.trader_op.operation_type == CopyTraderPositionChangeRecord.OperationType.CLOSE_POSITION
        order_info = self.find_order_by_client_id(self.follower_op.client_id)
        if order_info:
            if not is_need_close:
                self.finish_order_status(order_info)
                return
        self.sync_position_type_and_leverage()
        cur_position = self.position_pending_list[0]
        position_total_amount = Decimal(cur_position['amount'])
        position_id = cur_position['position_id']
        if is_need_close:
            follower_amount = Decimal(0)
        else:
            follower_amount = min([self.get_follower_order_amount(),
                                   position_total_amount])
        fee_rate = self.get_user_fee(user_id, market)
        try:
            l_market_close = log_func_consume(client.market_close)
            res = l_market_close(
                user_id=user_id,
                market=market,
                position_id=position_id,
                amount=amount_to_str(follower_amount),
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                source=self.source,
                client_id=op.client_id,
            )
        except client.BadResponse as e:
            if not is_need_close:  # 减仓失败不重试
                op.fail_reason = PerpetualResponseCode(e.code).name
                op.status = CopyFollowerOrderOperation.Status.FAILED
                db.session.commit()
                return
        # 平仓需要检查
        if is_need_close and client.position_pending(user_id, market):
            msg = (f"跟单交易告警：用户main_user_id:{self.main_user_id};sub_uer_id:{user_id}"
                   f"存在市场{market}平仓失败")
            send_alert_notice(msg, config["ADMIN_CONTACTS"].get('copy_trading', ''))
            raise InvalidArgument(message=msg)
        self.finish_order_status(res)
        self.notice_success_msg()

    @log_func_consume
    def add(self):
        user_id = self.sub_user_id
        client = self.client
        op = self.follower_op
        market = self.trader_op.market
        follower_amount = self.get_follower_order_amount()
        order_info = self.find_order_by_client_id(self.follower_op.client_id)

        if order_info:
            self.finish_order_status(order_info)
            return
        self.sync_position_type_and_leverage()
        fee_rate = self.get_user_fee(user_id, market)
        try:
            # wrap func
            l_put_limit = log_func_consume(self.client.put_limit)
            res = l_put_limit(
                user_id=user_id,
                market=market,
                side=self.sell_or_buy.value,
                amount=amount_to_str(follower_amount),
                price=amount_to_str(self.slippage_price, int(self.market_info['money_prec'])),
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=self.source,
                fee_asset=None,
                fee_discount=None,
                effect_type=2,  # 2：IOC作为滑点价格保护
                client_id=self.follower_op.client_id,
            )
        except client.BadResponse as e:
            op.fail_reason = PerpetualResponseCode(e.code).name
            op.status = CopyFollowerOrderOperation.Status.FAILED
            db.session.commit()
            if op.fail_reason == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH.name:
                current_app.logger.warning(
                    "COPY_TRADING_CONTRACT_BALANCE_NOT_ENOUGH: "
                    f"user_id={user_id}, "
                    f"market={market}, "
                    f"side={self.sell_or_buy.value}, "
                    f"amount={amount_to_str(follower_amount)}, "
                    f"price={amount_to_str(self.slippage_price, int(self.market_info['money_prec']))}, "
                    f"trader_user_balance={self.trader_user_balance}, "
                    f"follower_user_balance={self.follower_user_balance}"
                )
            return
        self.finish_order_status(res)
        self.notice_success_msg()


class AddPositionOperation(PositionOperation):
    def __init__(self, op_id: int):
        super().__init__(op_id)

    def notice_success_msg(self):
        if not self.position_pending_list:
            send_follower_add_position_success_notice.delay(
                self.trader_op.user_id,
                self.follower_op.user_id,
                self.sub_user_id,
                self.follower_op.market,
                self.follower_op.amount,
                self.follower_op.deal_stock,
            )

    @log_func_consume
    @validate_and_update_status(CopyFollowerOrderOperation.Status.FAILED)
    def check_order_validity(self, order_amount, sell_or_buy, price):
        # 价格保护遵循下单接口逻辑
        tool = VerifyPriceTool(
            self.trader_op.market,
            OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
            OrderType.LIMIT_ORDER_TYPE,
            sell_or_buy,
            order_amount,
            price,
            Decimal(),
        )
        try:
            tool.validate(User.UserType.NORMAL)
        except ErrorWithResponseCode as e:
            return False, OrderException(e.code).name
        return True, ''

    @log_func_consume
    def validate(self):
        order_amount = self.get_follower_order_amount()
        if (
                not self.check_operation_expired()
                or not self.check_min_order_amount(order_amount)
                or not self.check_market_setting()
                or not self.check_follower_status()
                or not self.check_trade_permission()
                or not self.check_order_validity(order_amount, self.sell_or_buy, self.slippage_price)
        ):
            return False
        return True

    @log_func_consume
    def process(self):
        with CacheLock(LockKeys.copy_follower_sub(self.follower_op.sub_user_id)):
            db.session.rollback()
            if self.is_operation_finished():
                return
            if self.validate():
                self.add()
            self.notice_fail_msg_result()
            

class ReducePositionOperation(PositionOperation):
    def __init__(self, op_id: int):
        super().__init__(op_id)

    def notice_success_msg(self):
        return

    @log_func_consume
    def validate(self):
        if (
                not self.check_operation_expired()
                or not self.check_position_existed()
                or not self.check_market_setting()
                or not self.check_follower_status()
                or not self.check_min_order_amount(self.get_follower_order_amount())
                or not self.check_trade_permission(is_closing_or_reducing_position=True)
        ):
            return False
        return True

    @log_func_consume
    def process(self):
        with CacheLock(LockKeys.copy_follower_sub(self.follower_op.sub_user_id)):
            db.session.rollback()
            if self.is_operation_finished():
                return
            if self.validate():
                self.reduce()
                

class ClosePositionOperation(PositionOperation):
    def __init__(self, op_id: int):
        super().__init__(op_id)

    def notice_success_msg(self):
        is_need_close = self.trader_op.operation_type == CopyTraderPositionChangeRecord.OperationType.CLOSE_POSITION
        if is_need_close:
            cur_position = self.position_pending_list[0]
            order_detail = json.loads(self.follower_op.order_detail)
            send_follower_close_position_success_notice.delay(
                self.trader_op.user_id,
                self.follower_op.user_id,
                Decimal(order_detail['last_deal_price']),
                cur_position['market'],
                cur_position['leverage'],
            )

    @log_func_consume
    def validate(self):
        if (
                not self.check_position_existed()
                or not self.check_market_setting()
                or not self.check_follower_status()
                or not self.check_trade_permission(is_closing_or_reducing_position=True)
        ):
            return False
        return True

    @log_func_consume
    def process(self):
        if self.is_operation_finished():
            return
        if self.validate():
            self.reduce()
