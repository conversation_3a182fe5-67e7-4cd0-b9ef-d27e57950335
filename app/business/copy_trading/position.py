# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import datetime
from flask import current_app

from app.common import TradeType, TradeBusinessType
from app.caches import PerpetualMarketCache
from app.business.fee_constant import DEFAULT_MIN_CONTRACT_TAKER_FEE, DEFAULT_MIN_CONTRACT_MAKER_FEE
from app.business import PerpetualServerClient, PerpetualHistoryDB, PerpetualLogDB, ORDER_BOTH_SIDE
from app.business.fee import FeeFetcher
from app.models.copy_trading import CopyFollowerOrderOperation
from app.utils import amount_to_str, batch_iter, timestamp_to_datetime


def cancel_user_all_limit_and_stop_order(user_id: int):
    """ 撤销全部的委托订单，外面加锁 """
    from app.business.perpetual.order import CancelOrderTool, OrderTypeToBeCancelled

    _stop_tool = CancelOrderTool(OrderTypeToBeCancelled.STOP, user_id, None, ORDER_BOTH_SIDE)
    _stop_tool.cancel_all()
    _limit_tool = CancelOrderTool(OrderTypeToBeCancelled.LIMIT, user_id, None, ORDER_BOTH_SIDE)
    _limit_tool.cancel_all()


def market_close_user_all_position(user_id: int, clear_take_profit_stop_loss: bool = False, cancel_order: bool = True) -> bool:
    """ 市价平仓所有生效中的仓位，外面加锁 """
    from app.business.perpetual import TakeProfitStopLossPriceType

    client = PerpetualServerClient(current_app.logger)
    positions = client.position_pending(user_id)
    fee_fetcher = FeeFetcher(user_id)
    stop_types = [i.value for i in TakeProfitStopLossPriceType]
    for pos in positions:
        market = pos["market"]
        market_info = PerpetualMarketCache().get_market_info(market)
        if not market_info:
            continue
        fee_result = fee_fetcher.fetch(TradeBusinessType.PERPETUAL, market)
        maker_fee_rate = max(fee_result[TradeType.MAKER], DEFAULT_MIN_CONTRACT_MAKER_FEE)
        taker_fee_rate = max(fee_result[TradeType.TAKER], DEFAULT_MIN_CONTRACT_TAKER_FEE)
        if clear_take_profit_stop_loss and int(pos['take_profit_type']) in stop_types:
            try:
                client.position_take_profit(
                    user_id=user_id,
                    market=market,
                    take_profit_price='0',
                    stop_type=pos['take_profit_type'],
                    position_id=pos['position_id'],
                    taker_fee_rate=amount_to_str(taker_fee_rate, 8),
                    maker_fee_rate=amount_to_str(maker_fee_rate, 8),
                )
            except client.BadResponse as e:
                current_app.logger.error(f'position clear_take_profit market {market} failed: {e!r}')
        if clear_take_profit_stop_loss and int(pos['stop_loss_type']) in stop_types:
            try:
                client.position_stop_loss(
                    user_id=user_id,
                    market=market,
                    stop_loss_price='0',
                    stop_type=pos['stop_loss_type'],
                    position_id=pos['position_id'],
                    taker_fee_rate=amount_to_str(taker_fee_rate, 8),
                    maker_fee_rate=amount_to_str(maker_fee_rate, 8),
                )
            except client.BadResponse as e:
                current_app.logger.error(f'position clear_stop_loss market {market} failed: {e!r}')

        try:
            client.market_close(
                user_id=user_id,
                market=market,
                position_id=pos['position_id'],
                amount='0',  # 0 代表全部平仓,
                taker_fee_rate=amount_to_str(taker_fee_rate, 8),
                source='system',
            )
        except client.BadResponse as e:
            current_app.logger.error(f'position close market {market} failed: {e!r}')
            continue

    if cancel_order:
        cancel_user_all_limit_and_stop_order(user_id)
    if client.position_pending(user_id):  # 避免市价未能全平
        return False
    return True


def query_users_time_range_finish_positions(
    user_ids: set[int],
    start_dt: datetime,
    end_dt: datetime = None,
    created_at: datetime = None,
    fin_columns: list[str] = None,
) -> list[dict]:
    if not fin_columns:
        fin_columns = (
            'position_id', 'user_id', 'create_time', 'update_time', 'market', 'profit_real',
        )
    dbs_tables = defaultdict(list)
    for user_id_ in user_ids:
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id_, 'position_history')
        dbs_tables[(_db, _table)].append(user_id_)
    start_ts = int(start_dt.timestamp())
    where = f'update_time >= {start_ts} '
    if end_dt:
        end_ts = int(end_dt.timestamp())
        where += f'and update_time < {end_ts} '
    if created_at:
        create_ts = int(created_at.timestamp())
        where += f'and create_time >= {create_ts} '
    fin_positions = []
    for k, v in dbs_tables.items():
        _db, _table = k
        if len(v) == 1:
            _where = f' user_id = {v[0]} and ' + where
        else:
            user_id_str = ','.join(map(str, v))
            _where = f' user_id in ({user_id_str}) and ' + where
        _fin_records = _db.table(_table).select(*fin_columns, where=_where)
        fin_positions.extend([dict(zip(fin_columns, i)) for i in _fin_records])
    return fin_positions


def query_user_position_by_deals(
    user_id: int,
    order_ids: list[int],
) -> list[dict]:
    fin_columns = (
        'position_id', 'order_id'
    )
    fin_deals = []
    if not order_ids:
        return fin_deals
    _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
    order_id_str = ','.join(map(str, order_ids))
    _where = f' user_id = {user_id} and order_id in ({order_id_str})'
    _fin_records = _db.table(_table).select(*fin_columns, where=_where)
    fin_deals.extend([dict(zip(fin_columns, i)) for i in _fin_records])
    return fin_deals


def query_user_position_by_position_id(
    user_id: int,
    position_ids: set[int],
) -> list[dict]:
    fin_columns = (
        'position_id',
    )
    fin_positions = []

    if not position_ids:
        return fin_positions

    _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'position_history')
    position_id_str = ','.join(map(str, position_ids))
    _where = f' user_id = {user_id} and position_id in ({position_id_str})'
    _fin_records = _db.table(_table).select(*fin_columns, where=_where)
    fin_positions.extend([dict(zip(fin_columns, i)) for i in _fin_records])
    return fin_positions


def query_users_time_range_positions(
    user_ids: set[int],
    start_dt: datetime,
    end_dt: datetime,
    created_at: datetime = None,
    fin_columns: list[str] = None,
    ped_columns: list[str] = None,
) -> tuple[dict[int, list], dict[int, list]]:
    """ 查时间范围内的已平仓位、当前仓位的已实现盈亏 """
    start_ts = int(start_dt.timestamp())
    end_ts = int(end_dt.timestamp())
    created_ts = int(created_at.timestamp()) if created_at else None
    if not fin_columns:
        fin_columns = (
            'position_id', 'user_id', 'create_time', 'update_time', 'market', 'profit_real',
        )

    dbs_tables = defaultdict(list)
    for user_id_ in user_ids:
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id_, 'position_history')
        dbs_tables[(_db, _table)].append(user_id_)

    fin_positions = []
    for k, v in dbs_tables.items():
        _db, _table = k
        user_id_str = ','.join(map(str, v))
        where = f' user_id in ({user_id_str}) ' \
                f'and update_time >= {start_ts} and update_time < {end_ts} '
        if created_ts:
            where += f" and create_time >= {created_ts} "
        _fin_records = _db.table(_table).select(*fin_columns, where=where)
        fin_positions.extend([dict(zip(fin_columns, i)) for i in _fin_records])

    slice_ts = end_ts - end_ts % 3600
    table = PerpetualLogDB.slice_position_table(slice_ts)
    if not table or not table.exists():
        s_table = PerpetualLogDB.table('slice_history')
        time_records = s_table.select(
            'time',
            where=f'`time` >= {start_ts} AND `time` <= {end_ts}'
        )
        if time_records:
            slice_ts = max([i[0] for i in time_records])
            table = PerpetualLogDB.table(f'slice_position_{slice_ts}')
    ped_positions = []
    if table and table.exists():
        if not ped_columns:
            ped_columns = (
                'id', 'user_id', 'create_time', 'update_time', 'amount', 'open_price',
                'profit_real',
            )
        for ch_ids in batch_iter(user_ids, 5000):
            user_id_str = ','.join(map(str, ch_ids))
            _ped_rows = table.select(*ped_columns, where=f"user_id in ({user_id_str})")
            ped_positions.extend([dict(zip(ped_columns, i)) for i in _ped_rows])

    fin_pos_ids = set()
    user_fin_positions_map = defaultdict(list)
    for r in fin_positions:
        user_fin_positions_map[r['user_id']].append(r)
        fin_pos_ids.add(r['position_id'])

    user_ped_positions_map = defaultdict(list)
    for r in ped_positions:
        if r['id'] in fin_pos_ids:
            continue  # 可能快照之后平仓了
        if int(r['create_time']) >= end_ts:
            continue
        r['position_id'] = r['id']
        user_ped_positions_map[r['user_id']].append(r)

    return user_fin_positions_map, user_ped_positions_map


def query_users_pending_position_unreal_profits(user_ids: set[int], end_dt: datetime, realtime: bool = True) -> dict:
    """ 查时间范围内的当前仓位的未实现盈亏 """
    if realtime:
        asset_ = "USDT"
        client = PerpetualServerClient()
        user_rt_unreal_profit_map = defaultdict(lambda: defaultdict(Decimal))
        for ch_ids in batch_iter(user_ids, 50):
            ch_res = client.get_users_balances(asset_, ch_ids)
            for uid, bl in ch_res.items():
                _profit_unreal = Decimal(bl.get(asset_, {}).get('profit_unreal', 0))
                user_rt_unreal_profit_map[uid][asset_] = _profit_unreal
        return user_rt_unreal_profit_map

    end_ts = int(end_dt.timestamp())
    bl_slice_ts = end_ts - end_ts % 600
    bl_table = PerpetualLogDB.slice_balance_table(bl_slice_ts, interval=600)
    if not bl_table:
        bl_table = PerpetualLogDB.slice_balance_table(bl_slice_ts)
    if not bl_table or not bl_table.exists():
        s_table = PerpetualLogDB.table('slice_history')
        start_ts = bl_slice_ts - 3600 * 2
        time_records = s_table.select(
            'time',
            where=f'`time` >= {start_ts} AND `time` <= {end_ts}'
        )
        if time_records:
            slice_ts = max([i[0] for i in time_records])
            bl_table = PerpetualLogDB.table(f'slice_balance_{slice_ts}')
    if not bl_table or not bl_table.exists():
        return {}

    user_unreal_profit_map = defaultdict(lambda: defaultdict(Decimal))
    for ch_ids in batch_iter(user_ids, 5000):
        user_id_str = ','.join(map(str, ch_ids))
        where = f"user_id in ({user_id_str}) AND type=5 "
        _rows = bl_table.select("user_id, asset, balance", where=where)
        for _user_id, asset, balance in _rows:
            user_unreal_profit_map[_user_id][asset] += balance
    return user_unreal_profit_map


def query_users_last_trade_time(user_ids: set[int], start_dt: datetime, end_dt: datetime) -> dict:
    """ 查时间范围内的用户最后成交时间 """
    start_ts = int(start_dt.timestamp())
    end_ts = int(end_dt.timestamp())

    dbs_tables = defaultdict(list)
    for user_id_ in user_ids:
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id_, 'deal_history')
        dbs_tables[(_db, _table)].append(user_id_)

    user_last_trade_time_map = dict()
    cols = ["user_id", "MAX(time)"]
    for k, v in dbs_tables.items():
        _db, _table = k
        user_id_str = ','.join(map(str, v))
        where = f' user_id in ({user_id_str}) ' \
                f'and time >= {start_ts} and time < {end_ts} '
        _deals = _db.table(_table).select(*cols, where=where, group_by="user_id")
        for _r in _deals:
            user_last_trade_time_map[_r[0]] = timestamp_to_datetime(int(_r[1]))
    return user_last_trade_time_map


def is_position_record_completed(sub_user_id, copy_trader_user_id, start_created_at):
    order_op_list = CopyFollowerOrderOperation.query.filter(
        CopyFollowerOrderOperation.sub_user_id == sub_user_id,
        CopyFollowerOrderOperation.copy_trader_user_id == copy_trader_user_id,
        CopyFollowerOrderOperation.created_at >= start_created_at,
        CopyFollowerOrderOperation.status == CopyFollowerOrderOperation.Status.FINISHED,
        CopyFollowerOrderOperation.amount > 0,
    ).with_entities(
        CopyFollowerOrderOperation.sub_user_id,
        CopyFollowerOrderOperation.order_id,
        CopyFollowerOrderOperation.created_at,
    ).all()
    order_ids = [i.order_id for i in order_op_list]
    position_list = query_user_position_by_deals(sub_user_id, order_ids)
    deal_order_ids = {i['order_id'] for i in position_list}
    if len(order_ids) != len(deal_order_ids):
        return False
    position_ids = {i['position_id'] for i in position_list}
    his_position_list = query_user_position_by_position_id(sub_user_id,
                                                           position_ids)
    his_position_ids = {i['position_id'] for i in his_position_list}
    if len(position_ids) != len(his_position_ids):
        return False
    return True
