import json
from enum import Enum
from app.business import UserSettings, lock_call, PerpetualServerClient
from app.caches import UserLoginTokenCache
from app.caches.user import UserVisitPermissionCache
from app.common import CeleryQueues
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import User, SubAccount, UserLoginState, db, BigUserCustomer
from app.models.admin_tag import AdminTagUser, AdminTagCategory
from app.models.mongo.op_log import OPNamespaceObjectUser
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog
from app.utils import celery_task, batch_iter


class TagType(Enum):
    INTERNAL_STAFF = 1  # 员工标签
    DISABLE_ACCOUNT = 2  # 禁用账号
    BIG_BALANCE_OWNER = 3  # 资产大户
    BIG_CET_OWNER = 4  # CET大户
    INTERNAL_ACCOUNT = 5  # 内部账号
    SYSTEM_ACCOUNT = 6  # 系统账号
    WS_SPEED_WHITELIST = 7  # ws 加速白名单
    API_SELFDEAL_WHITELIST = 8  # API 自成交白名单
    INACTIVE_USER = 9  # 流失用户
    SPECIAL_PERPETUAL_POSITION = 10  # 合约特殊持仓上限
    CUSTODY_WHITELIST = 11  # 第三方资产托管白名单
    CLEAR_WHITELIST = 12  # 清退白名单


class TagOperator:
    def __init__(self, tag_id: int):
        self.tag_id = tag_id
        self.tag_name, self.tag_description = AdminTagHelper.tag_detail_map[tag_id]

    def get_tag_user(self, user_id: int) -> AdminTagUser:
        return AdminTagUser.get_or_create(user_id=user_id, tag_id=self.tag_id)

    def update_tag_user(self, user_id: int, updated_by: int, remark=None, detail=None):
        tag_user = self.get_tag_user(user_id)
        if tag_user.status and tag_user.status == AdminTagUser.Status.PASSED:
            raise InvalidArgument(message='tag status error')
        
        if remark:
            tag_user.remark = remark
        tag_user.updated_by = updated_by
        tag_user.status = AdminTagUser.Status.PASSED
        
        if detail:
            tag_user.detail = json.dumps(detail)
            AdminOperationLog.new_add(
                user_id=updated_by,
                ns_obj=OPNamespaceObjectUser.Basics,
                detail=dict(
                    tag=self.tag_name,
                    old=detail['old'],
                    new=detail['new'],
                ),
                target_user_id=user_id,
            )
        db.session.add(tag_user)

    def delete_tag_user(self, user_id: int, updated_by: int):
        tag_user = AdminTagUser.query.filter(
            AdminTagUser.user_id == user_id,
            AdminTagUser.tag_id == self.tag_id
        ).first()

        if not tag_user:
            raise RecordNotFound(message="tag user not found")
        if tag_user.status == AdminTagUser.Status.DELETED:
            raise InvalidArgument(message='tag status error')

        detail = json.loads(tag_user.detail)
        old_data = {'value': detail['new']['value']}
        new_data = {'value': detail['old']['value']}  # 还原到旧值
        detail = {'old': old_data, 'new': new_data}

        tag_user.detail = json.dumps(detail)
        tag_user.updated_by = updated_by
        tag_user.status = AdminTagUser.Status.DELETED
        db.session.add(tag_user)
        
        AdminOperationLog.new_add(
            user_id=updated_by,
            ns_obj=OPNamespaceObjectUser.Basics,
            detail=dict(
                tag=self.tag_name,
                old=old_data,
                new=new_data,
            ),
            target_user_id=user_id,
        )

        return tag_user, old_data, new_data

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        pass

    def handle_delete(self, user_id: int, updated_by: int):
        pass


class InternalStaffTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.INTERNAL_STAFF.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        user = User.query.get(user_id)
        old_data = {'value': user.kyc_status.value}
        new_data = {'value': User.KYCStatus.PASSED.value}
        
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        
        if old_data != new_data:
            user.kyc_status = User.KYCStatus.PASSED
        db.session.commit()
        UserVisitPermissionCache().add_user(user_id,
                                            UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)

    def handle_delete(self, user_id: int, updated_by: int):
        tag_user, old_data, new_data = self.delete_tag_user(user_id, updated_by)
        
        user = User.query.get(user_id)
        if old_data != new_data:
            user.kyc_status = User.KYCStatus(new_data['value'])
        db.session.commit()
        UserVisitPermissionCache().del_user(
            user_id, UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)


class DisableAccountTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.DISABLE_ACCOUNT.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        user_settings = UserSettings(user_id)
        old_data = {'value': user_settings.login_disabled_by_admin}
        new_data = {'value': True}

        if old_data != new_data:
            user_settings.login_disabled_by_admin = new_data['value']

        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})

        # Clear login tokens
        sub_user_ids = SubAccount.query.with_entities(SubAccount.user_id).filter(
            SubAccount.main_user_id == user_id
        ).all()
        sub_user_ids = [item.user_id for item in sub_user_ids]

        for uid in [user_id] + sub_user_ids:
            login_cache = UserLoginTokenCache(uid)
            tokens = login_cache.clear_tokens()
            UserLoginState.clear_tokens(tokens)

        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        tag_user, old_data, new_data = self.delete_tag_user(user_id, updated_by)

        if old_data != new_data:
            UserSettings(user_id).login_disabled_by_admin = new_data['value']

        db.session.commit()


class InternalAccountTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.INTERNAL_ACCOUNT.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()
        UserVisitPermissionCache().add_user(
            user_id, UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()
        UserVisitPermissionCache().del_user(
            user_id, UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)


class SystemAccountTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.SYSTEM_ACCOUNT.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()


class WSSpeedWhitelistTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.WS_SPEED_WHITELIST.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()


class APISelfDealWhitelistTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.API_SELFDEAL_WHITELIST.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()


class InactiveUserTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.INACTIVE_USER.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()


class CustodyWhitelistTag(TagOperator):
    def __init__(self):
        super().__init__(TagType.CUSTODY_WHITELIST.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()


class BigCustomerTag(TagOperator):
    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        # No business logic needed for big customer tags
        pass

    def handle_delete(self, user_id: int, updated_by: int):
        # No business logic needed for big customer tags
        pass


class BigCETCustomerTag(BigCustomerTag):
    def __init__(self):
        super().__init__(TagType.BIG_CET_OWNER.value)


class BigBalanceCustomerTag(BigCustomerTag):
    def __init__(self):
        super().__init__(TagType.BIG_BALANCE_OWNER.value)


class SpecialPositionTag(TagOperator):

    def __init__(self):
        super().__init__(TagType.SPECIAL_PERPETUAL_POSITION.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()
        PerpetualServerClient().update_preference_special()

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()
        PerpetualServerClient().update_preference_special()


class ClearWhitelistTag(TagOperator):

    def __init__(self):
        super().__init__(TagType.CLEAR_WHITELIST.value)

    def handle_pass(self, user_id: int, updated_by: int, remark=None):
        old_data = {'value': ''}
        new_data = {'value': ''}
        self.update_tag_user(user_id, updated_by, remark, {'old': old_data, 'new': new_data})
        db.session.commit()
        UserVisitPermissionCache().add_user(user_id,
                                            UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)

    def handle_delete(self, user_id: int, updated_by: int):
        self.delete_tag_user(user_id, updated_by)
        db.session.commit()
        UserVisitPermissionCache().del_user(user_id,
                                            UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE)



# Task handlers
# 按产品的要求目前不用异步处理，考虑到后续可能出现用户量大可支持异步
@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call(with_args=['user_id', 'tag_type'])
def handle_admin_tag_task(user_id: int, updated_by: int, status: str, tag_type: int, remark=None):
    tag_handler_map = {
        TagType.INTERNAL_STAFF.value: InternalStaffTag,
        TagType.DISABLE_ACCOUNT.value: DisableAccountTag,
        TagType.BIG_CET_OWNER.value: BigCETCustomerTag,
        TagType.BIG_BALANCE_OWNER.value: BigBalanceCustomerTag,
        TagType.INTERNAL_ACCOUNT.value: InternalAccountTag,
        TagType.SYSTEM_ACCOUNT.value: SystemAccountTag,
        TagType.WS_SPEED_WHITELIST.value: WSSpeedWhitelistTag,
        TagType.API_SELFDEAL_WHITELIST.value: APISelfDealWhitelistTag,
        TagType.INACTIVE_USER.value: InactiveUserTag,
        TagType.SPECIAL_PERPETUAL_POSITION.value: SpecialPositionTag,
        TagType.CUSTODY_WHITELIST.value: CustodyWhitelistTag,
        TagType.CLEAR_WHITELIST.value: ClearWhitelistTag,
    }
    
    tag_handler = tag_handler_map[tag_type]()
    if status == AdminTagUser.Status.PASSED.name:
        tag_handler.handle_pass(user_id, updated_by, remark)
    else:
        tag_handler.handle_delete(user_id, updated_by)


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call()
def update_big_customer_admin_tags():
    # 同步大客户列表到admin tag页面，仅展示
    user_query = BigUserCustomer.query.filter(
        BigUserCustomer.status == BigUserCustomer.StatusType.PASS
    ).with_entities(
        BigUserCustomer.user_id,
        BigUserCustomer.status,
        BigUserCustomer.is_big_cet_type,
        BigUserCustomer.is_big_balance_type,
    ).all()
    cet_valid_user_ids = {i.user_id for i in user_query if i.is_big_cet_type}
    balance_valid_user_ids = {i.user_id for i in user_query if i.is_big_balance_type}
    
    def _update_admin_tag_users(valid_user_ids, tag_id):
        admin_tag_user_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_id
        ).all()
        for admin_tag_user_query_ in batch_iter(admin_tag_user_query, 1000):
            for row in admin_tag_user_query_:
                if row.user_id in valid_user_ids and row.status != AdminTagUser.Status.PASSED:
                    row.status = AdminTagUser.Status.PASSED
                elif row.user_id not in valid_user_ids and row.status != AdminTagUser.Status.DELETED:
                    row.status = AdminTagUser.Status.DELETED
            db.session.commit()
        admin_tag_user_ids = {i.user_id for i in admin_tag_user_query}
        new_user_ids = valid_user_ids - admin_tag_user_ids
        to_create_list = []
        for user_id in new_user_ids:
            to_create_list.append(AdminTagUser(
                user_id=user_id,
                tag_id=tag_id,
            ))
        if to_create_list:
            db.session.add_all(to_create_list)
            db.session.commit()
            
    _update_admin_tag_users(cet_valid_user_ids, TagType.BIG_CET_OWNER.value)
    _update_admin_tag_users(balance_valid_user_ids, TagType.BIG_BALANCE_OWNER.value)


class AdminTagHelper:
    tag_detail_map = {
        TagType.INTERNAL_STAFF.value: ['员工标签', '内部员工账号，无需提交认证自动通过个人初级KYC'],
        TagType.DISABLE_ACCOUNT.value: ['禁用账号', '禁用用户无法登录WEB&APP端'],
        TagType.BIG_BALANCE_OWNER.value: ['资产大户', '用户历史持仓峰值大于等于阈值的用户'],
        TagType.BIG_CET_OWNER.value: ['CET大户', '最新CET持仓币数大于阈值标准的用户'],
        TagType.INTERNAL_ACCOUNT.value: ['内部账号', '内部账号，非用户资产，手动添加与删除'],
        TagType.SYSTEM_ACCOUNT.value: ['系统账号', '内部账号剔除内部做市商，相关报表需要剔除这个标签数据'],
        TagType.WS_SPEED_WHITELIST.value: ['ws 加速白名单', '名单内账号，server会写到配置文件中，WEB无其他特殊权限，server侧处理相关逻辑'],
        TagType.API_SELFDEAL_WHITELIST.value: ['API识别自成交', '名单内的账号，需要通过APi新增字段来识别自成交、正常成交的数据'],
        TagType.INACTIVE_USER.value: ['流失用户', '手动添加，名单内账号不进行任何触达，包括：邮件、弹窗、PUSH、站内信'],
        TagType.SPECIAL_PERPETUAL_POSITION.value: ["合约特殊持仓上限", "不受合约持仓上限限制的用户"],
        TagType.CUSTODY_WHITELIST.value: ['三方托管用户', '设置了该标签的用户才允许创建【三方托管子账号】'],
        TagType.CLEAR_WHITELIST.value: ['清退白名单', '免受清退白名单，适用于受访问限制的地区的合作方/项目方/特殊账户等'],
    }
    # 用于其他业务同步数据，不支持编辑
    editable_black_list = [TagType.BIG_BALANCE_OWNER.value, TagType.BIG_CET_OWNER.value]

    INTERNAL_TYPE_LIST = [
        TagType.INTERNAL_STAFF.value,
        TagType.INTERNAL_ACCOUNT.value,
    ]

    CLEAR_WHITELIST_TYPES = [
        TagType.INTERNAL_STAFF.value,
        TagType.INTERNAL_ACCOUNT.value,
        TagType.CLEAR_WHITELIST.value
    ]

    @classmethod
    def query_clear_whitelist_user_ids(cls) -> set[int]:
        admin_tag_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id.in_(cls.CLEAR_WHITELIST_TYPES),
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).with_entities(
            AdminTagUser.user_id
        ).all()
        return {i.user_id for i in admin_tag_query}

    @classmethod
    def is_clear_whitelist_user(cls, user_id: int) -> bool:
        admin_tag_user = AdminTagUser.query.filter(
            AdminTagUser.user_id == user_id,
            AdminTagUser.tag_id.in_(cls.CLEAR_WHITELIST_TYPES),
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).with_entities(
            AdminTagUser.user_id
        ).first()
        return bool(admin_tag_user)

    @classmethod
    def get_tag_users(cls, tag_type: TagType) -> list[int]:
        tag_category = AdminTagCategory.query.filter(
            AdminTagCategory.id == tag_type.value,
            AdminTagCategory.status == AdminTagCategory.Status.PASSED
        ).first()

        if not tag_category:
            return []

        tag_users = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_category.id,
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).with_entities(AdminTagUser.user_id).all()
        return [user.user_id for user in tag_users]

    @classmethod
    def filter_inactive_users(cls, user_ids: set[int] | list[int]) -> set[int]:
        """从用户ID列表中过滤掉流失用户"""
        if not user_ids:
            return set()
        # original user_ids is list, convert to set
        user_ids_set = set(user_ids) if isinstance(user_ids, list) else user_ids
        # query inactive user ids
        if len(user_ids) <= 1000:
            admin_tag_query = AdminTagUser.query.filter(
                AdminTagUser.tag_id == TagType.INACTIVE_USER.value,
                AdminTagUser.user_id.in_(user_ids_set),
                AdminTagUser.status == AdminTagUser.Status.PASSED
            ).with_entities(
                AdminTagUser.user_id
            ).all()
        else:
            admin_tag_query = AdminTagUser.query.filter(
                AdminTagUser.tag_id == TagType.INACTIVE_USER.value,
                AdminTagUser.status == AdminTagUser.Status.PASSED
            ).with_entities(
                AdminTagUser.user_id
            ).all()
        inactive_user_ids_set = {i.user_id for i in admin_tag_query}
        return user_ids_set - inactive_user_ids_set