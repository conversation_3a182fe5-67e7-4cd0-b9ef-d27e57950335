import functools
import itertools
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import Iterable, List

from celery.schedules import crontab
from flask import current_app
from sqlalchemy import or_

from app.business import PriceManager, TradeHistoryDB, lock_call
from app.business.activity.trade import TradeActivityBusiness
from app.business.balance.helper import UserTotalBalanceHelper
from app.business.clients import AntiFraudClient
from app.business.ip import IPReputationManager
from app.business.utils import query_records_by_time_range
from app.caches.statistics import AntiFraudDataCursorCache, AntiFraudReportCursorCache
from app.common import CeleryQueues, Language
from app.models import (
    TradeRankActivityDetail,
    AirdropActivity,
    AirdropActivityUserRecord,
    AntiFraudScore,
    DiscountActivity,
    DiscountActivityOrder,
    KYCInstitution,
    KycVerification,
    KycVerificationPro,
    LoginHistory,
    LoginRelationHistory,
    TradeRankActivity,
    TradeRankActivityJoinUser,
    TradeRankActivityUserInfo,
    User,
    Deposit,
    <PERSON>dra<PERSON>,
    db, IPReputationInfo, ReferralHistory,
)
from app.models.mongo.sms import MobileMessageRecordMySQL
from app.models.operation import (
    ActivityBlackList,
    AirdropActivityLotteryHistory,
    DiscountActivityLotteryHistory, DepositBonusActivity, DepositBonusActivityConfig, DepositBonusActivityApplyUser,
    DepositBonusActivityUserInfo,
)
from app.utils import (
    amount_to_str,
    batch_iter,
    celery_task,
    g_map,
    now,
    scheduled,
    timestamp_to_datetime,
    route_module_to_celery_queue
)

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


@scheduled(120)
@lock_call()
def report_anti_fraud_data_schedule():
    client = AntiFraudClient(use_udp=True)
    cache = AntiFraudDataCursorCache()
    report_registration(client, cache, "user")
    report_login_log(client, cache, "login_log")
    # report_local_withdrawal(client, cache, 'withdrawal')
    report_kyc(client, cache)
    report_sms(client, cache, "sms")
    report_user_balance_history(client, cache)
    report_ip_reputation_info(client, cache)


def err_catch(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            current_app.logger.error(
                f"report_anti_fraud_data task {f.__name__} exec error: {e}"
            )
            return

    return wrapper


@err_catch
def report_registration(client, cache, key):
    if not (last_id := cache.hget(key)):
        if user := User.query.order_by(User.id.desc()).first():
            cache.hset(key, user.id)
            return

    users = User.query.filter(User.id > last_id).order_by(User.id.asc()).all()
    for user in users:
        login_log = (
            LoginHistory.query.filter(LoginHistory.user_id == user.id)
            .order_by(LoginHistory.id.desc())
            .first()
        )
        user_agent = login_log.user_agent if login_log else "unknown"
        client.report_registration(user, user_agent)
        cache.hset(key, user.id)


@err_catch
def report_login_log(client, cache, key):
    if not (last_id := cache.hget(key)):
        if login_log := LoginHistory.query.order_by(LoginHistory.id.desc()).first():
            cache.hset(key, login_log.id)
            return

    login_logs = (
        LoginHistory.query.filter(LoginHistory.id > last_id)
        .order_by(LoginHistory.id.asc())
        .all()
    )
    for login_log in login_logs:
        client.report_login(login_log)
        cache.hset(key, login_log.id)


@err_catch
def report_local_withdrawal(client, cache, key):
    if not (last_id := cache.hget(key)):
        if transfer := Withdrawal.query.order_by(Withdrawal.id.desc()).first():
            cache.hset(key, transfer.id)
            return

    transfers = (
        Withdrawal.query.filter(Withdrawal.id > last_id)
        .order_by(Withdrawal.id.asc())
        .all()
    )
    for transfer in transfers:
        if transfer.type == Withdrawal.Type.LOCAL:
            client.report_transfer_log(transfer)
            cache.hset(key, transfer.id)
    if transfers and transfers[-1].type != Withdrawal.Type.LOCAL:
        cache.hset(key, transfers[-1].id)


@err_catch
def report_kyc(client, cache):
    kyc_type_map = {
        "kyc_verification": KycVerification,
        "kyc_verification_pro": KycVerificationPro,
        "kyc_institution": KYCInstitution,
    }

    _now = now()
    for kyc_type, kyc_model in kyc_type_map.items():
        if not (_last_time := cache.hget(kyc_type)):
            cache.hset(kyc_type, str(_now.timestamp()))
            continue

        kyc_infos = query_records_by_time_range(
            kyc_model,
            _now - timedelta(days=30),
            _now,
        )

        last_time = timestamp_to_datetime(float(_last_time))
        last_updated_at = last_time
        for kyc_info in kyc_infos:
            if kyc_info.updated_at <= last_time:
                continue

            client.report_kyc(
                kyc_info.user_id, kyc_info.updated_at, kyc_type, kyc_info.status.value
            )

            if kyc_info.updated_at > last_updated_at:
                last_updated_at = kyc_info.updated_at
                cache.hset(kyc_type, str(last_updated_at.timestamp()))

        cache.hset(kyc_type, str(_now.timestamp()))


@err_catch
def report_sms(client, cache, key):
    if not (last_created_at := cache.hget(key)):
        if sms_recode := MobileMessageRecordMySQL.query.order_by(MobileMessageRecordMySQL.id.desc()).first():
            cache.hset(
                key, str(sms_recode.created_at.timestamp())
            )  # 时间戳精确到毫秒，直接用来做游标
            return

    sms_recodes = (
        MobileMessageRecordMySQL.query
        .filter(MobileMessageRecordMySQL.created_at > timestamp_to_datetime(float(last_created_at)))
        .order_by(MobileMessageRecordMySQL.id.asc())
        .all()
    )

    last_created_at = None
    for sms_recode in sms_recodes:
        client.report_sms(
            sms_recode.user_id,
            sms_recode.created_at,
            sms_recode.mobile_num,
            sms_recode.mobile_country_code,
            sms_recode.country,
            sms_recode.business,
            sms_recode.has_user_binded_mobile,
        )

        if last_created_at is None or sms_recode.created_at > last_created_at:
            last_created_at = sms_recode.created_at
        cache.hset(key, str(sms_recode.created_at.timestamp()))


@err_catch
def report_user_balance_history(client, cache):
    _cache_key = "user_balance_history_{}_{}"
    _now = now()

    def inner(db_index):
        db_count, table_count = db_index
        cache_key = _cache_key.format(db_count, table_count)
        _db = TradeHistoryDB.DBS[db_count]

        if not (last_id := cache.hget(cache_key)):
            records = _db.table(f"balance_history_{table_count}").select(
                *["id"], order_by="id DESC", limit=1
            )

            if records:
                cache.hset(cache_key, str(records[0][0]))
            return

        fields = ["id", "user_id", "time", "account", "asset", "change", "balance"]
        where = f"id > {last_id}"
        records = _db.table(f"balance_history_{table_count}").select(
            *fields,
            where=where,
            order_by="id ASC",
        )

        user_balance_historys = [dict(zip(fields, item)) for item in records]

        for user_balance_history in user_balance_historys:
            client.report_user_balance_history(
                user_balance_history["user_id"],
                user_balance_history["time"],
                user_balance_history["account"],
                user_balance_history["asset"],
                str(user_balance_history["change"]),
                str(user_balance_history["balance"]),
            )

            cache.hset(cache_key, user_balance_history["id"])

    g_map(
        inner,
        [
            (db_count, table_count)
            for db_count in range(TradeHistoryDB.DB_COUNT)
            for table_count in range(TradeHistoryDB.TABLE_COUNT)
        ],
        size=50,
    )


@err_catch
def report_ip_reputation_info(client, cache):
    _cache_key = "ip_reputation_info"
    _now = now()

    if not (_last_time := cache.hget(_cache_key)):
        cache.hset(_cache_key, str(_now.timestamp()))
        return

    ip_reputations = IPReputationInfo.query.filter(
        IPReputationInfo.updated_at > timestamp_to_datetime(float(_last_time))).order_by(
        IPReputationInfo.updated_at.asc()).all()

    for ip_reputation in ip_reputations:
        ip_reputation_info = ip_reputation.reputation_info
        client.report_ip_reputation_info(
            ip_reputation.ip,
            ip_reputation.updated_at.timestamp(),
            bool(IPReputationManager.format_anonymity_info(ip_reputation_info.get('anonymity', {}))),
            bool(ip_reputation_info.get('blacklists', {}).get('detections')),
            ip_reputation_info.get('information', {}).get('country_code', ''),
        )
        cache.hset(_cache_key, str(_now.timestamp()))


"""
本行注释之前的代码为羊毛党风控服务的上报逻辑。
以下逻辑为业务侧自动筛选羊毛党的计算逻辑。
    由于羊毛党风控目前想要实现自动风控较为复杂，且运营对该需求要求尽早交付，因此暂时先在业务侧实现。
"""


@scheduled(crontab(hour="*/1", minute="5"))
@lock_call()
def update_anti_frand_score_schedule():
    ## 交易排位赛
    model = TradeRankActivity
    trade_rank_list: List[TradeRankActivity] = (
        model.query.filter(
            model.status == model.Status.ONLINE,
            model.started_at > now() - timedelta(days=30),
            model.ended_at < now(),
        )
        .with_entities(model.id, model.activity_id)
        .order_by(model.id)
        .all()
    )
    for trand_rank in trade_rank_list:
        joined_records = (
            TradeRankActivityJoinUser.query.filter(
                TradeRankActivityJoinUser.trade_activity_id == trand_rank.id,
            )
            .with_entities(TradeRankActivityJoinUser.user_id)
            .all()
        )
        joined_user_ids = [item.user_id for item in joined_records]
        update_anti_fraud_trade_rank(trand_rank.activity_id, joined_user_ids)
    ## 随机空投
    model = AirdropActivity
    airdrop_list: List[AirdropActivity] = (
        model.query.filter(
            model.status == model.StatusType.ONLINE,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id, model.activity_id)
        .order_by(model.id)
        .all()
    )
    for airdrop in airdrop_list:
        joined_records = AirdropActivityUserRecord.query.filter(
            AirdropActivityUserRecord.airdrop_activity_id == airdrop.id,
        ).all()
        joined_user_ids = [item.user_id for item in joined_records]
        update_anti_fraud_random_airdrop(airdrop.activity_id, joined_user_ids)
    ## Dibs
    model = DiscountActivity
    discount_list: List[DiscountActivity] = (
        model.query.filter(
            model.status == model.StatusType.ONLINE,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id, model.activity_id)
        .order_by(model.id)
        .all()
    )
    for discount in discount_list:
        joined_records = (
            DiscountActivityOrder.query.filter(
                DiscountActivityOrder.discount_activity_id == discount.id
            )
            .with_entities(DiscountActivityOrder.user_id)
            .all()
        )
        joined_user_ids = [item.user_id for item in joined_records]
        update_anti_fraud_dibs(discount.activity_id, joined_user_ids)
    # 充值福利
    model = DepositBonusActivity
    deposit_bonus_list: List[DepositBonusActivity] = (
        model.query.filter(
            model.status == model.StatusType.ONLINE,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id)
        .order_by(model.id)
        .all()
    )
    for deposit_bonus in deposit_bonus_list:
        config_rows = DepositBonusActivityConfig.query.with_entities(
            DepositBonusActivityConfig.id,
            DepositBonusActivityConfig.deposit_bonus_id,
            DepositBonusActivityConfig.activity_id,
        ).filter(
            DepositBonusActivityConfig.deposit_bonus_id == deposit_bonus.id
        ).all()
        for cfg in config_rows:
            joined_records = (
                DepositBonusActivityApplyUser.query.filter(
                    DepositBonusActivityApplyUser.deposit_bonus_id == deposit_bonus.id,
                    DepositBonusActivityApplyUser.activity_id == cfg.id,
                )
                .with_entities(DepositBonusActivityApplyUser.user_id)
                .all()
            )
            joined_user_ids = [item.user_id for item in joined_records]
            update_anti_fraud_deposit_bonus(cfg.activity_id, joined_user_ids)


@celery_task
def update_anti_fraud_trade_rank(activity_id, user_ids, force_update=False):
    if _check_anti_score_is_generated(activity_id) and not force_update:
        return

    trade_rank_activity = TradeRankActivity.query.filter(
        TradeRankActivity.activity_id == activity_id
    ).first()
    if not trade_rank_activity:
        return

    ranks: TradeRankActivityUserInfo = (
        TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == trade_rank_activity.id,
            TradeRankActivityUserInfo.user_id.in_(user_ids),
        )
        .with_entities(
            TradeRankActivityUserInfo.user_id,
            TradeRankActivityUserInfo.gift_asset,
            TradeRankActivityUserInfo.gift_amount,
            TradeRankActivityUserInfo.market_fee_usd,
        )
        .all()
    )
    is_surolus_mapping = defaultdict(bool)
    for rank_user_info in ranks:
        gift_usd = (
            PriceManager.asset_to_usd(rank_user_info.gift_asset)
            * rank_user_info.gift_amount
        )
        if rank_user_info.market_fee_usd > gift_usd:
            is_surolus_mapping[rank_user_info.user_id] = True

    (
        score_mapping,
        same_register_ip_cnt_mapping,
        same_login_ip_cnt_mapping,
        same_device_id_cnt_mapping,
        users_total_usd_mapping,
        peer_transfer_mapping,
    ) = get_user_fraud_score(user_ids)
    similar_email_mapping = get_similar_emails(user_ids)

    data = defaultdict(dict)
    for user_id in user_ids:
        data[user_id]["same_register_ip_cnt"] = same_register_ip_cnt_mapping.get(
            user_id, 0
        )
        data[user_id]["same_login_ip_cnt"] = same_login_ip_cnt_mapping.get(user_id, 0)
        data[user_id]["same_device_id_cnt"] = same_device_id_cnt_mapping.get(user_id, 0)
        data[user_id]["total_usd"] = amount_to_str(
            users_total_usd_mapping.get(user_id, Decimal(0)), 2
        )
        data[user_id]["score"] = score_mapping.get(user_id, 0)
        data[user_id]["peer_transfer_user_ids"] = list(
            peer_transfer_mapping.get(user_id, [])
        )
        data[user_id]["similar_emails"] = similar_email_mapping.get(user_id, {})
        data[user_id]["is_surolus"] = is_surolus_mapping.get(user_id, False)

    record = AntiFraudScore.get_or_create(
        activity_type=AntiFraudScore.ActivityType.TRADE_RANK, activity_id=activity_id
    )
    record.data = data
    db.session.add(record)
    db.session.commit()
    return


def _check_anti_score_is_generated(activity_id):
    record = AntiFraudScore.query.filter(
        AntiFraudScore.activity_id == activity_id
    ).first()
    return record is not None


@celery_task
def update_anti_fraud_random_airdrop(activity_id, user_ids, force_update=False):
    if _check_anti_score_is_generated(activity_id) and not force_update:
        return

    return _update_anti_fraud_by_only_score(
        user_ids, AntiFraudScore.ActivityType.AIRDROP, activity_id
    )


@celery_task
def update_anti_fraud_dibs(activity_id, user_ids, force_update=False):
    if _check_anti_score_is_generated(activity_id) and not force_update:
        return

    return _update_anti_fraud_by_only_score(
        user_ids, AntiFraudScore.ActivityType.DIBS, activity_id
    )


@celery_task
def update_anti_fraud_deposit_bonus(activity_id, user_ids, force_update=False):
    if _check_anti_score_is_generated(activity_id) and not force_update:
        return

    return _update_anti_fraud_by_only_score(
        user_ids, AntiFraudScore.ActivityType.DEPOSIT_BONUS, activity_id
    )


def _update_anti_fraud_by_only_score(user_ids, activity_type, activity_id):
    (
        score_mapping,
        same_register_ip_cnt_mapping,
        same_login_ip_cnt_mapping,
        same_device_id_cnt_mapping,
        users_total_usd_mapping,
        peer_transfer_mapping,
    ) = get_user_fraud_score(user_ids)
    similar_email_mapping = get_similar_emails(user_ids)

    data = defaultdict(dict)
    for user_id in user_ids:
        data[user_id]["same_register_ip_cnt"] = same_register_ip_cnt_mapping.get(
            user_id, 0
        )
        data[user_id]["same_login_ip_cnt"] = same_login_ip_cnt_mapping.get(user_id, 0)
        data[user_id]["same_device_id_cnt"] = same_device_id_cnt_mapping.get(user_id, 0)
        data[user_id]["total_usd"] = amount_to_str(
            users_total_usd_mapping.get(user_id, Decimal(0)), 2
        )
        data[user_id]["score"] = score_mapping.get(user_id, 0)
        data[user_id]["peer_transfer_user_ids"] = list(
            peer_transfer_mapping.get(user_id, [])
        )
        data[user_id]["similar_emails"] = similar_email_mapping.get(user_id, {})

    record = AntiFraudScore.get_or_create(
        activity_type=activity_type, activity_id=activity_id
    )
    record.data = data
    db.session.add(record)
    db.session.commit()
    return


def get_similar_emails(user_ids):
    user_email_mapping = {}
    for batch_user_id in batch_iter(user_ids, 1000):
        user_emails = (
            User.query.filter(User.id.in_(batch_user_id))
            .with_entities(User.id, User.email)
            .all()
        )
        user_email_mapping.update(dict(user_emails))

    similar_email_map = defaultdict(dict)
    for user_id, email in user_email_mapping.items():
        if not email:
            continue
        root = _extract_root(email)
        if not root:
            continue
        similar_email_map[root][user_id] = email

    res = defaultdict(list)
    for user_id, email in user_email_mapping.items():
        if not email:
            continue
        root = _extract_root(email)
        if similar_emails_dict := similar_email_map[root]:
            res[user_id] = {
                key: val for key, val in similar_emails_dict.items() if key != user_id
            }
    return res


def _extract_root(email):
    prefix, _ = email.split("@", 1)

    res = ""
    for char in prefix:
        if char.isalpha():
            res += char
    return res.lower()


def get_same_device_users_count(user_ids: Iterable[int]):
    records = (
        LoginRelationHistory.query.filter(LoginRelationHistory.user_id.in_(user_ids))
        .order_by(LoginRelationHistory.id.asc())
        .all()
    )

    _register_ip_mapping, _user_register_ip_mapping = defaultdict(set), defaultdict(set)
    _login_ip_mapping, _user_login_ip_mapping = defaultdict(set), defaultdict(set)
    _device_id_mapping, _user_device_id_mapping = defaultdict(set), defaultdict(set)

    for v in records:
        _login_ip_mapping[v.ip].add(v.user_id)
        _user_login_ip_mapping[v.user_id].add(v.ip)
        if v.is_registration:
            _register_ip_mapping[v.ip].add(v.user_id)
            _user_register_ip_mapping[v.user_id].add(v.ip)
        if v.device_id:
            _device_id_mapping[v.device_id].add(v.user_id)
            _user_device_id_mapping[v.user_id].add(v.device_id)

    same_register_mapping = defaultdict(set)
    same_login_ip_mapping = defaultdict(set)
    same_device_id_mapping = defaultdict(set)
    for user_id, register_ip_set in _user_register_ip_mapping.items():
        for register_ip in register_ip_set:
            same_register_mapping[user_id] |= _register_ip_mapping[register_ip]

    for user_id, login_ip_set in _user_login_ip_mapping.items():
        for login_ip in login_ip_set:
            same_login_ip_mapping[user_id] |= _login_ip_mapping[login_ip]

    for user_id, device_id_set in _user_device_id_mapping.items():
        for device_id in device_id_set:
            same_device_id_mapping[user_id] |= _device_id_mapping[device_id]

    same_register_cnt_mapping = defaultdict(int)
    same_login_ip_cnt_mapping = defaultdict(int)
    same_device_id_cnt_mapping = defaultdict(int)
    for user_id, same_register_set in same_register_mapping.items():
        same_register_cnt_mapping[user_id] = (
            len(same_register_set) - 1 if same_register_set else 0
        )
    for user_id, same_login_ip_set in same_login_ip_mapping.items():
        same_login_ip_cnt_mapping[user_id] = (
            len(same_login_ip_set) - 1 if same_login_ip_set else 0
        )
    for user_id, same_device_id_mapping in same_device_id_mapping.items():
        same_device_id_cnt_mapping[user_id] = (
            len(same_device_id_mapping) - 1 if same_device_id_mapping else 0
        )
    return (
        same_register_cnt_mapping,
        same_login_ip_cnt_mapping,
        same_device_id_cnt_mapping,
    )


def get_user_total_usd(user_ids: Iterable[int]):
    return UserTotalBalanceHelper(user_ids).get_user_balances()


def get_peer_transfer_users(user_ids: Iterable[int]):
    q = (
        Withdrawal.query.filter(
            or_(
                Withdrawal.user_id.in_(user_ids),
                Withdrawal.recipient_user_id.in_(user_ids),
            ),
            Withdrawal.type == Withdrawal.Type.LOCAL,
            Withdrawal.status == Withdrawal.Status.FINISHED,
        )
        .with_entities(Withdrawal.user_id, Withdrawal.recipient_user_id)
        .all()
    )

    peer_transfer_mapping = defaultdict(set)
    for item in q:
        if item.user_id in user_ids and item.recipient_user_id in user_ids and item.user_id != item.recipient_user_id:
            peer_transfer_mapping[item.user_id].add(item.recipient_user_id)
            peer_transfer_mapping[item.recipient_user_id].add(item.user_id)
    return peer_transfer_mapping


def get_user_fraud_score(user_ids: Iterable[int]):
    fraud_score_mapping = defaultdict(float)

    users_total_usd_mapping = get_user_total_usd(user_ids)
    peer_transfer_mapping = get_peer_transfer_users(user_ids)
    (
        same_register_ip_cnt_mapping,
        same_login_ip_cnt_mapping,
        same_device_id_cnt_mapping,
    ) = get_same_device_users_count(user_ids)
    for user_id in user_ids:
        if same_register_ip_cnt_mapping.get(user_id, 0) >= 2:
            fraud_score_mapping[user_id] += 0.5

        if same_login_ip_cnt_mapping.get(user_id, 0) >= 2:
            fraud_score_mapping[user_id] += 0.5

        if same_device_id_cnt_mapping.get(user_id, 0) >= 2:
            fraud_score_mapping[user_id] += 0.5

        if users_total_usd_mapping.get(user_id, 0) < 1:
            fraud_score_mapping[user_id] += 0.5

        if len(peer_transfer_mapping.get(user_id, set())) > 0:
            fraud_score_mapping[user_id] += 1
    return (
        fraud_score_mapping,
        same_register_ip_cnt_mapping,
        same_login_ip_cnt_mapping,
        same_device_id_cnt_mapping,
        users_total_usd_mapping,
        peer_transfer_mapping,
    )


def _revoke_trade_rank_rewards(activity_id, data):
    ## 只能对进行中的活动进行操作
    activity = TradeRankActivity.query.filter(
        TradeRankActivity.activity_id == activity_id,
        TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
    ).first()
    if not activity:
        return

    b = TradeActivityBusiness(activity.id)
    for user_id, anti_fraud_data in data.items():
        if _check_trade_rank_fraud_rule(anti_fraud_data):
            b.set_black_list_user(
                user_id,
                ActivityBlackList.Status.PASSED,
                "羊毛得分规则剔除",
                commit=False,
            )

        similar_emails_dict = anti_fraud_data.get("similar_emails", {})
        if not similar_emails_dict:
            continue

        # 有相似邮箱的只保留排名最高的那个
        all_similar_user_ids = list(similar_emails_dict.keys()) + [str(user_id)]
        rewards_records = (
            TradeRankActivityUserInfo.query.filter(
                TradeRankActivityUserInfo.trade_activity_id == activity.id,
                TradeRankActivityUserInfo.user_id.in_(all_similar_user_ids),
            )
            .with_entities(
                TradeRankActivityUserInfo.user_id, TradeRankActivityUserInfo.rank
            )
            .all()
        )
        rewards_records = [r for r in rewards_records if r.rank]
        rewards_records_sorted = sorted(rewards_records, key=lambda x: x.rank)
        if not rewards_records:
            continue

        highest_rank_user_id = rewards_records_sorted[0].user_id
        for user_id, _ in rewards_records:
            # 排名最高那个，同时满足了前面羊毛得分规则剔除的，仍然要给他发奖励
            if user_id == highest_rank_user_id:
                b.set_black_list_user(
                    user_id, ActivityBlackList.Status.DELETED, "", commit=False
                )
                continue

            remark = "相似邮箱规则剔除"
            if _check_trade_rank_fraud_rule(data.get(str(user_id), {})):
                remark = "羊毛得分规则剔除，相似邮箱规则剔除"

            b.set_black_list_user(
                user_id, ActivityBlackList.Status.PASSED, remark, commit=False
            )

    if activity.ended_at <= now():
        b.update_rankings()
    return


def _check_trade_rank_fraud_rule(anti_fraud_data):
    return anti_fraud_data.get("score", 0) >= 2.5 and not anti_fraud_data.get(
        "is_surolus", False
    )


def _revoke_random_airdrop_rewards(activity_id, data):
    ## 只能对进行中的活动进行操作
    activity = AirdropActivity.query.filter(
        AirdropActivity.activity_id == activity_id,
    ).first()

    if activity.active_status != AirdropActivity.ActiveStatus.PROCESSED:
        return

    anti_fraud_user_cache = set()
    for user_id, anti_fraud_data in data.items():
        row = AirdropActivityLotteryHistory.query.filter(
            AirdropActivityLotteryHistory.user_id == user_id,
            AirdropActivityLotteryHistory.airdrop_activity_id == activity.id,
        ).first()

        if not row:
            continue

        if anti_fraud_data.get("score") >= 2.5:
            row.remark = "羊毛得分规则剔除"
            row.state = AirdropActivityLotteryHistory.StateType.INVALID
            anti_fraud_user_cache.add(row.user_id)

        similar_emails_dict = anti_fraud_data.get("similar_emails", {})
        if not similar_emails_dict:
            continue

        all_similar_user_ids = [int(k) for k in similar_emails_dict.keys()] + [user_id]
        for user_id in all_similar_user_ids:
            # 相似邮箱和羊毛两个规则同时生效，则合并
            remark = "相似邮箱规则剔除"
            if data.get(user_id, {}).get("score", 0) >= 2.5:
                remark = "羊毛得分规则剔除，相似邮箱规则剔除"

            row.remark = remark
            row.state = AirdropActivityLotteryHistory.StateType.INVALID


def _revoke_dibs_rewards(activity_id, data):
    ## 只能对进行中的活动进行操作
    activity = DiscountActivity.query.filter(
        DiscountActivity.activity_id == activity_id,
    ).first()

    if activity.active_status != DiscountActivity.ActiveStatus.PROCESSED:
        return

    for user_id, anti_fraud_data in data.items():
        row = DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.user_id == user_id,
            DiscountActivityLotteryHistory.discount_activity_id == activity.id,
        ).first()
        if not row:
            return

        if anti_fraud_data.get("score") >= 2.5:
            row.remark = "羊毛得分规则剔除"
            row.state = DiscountActivityLotteryHistory.StateType.INVALID

        similar_emails_dict = anti_fraud_data.get("similar_emails", {})
        if not similar_emails_dict:
            continue

        all_similar_user_ids = [int(k) for k in similar_emails_dict.keys()] + [user_id]
        for user_id in all_similar_user_ids:
            # 相似邮箱和羊毛两个规则同时生效，则合并
            remark = "相似邮箱规则剔除"
            if data.get(user_id, {}).get("score", 0) >= 2.5:
                remark = "羊毛得分规则剔除，相似邮箱规则剔除"

            row.remark = remark
            row.state = DiscountActivityLotteryHistory.StateType.INVALID


def _get_deposit_tx_ids_with_activity(activity: DepositBonusActivity) -> dict[int, set]:
    user_id_created_at_map = {
        item.user_id: item.created_at for item in DepositBonusActivityUserInfo.query.filter(
            DepositBonusActivityUserInfo.deposit_bonus_id == activity.id,
        ).with_entities(
            DepositBonusActivityUserInfo.user_id,
            DepositBonusActivityUserInfo.created_at,
        ).all()
    }

    user_deposit_map = defaultdict(set)  # 用户活动期间充值的tx_id集合
    for batch_ids in batch_iter([int(item) for item in user_id_created_at_map.keys()], 2000):
        for row in Deposit.query.filter(
                Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.chain.in_(activity.get_chains()),
                Deposit.asset == activity.asset,
                Deposit.created_at >= activity.start_time,
                Deposit.created_at < activity.end_time,
                Deposit.user_id.in_(batch_ids),
                Deposit.status.in_([
                    Deposit.Status.FINISHED,
                    Deposit.Status.TO_HOT,
                    Deposit.Status.CONFIRMING,
                ]),
        ).with_entities(
            Deposit.user_id,
            Deposit.tx_id,
            Deposit.created_at,
        ).all():
            applying_at = user_id_created_at_map[row.user_id]
            if row.created_at < applying_at:  # 报名前的不计入
                continue
            user_deposit_map[row.user_id].add(row.tx_id)
    return user_deposit_map


def _tx_ids_in_withdraw(tx_ids: set[str]) -> bool:
    """从充值表检查交易ID是否存在从交易所提取的"""
    if len(tx_ids) == 0:
        return False
    return Withdrawal.query.filter(
        Withdrawal.tx_id.in_(tx_ids)
    ).count() > 0


def _revoke_deposit_bonus_rewards(activity_id, data):
    ## 只能对进行中的活动进行操作
    # 先找子活动，再找父活动
    config = DepositBonusActivityConfig.query.filter(
        DepositBonusActivityConfig.activity_id == activity_id
    ).first()
    if not config:
        return
    activity: DepositBonusActivity = DepositBonusActivity.query.filter(
        DepositBonusActivity.id == config.deposit_bonus_id,
        DepositBonusActivity.status == DepositBonusActivity.StatusType.ONLINE,
    ).first()
    if not activity:
        return

    user_deposit_map = _get_deposit_tx_ids_with_activity(activity)  # 用户活动期间充值的tx_id集合

    user_ids = {int(user_id) for user_id in data.keys()}
    user_ids.update(set(user_deposit_map.keys()))

    for user_id in user_ids:
        row = DepositBonusActivityUserInfo.query.filter(
            DepositBonusActivityUserInfo.deposit_bonus_id == activity.id,
            DepositBonusActivityUserInfo.activity_id == config.id,
            DepositBonusActivityUserInfo.user_id == user_id,
        ).first()
        if not row:
            return

        anti_fraud_data = data.get(str(user_id), {})

        remarks = set()
        if anti_fraud_data.get("score", 0) >= 2.5:
            remarks.add(DepositBonusActivityUserInfo.RemarkType.ANTI_FRAUD.value)

        similar_emails_dict = anti_fraud_data.get("similar_emails", {})
        if similar_emails_dict:
            remarks.add(DepositBonusActivityUserInfo.RemarkType.SIMILAR_EMAIL.value)
            for _user_id in similar_emails_dict.keys():
                if data.get(_user_id, {}).get("score", 0) >= 2.5:
                    remarks.add(DepositBonusActivityUserInfo.RemarkType.ANTI_FRAUD.value)

        if _tx_ids_in_withdraw(user_deposit_map.get(row.user_id, [])):
            remarks.add(DepositBonusActivityUserInfo.RemarkType.FRAUD_TX_ID.value)

        if len(remarks) > 0:
            row.remark = ','.join(remarks)
            row.status = DepositBonusActivityUserInfo.Status.INVALID


revoke_fraud_user_rewards_methods = {
    AntiFraudScore.ActivityType.TRADE_RANK: _revoke_trade_rank_rewards,
    AntiFraudScore.ActivityType.AIRDROP: _revoke_random_airdrop_rewards,
    AntiFraudScore.ActivityType.DIBS: _revoke_dibs_rewards,
    AntiFraudScore.ActivityType.DEPOSIT_BONUS: _revoke_deposit_bonus_rewards,
}


@scheduled(crontab(minute="*/5"))
@lock_call()
def revoke_fraud_user_rewards():
    records = AntiFraudScore.query.filter(
        AntiFraudScore.status == AntiFraudScore.Status.CREATED
    ).all()

    for record in records:
        if not record.data:
            record.status = AntiFraudScore.Status.FINISHED
            db.session.commit()
            continue

        method = revoke_fraud_user_rewards_methods.get(record.activity_type, None)
        if not method:
            record.status = AntiFraudScore.Status.FINISHED
            db.session.commit()
            continue

        method(record.activity_id, record.data)
        record.status = AntiFraudScore.Status.FINISHED
        db.session.commit()


def _extract_trade_rank_fraud_user_ids(trand_rank, anti_fraud_score):
    # 上传活动中被判断为相似邮箱 >= 3，且活动资格无效的用户上传到羊毛党系统中
    simlars_email_user_ids = anti_fraud_score.load_similar_email_user_ids(
        above_similar_emails_cnt=3,
    )
    invalid_lottery = (
        ActivityBlackList.query.filter(
            ActivityBlackList.activity_id == trand_rank.activity_id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED,
        )
        .with_entities(ActivityBlackList.user_id)
        .all()
    )
    invalid_lottery_user_ids = [item.user_id for item in invalid_lottery]
    fraud_user_ids = set(simlars_email_user_ids) & set(invalid_lottery_user_ids)
    return fraud_user_ids


def _extract_random_airdrop_fraud_user_ids(airdrop_activity, anti_fraud_score):
    # 上传活动中被判断为相似邮箱 >= 3，且活动资格无效的用户上传到羊毛党系统中
    simlars_email_user_ids = anti_fraud_score.load_similar_email_user_ids(
        above_similar_emails_cnt=3,
    )
    invalid_lottery = (
        AirdropActivityLotteryHistory.query.filter(
            AirdropActivityLotteryHistory.airdrop_activity_id == airdrop_activity.id,
            AirdropActivityLotteryHistory.state
            == AirdropActivityLotteryHistory.StateType.INVALID,
        )
        .with_entities(AirdropActivityLotteryHistory.user_id)
        .all()
    )
    invalid_lottery_user_ids = [item.user_id for item in invalid_lottery]
    fraud_user_ids = set(simlars_email_user_ids) & set(invalid_lottery_user_ids)
    return fraud_user_ids


def _extract_dibs_fraud_user_ids(discount_activity, anti_fraud_score):
    # 上传活动中被判断为相似邮箱 >= 3，且活动资格无效的用户上传到羊毛党系统中
    simlars_email_user_ids = anti_fraud_score.load_similar_email_user_ids(
        above_similar_emails_cnt=3,
    )
    invalid_lottery = (
        DiscountActivityLotteryHistory.query.filter(
            DiscountActivityLotteryHistory.discount_activity_id == discount_activity.id,
            DiscountActivityLotteryHistory.state
            == DiscountActivityLotteryHistory.StateType.INVALID,
        )
        .with_entities(DiscountActivityLotteryHistory.user_id)
        .all()
    )
    invalid_lottery_user_ids = [item.user_id for item in invalid_lottery]
    fraud_user_ids = set(simlars_email_user_ids) & set(invalid_lottery_user_ids)
    return fraud_user_ids


def _extract_deposit_bonus_fraud_user_ids(activity, anti_fraud_score):
    # 上传活动中被判断为相似邮箱 >= 3，且活动资格无效的用户上传到羊毛党系统中
    simlars_email_user_ids = anti_fraud_score.load_similar_email_user_ids(
        above_similar_emails_cnt=3,
    )
    invalid_lottery = (
        DepositBonusActivityUserInfo.query.filter(
            DepositBonusActivityUserInfo.activity_id == activity.id,
            DepositBonusActivityUserInfo.status == DepositBonusActivityUserInfo.Status.INVALID,
        )
        .with_entities(DepositBonusActivityUserInfo.user_id)
        .all()
    )
    invalid_lottery_user_ids = [item.user_id for item in invalid_lottery]
    fraud_user_ids = set(simlars_email_user_ids) & set(invalid_lottery_user_ids)
    return fraud_user_ids


def _report_fraud_user_to_anti_fraud_system(user_ids, activity_id, activity_name):
    client = AntiFraudClient()

    risk_users, remark = [], f"{activity_id}-{activity_name}"
    for user_id in user_ids:
        risk_users.append(dict(user_id=user_id, remark=remark))
    try:
        # report as admin, so user_id is 0
        client.import_risk_users(0, risk_users)
    except Exception as e:
        current_app.logger.error(f"report fraud user failed with error: {e}")
        return False
    return True


@scheduled(crontab(minute="*/5"))
@lock_call()
def report_fraud_user_to_anti_fraud_system():
    ## 交易排位赛
    model = TradeRankActivity
    trade_rank_list: List[TradeRankActivity] = (
        model.query.filter(
            model.status == model.Status.FINISHED,
            model.started_at > now() - timedelta(days=30),
            model.ended_at < now(),
        )
        .with_entities(model.id, model.activity_id)
        .order_by(model.id)
        .all()
    )
    for trand_rank in trade_rank_list:
        anti_fraud_score = AntiFraudScore.query.filter_by(
            activity_type=AntiFraudScore.ActivityType.TRADE_RANK,
            activity_id=trand_rank.activity_id,
            status=AntiFraudScore.Status.FINISHED,
        ).first()
        if not anti_fraud_score:
            continue
        fraud_user_ids = _extract_trade_rank_fraud_user_ids(
            trand_rank, anti_fraud_score
        )
        # get title from detail record
        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id == trand_rank.id,
        ).all()
        title_map = dict()
        for item in details:
            title_map[item.lang.name] = item.title
        title = (
            title_map[Language.ZH_HANS_CN.name]
            if Language.ZH_HANS_CN.name in title_map
            else title_map.get(Language.ZH_HANS_CN.name, "UNKNOWN")
        )
        if (not fraud_user_ids) or _report_fraud_user_to_anti_fraud_system(
            fraud_user_ids, trand_rank.activity_id, title
        ):
            anti_fraud_score.status = AntiFraudScore.Status.REPORTED
            db.session.commit()
            continue

    ## 随机空投
    model = AirdropActivity
    airdrop_list: List[AirdropActivity] = (
        model.query.filter(
            model.status == model.StatusType.FINISHED,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id, model.activity_id, model.name)
        .order_by(model.id)
        .all()
    )
    for airdrop in airdrop_list:
        anti_fraud_score = AntiFraudScore.query.filter_by(
            activity_type=AntiFraudScore.ActivityType.AIRDROP,
            activity_id=airdrop.activity_id,
            status=AntiFraudScore.Status.FINISHED,
        ).first()
        if not anti_fraud_score:
            continue
        fraud_user_ids = _extract_random_airdrop_fraud_user_ids(
            airdrop, anti_fraud_score
        )
        if (not fraud_user_ids) or _report_fraud_user_to_anti_fraud_system(
            fraud_user_ids, airdrop.activity_id, airdrop.name
        ):
            anti_fraud_score.status = AntiFraudScore.Status.REPORTED
            db.session.commit()
            continue

    ## Dibs
    model = DiscountActivity
    discount_list: List[DiscountActivity] = (
        model.query.filter(
            model.status == model.StatusType.FINISHED,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id, model.activity_id, model.name)
        .order_by(model.id)
        .all()
    )
    for discount in discount_list:
        anti_fraud_score = AntiFraudScore.query.filter_by(
            activity_type=AntiFraudScore.ActivityType.DIBS,
            activity_id=discount.activity_id,
            status=AntiFraudScore.Status.FINISHED,
        ).first()
        if not anti_fraud_score:
            continue
        fraud_user_ids = _extract_dibs_fraud_user_ids(discount, anti_fraud_score)
        if (not fraud_user_ids) or _report_fraud_user_to_anti_fraud_system(
            fraud_user_ids, discount.activity_id, discount.name
        ):
            anti_fraud_score.status = AntiFraudScore.Status.REPORTED
            db.session.commit()
            continue

    ## 充值福利
    model = DepositBonusActivity
    deposit_bonus_list: List[DepositBonusActivity] = (
        model.query.filter(
            model.status == model.StatusType.FINISHED,
            model.start_time > now() - timedelta(days=30),
            model.end_time < now(),
        )
        .with_entities(model.id, model.name)
        .order_by(model.id)
        .all()
    )
    for deposit_bonus in deposit_bonus_list:
        config_rows = DepositBonusActivityConfig.query.with_entities(
            DepositBonusActivityConfig.id,
            DepositBonusActivityConfig.deposit_bonus_id,
            DepositBonusActivityConfig.activity_id,
        ).filter(
            DepositBonusActivityConfig.deposit_bonus_id == deposit_bonus.id
        ).all()
        for cfg in config_rows:
            anti_fraud_score = AntiFraudScore.query.filter_by(
                activity_type=AntiFraudScore.ActivityType.DEPOSIT_BONUS,
                activity_id=cfg.activity_id,
                status=AntiFraudScore.Status.FINISHED,
            ).first()
            if not anti_fraud_score:
                continue
            fraud_user_ids = _extract_deposit_bonus_fraud_user_ids(
                cfg, anti_fraud_score
            )
            if (not fraud_user_ids) or _report_fraud_user_to_anti_fraud_system(
                    fraud_user_ids, cfg.activity_id, deposit_bonus.name
            ):
                anti_fraud_score.status = AntiFraudScore.Status.REPORTED
                db.session.commit()
                continue

@scheduled(crontab(hour='*/1', minute="0"))
@lock_call()
def report_fraud_referree_to_anti_fraud_system():
    ## 羊毛被邀请人
    report_type = 'fraud_referree'
    cursor_cache = AntiFraudReportCursorCache()
    _now = now()
    _now_ts = int(_now.timestamp())
    if not (_last_time := cursor_cache.hget(report_type)):
        cursor_cache.hset(report_type, str(_now_ts))
        return

    client = AntiFraudClient()
    fraud_referrer_infos = client.get_all_fraud_referrer_infos()

    new_referrer_ids = []
    old_referrer_ids = []
    for referrer_info in fraud_referrer_infos:
        if int(referrer_info.get('created_at', 0)) >= int(_last_time):
            new_referrer_ids.append(referrer_info['config_key'])
        else:
            old_referrer_ids.append(referrer_info['config_key'])

    referral_histories_by_new = []
    referral_histories_by_old = []
    if new_referrer_ids:
        referral_histories_by_new = ReferralHistory.query.with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).filter(
            ReferralHistory.referrer_id.in_(new_referrer_ids),
        ).all()

    if old_referrer_ids:
        referral_histories_by_old = ReferralHistory.query.with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).filter(
            ReferralHistory.referrer_id.in_(old_referrer_ids),
            ReferralHistory.created_at >= timestamp_to_datetime(int(_last_time)),
        ).all()

    fraud_referrer_email_map = {}
    for ids in batch_iter(new_referrer_ids + old_referrer_ids, 5000):
        users = User.query.filter(
            User.id.in_(ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        fraud_referrer_email_map.update({user.id: user.email for user in users})

    report_data = []
    for referral_history in itertools.chain(referral_histories_by_new, referral_histories_by_old):
        referrer_email = fraud_referrer_email_map.get(
            referral_history.referrer_id, referral_history.referrer_id
        )
        report_data.append(dict(
            user_id=referral_history.referree_id,
            remark=f'羊毛邀请用户-邀请人为{referrer_email}'
        ))

    try:
        # report as admin, so user_id is 0
        if report_data:
            client.import_risk_users(0, report_data)
    except Exception as e:
        current_app.logger.error(f"report fraud user failed with error: {e}")
        return

    cursor_cache.hset(report_type, str(_now_ts))
