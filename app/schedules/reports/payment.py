# -*- coding: utf-8 -*-
from copy import deepcopy
from collections import defaultdict
from decimal import Decimal
from datetime import date, timedelta

from pyroaring import BitMap

from app.business import (
    CeleryQueues,
    scheduled,
    crontab,
    lock_call,
    AssetPrice,
)
from app.business.utils import yield_query_records_by_time_range
from app.models import (
    db,
    DailyPaymentSiteReport,
    DailyPaymentAssetReport,
    MonthlyPaymentSiteReport,
    MonthlyPaymentAssetReport,
)
from app.models.payment import PaymentTransaction, PaymentAssetHedgingHistory, SysAssetFixedExchangeHistory
from app.utils import today, quantize_amount, next_month, batch_iter, route_module_to_celery_queue
from app.utils.date_ import date_to_datetime
from app.schedules.reports.utils import get_monthly_report_date


route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def build_daily_payment_report_data(start_date: date, end_date: date) -> dict[str, dict]:
    from app.assets.asset import list_all_assets

    model = PaymentTransaction
    query = yield_query_records_by_time_range(
        model,
        start_time=date_to_datetime(start_date),
        end_time=date_to_datetime(end_date),
        select_fields=(
            model.payer_id,
            model.pay_asset,
            model.pay_amount,
            model.pay_fee_amount,
            model.receiver_id,
            model.receive_asset,
            model.receive_amount,
            model.status,
        ),
    )
    rows: list[model] = []
    hg_trac_ids = []
    for row in query:
        if row.status not in [model.Status.PROCESSING, model.Status.FINISHED]:
            continue
        rows.append(row)
        if row.pay_asset != row.receive_asset:
            hg_trac_ids.append(row.id)

    hg_model = PaymentAssetHedgingHistory
    hedging_rows: list[hg_model] = []
    for ch_trac_ids in batch_iter(hg_trac_ids, 5000):
        ch_hedging_rows = hg_model.query.filter(
            hg_model.transaction_id.in_(ch_trac_ids),
            hg_model.status == hg_model.Status.FINISHED,
        ).all()
        hedging_rows.extend(ch_hedging_rows)

    hedging_ids = [i.id for i in hedging_rows]
    hedging_exc_row_map: dict[int, SysAssetFixedExchangeHistory] = {}
    for ch_hd_ids in batch_iter(hedging_ids, 5000):
        ch_hedging_exc_rows = SysAssetFixedExchangeHistory.query.filter(
            SysAssetFixedExchangeHistory.biz_id.in_(ch_hd_ids),
            SysAssetFixedExchangeHistory.biz_type == SysAssetFixedExchangeHistory.BizType.PAYMENT_HEDGING,
            SysAssetFixedExchangeHistory.status == SysAssetFixedExchangeHistory.Status.FINISHED,
        ).all()
        hedging_exc_row_map.update({i.biz_id: i for i in ch_hedging_exc_rows})

    zero = Decimal()
    data_template = {
        "pay_usd": zero,
        "fee_usd": zero,
        "exchange_usd": zero,
        "exchange_profit_usd": zero,
        "exchange_loss_usd": zero,
        "pay_user_ids": set(),
        "receive_user_ids": set(),
        "pay_count": 0,
        "exchange_count": 0,
    }

    asset_price_map = AssetPrice.get_close_price_map(start_date)
    pay_asset_data_dict = {}
    for r in rows:
        data = pay_asset_data_dict.get(r.pay_asset, deepcopy(data_template))
        pay_asset_data_dict[r.pay_asset] = data

        pay_usd = asset_price_map.get(r.pay_asset, zero) * r.pay_amount
        fee_usd = asset_price_map.get(r.pay_asset, zero) * r.pay_fee_amount
        data["pay_usd"] += pay_usd
        data["fee_usd"] += fee_usd
        data["pay_user_ids"].add(r.payer_id)
        data["receive_user_ids"].add(r.receiver_id)
        data["pay_count"] += 1
        if r.pay_asset != r.receive_asset:
            data["exchange_count"] += 1

    for r in hedging_rows:
        if r.source_asset not in pay_asset_data_dict:
            continue
        exchange_usd = asset_price_map.get(r.source_asset, zero) * r.source_filled_amount
        target_pl = asset_price_map.get(r.target_asset, zero) * (r.target_filled_amount - r.target_amount)
        data = pay_asset_data_dict[r.source_asset]
        data["exchange_usd"] += exchange_usd
        hedging_exc_row = hedging_exc_row_map.get(r.id)
        if hedging_exc_row:
            _, _, profit_data = hedging_exc_row.get_asset_exchanged_data()
            for p_asset, p_amount in profit_data.items():
                _profit = asset_price_map.get(p_asset, zero) * p_amount
                data["exchange_profit_usd"] += _profit
        if target_pl < 0:
            data["exchange_loss_usd"] += target_pl

    # 填充有之前日报数据，但当天没数据的币种
    _all_assets = set(list_all_assets())
    _pay_assets = {i.pay_asset for i in DailyPaymentAssetReport.query.with_entities(
        DailyPaymentAssetReport.pay_asset.distinct().label('pay_asset')
    ).all()}
    _pay_assets = _pay_assets & _all_assets
    for _pa in _pay_assets:
        if _pa not in pay_asset_data_dict:
            pay_asset_data_dict[_pa] = deepcopy(data_template)

    # 汇总全部币种
    site_data = deepcopy(data_template)
    for data in pay_asset_data_dict.values():
        for k, v in data.items():
            if isinstance(v, set):
                site_data[k].update(v)
            elif isinstance(v, (Decimal, int)):
                site_data[k] += v
    pay_asset_data_dict[""] = site_data
    return pay_asset_data_dict


def update_daily_payment_report(start_date: date, end_date: date):
    asset_data_dict = build_daily_payment_report_data(start_date, end_date)

    asset_data = asset_data_dict.pop("")
    site_row: DailyPaymentSiteReport = DailyPaymentSiteReport.get_or_create(report_date=start_date)
    site_row.pay_usd = quantize_amount(asset_data['pay_usd'], 2)
    site_row.fee_usd = quantize_amount(asset_data['fee_usd'], 2)
    site_row.exchange_usd = quantize_amount(asset_data['exchange_usd'], 2)
    site_row.exchange_profit_usd = quantize_amount(asset_data['exchange_profit_usd'], 2)
    site_row.exchange_loss_usd = quantize_amount(asset_data['exchange_loss_usd'], 2)
    site_row.pay_count = asset_data['pay_count']
    site_row.exchange_count = asset_data['exchange_count']
    site_row.avg_exchange_usd = quantize_amount(site_row.exchange_usd / site_row.exchange_count, 2) if site_row.exchange_count else 0

    site_cur_user_bitmap = BitMap()
    pay_and_receive_user_ids = asset_data['pay_user_ids'] | asset_data['receive_user_ids']
    site_cur_user_bitmap.update(pay_and_receive_user_ids)
    last_side_row: DailyPaymentSiteReport = DailyPaymentSiteReport.query.filter(
        DailyPaymentSiteReport.report_date < start_date,
    ).order_by(DailyPaymentSiteReport.report_date.desc()).first()
    if not last_side_row:
        site_his_user_bitmap = BitMap()
        new_user_count = len(pay_and_receive_user_ids)
    else:
        site_his_user_bitmap: BitMap = BitMap.deserialize(last_side_row.history_user_bitmap)
        new_user_bitmap = site_cur_user_bitmap.difference(site_his_user_bitmap)  # 删除老的
        new_user_count = len(new_user_bitmap)
    site_row.user_count = len(pay_and_receive_user_ids)
    site_row.new_user_count = new_user_count
    site_row.user_bitmap = site_cur_user_bitmap.serialize()  # noqa
    site_his_user_bitmap.update(pay_and_receive_user_ids)
    site_row.history_user_bitmap = site_his_user_bitmap.serialize()  # noqa
    db.session.add(site_row)

    for pay_asset, asset_data in asset_data_dict.items():
        p_asset_row: DailyPaymentAssetReport = DailyPaymentAssetReport.get_or_create(
            report_date=start_date, pay_asset=pay_asset,
        )
        p_asset_row.pay_usd = quantize_amount(asset_data["pay_usd"], 2)
        p_asset_row.exchange_usd = quantize_amount(asset_data["exchange_usd"], 2)
        pay_user_ids = asset_data["pay_user_ids"]
        p_asset_row.pay_user_count = len(pay_user_ids)
        p_asset_row.pay_count = asset_data['pay_count']
        p_asset_row.exchange_count = asset_data['exchange_count']
        p_asset_row.avg_exchange_usd = quantize_amount(p_asset_row.exchange_usd / p_asset_row.exchange_count, 2) \
            if p_asset_row.exchange_count else 0
        pay_user_bitmap = BitMap()
        pay_user_bitmap.update(pay_user_ids)
        p_asset_row.pay_user_bitmap = pay_user_bitmap.serialize()  # noqa
        db.session.add(p_asset_row)
    db.session.commit()


@scheduled(crontab(minute="15,25", hour="2"))
@lock_call()
def update_daily_payment_report_schedule():
    """收付款全站日报、币种日报"""
    today_ = today()
    last_record = DailyPaymentSiteReport.query.order_by(DailyPaymentSiteReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        first_r: PaymentTransaction = PaymentTransaction.query.order_by(PaymentTransaction.id.asc()).first()
        start_date = first_r.created_at.date()

    while start_date < today_:
        end_date = start_date + timedelta(days=1)
        update_daily_payment_report(start_date, end_date)
        start_date = end_date


def update_monthly_payment_report(start_date: date, end_date: date):
    daily_site_rows: list[DailyPaymentSiteReport] = DailyPaymentSiteReport.query.filter(
        DailyPaymentSiteReport.report_date >= start_date,
        DailyPaymentSiteReport.report_date < end_date,
    ).all()
    pay_usd = fee_usd = exchange_usd = exchange_profit_usd = exchange_loss_usd = Decimal()
    pay_and_receive_user_ids = set()
    pay_count = exchange_count = 0
    for r in daily_site_rows:
        pay_usd += r.pay_usd
        fee_usd += r.fee_usd
        exchange_usd += r.exchange_usd
        exchange_profit_usd += r.exchange_profit_usd
        exchange_loss_usd += r.exchange_loss_usd
        pay_count += r.pay_count
        exchange_count += r.exchange_count
        pay_and_receive_user_ids.update(list(BitMap.deserialize(r.user_bitmap)))
    site_row: MonthlyPaymentSiteReport = MonthlyPaymentSiteReport.get_or_create(report_date=start_date)
    site_row.pay_usd = quantize_amount(pay_usd, 2)
    site_row.fee_usd = quantize_amount(fee_usd, 2)
    site_row.exchange_usd = quantize_amount(exchange_usd, 2)
    site_row.exchange_profit_usd = quantize_amount(exchange_profit_usd, 2)
    site_row.exchange_loss_usd = quantize_amount(exchange_loss_usd, 2)
    site_row.pay_count = pay_count
    site_row.exchange_count = exchange_count
    site_row.avg_exchange_usd = quantize_amount(site_row.exchange_usd / site_row.exchange_count, 2) if site_row.exchange_count else 0

    site_cur_user_bitmap = BitMap()
    site_cur_user_bitmap.update(pay_and_receive_user_ids)
    last_side_row: MonthlyPaymentSiteReport = MonthlyPaymentSiteReport.query.filter(
        MonthlyPaymentSiteReport.report_date < start_date,
    ).order_by(MonthlyPaymentSiteReport.report_date.desc()).first()
    if not last_side_row:
        site_his_user_bitmap = BitMap()
        new_user_count = len(pay_and_receive_user_ids)
    else:
        site_his_user_bitmap: BitMap = BitMap.deserialize(last_side_row.history_user_bitmap)
        new_user_bitmap = site_cur_user_bitmap.difference(site_his_user_bitmap)  # 删除老的
        new_user_count = len(new_user_bitmap)
    site_row.user_count = len(pay_and_receive_user_ids)
    site_row.new_user_count = new_user_count
    site_row.user_bitmap = site_cur_user_bitmap.serialize()  # noqa
    site_his_user_bitmap.update(pay_and_receive_user_ids)
    site_row.history_user_bitmap = site_his_user_bitmap.serialize()  # noqa
    db.session.add(site_row)

    daily_asset_rows: list[DailyPaymentAssetReport] = DailyPaymentAssetReport.query.filter(
        DailyPaymentAssetReport.report_date >= start_date,
        DailyPaymentAssetReport.report_date < end_date,
    ).all()
    asset_daily_rows_map = defaultdict(list)
    for r in daily_asset_rows:
        asset_daily_rows_map[r.pay_asset].append(r)

    for pay_asset, asset_daily_rows in asset_daily_rows_map.items():
        asset_daily_rows: list[DailyPaymentAssetReport]
        pay_usd = exchange_usd = Decimal()
        pay_user_ids = set()
        pay_count = exchange_count = 0
        for d_row in asset_daily_rows:
            pay_usd += d_row.pay_usd
            exchange_usd += d_row.exchange_usd
            pay_count += d_row.pay_count
            exchange_count += d_row.exchange_count
            pay_user_ids.update(list(BitMap.deserialize(d_row.pay_user_bitmap)))

        asset_row: MonthlyPaymentAssetReport = MonthlyPaymentAssetReport.get_or_create(
            report_date=start_date, pay_asset=pay_asset
        )
        asset_row.pay_usd = quantize_amount(pay_usd, 2)
        asset_row.exchange_usd = quantize_amount(exchange_usd, 2)
        asset_row.pay_user_count = len(pay_user_ids)
        asset_row.pay_count = pay_count
        asset_row.exchange_count = exchange_count
        asset_row.avg_exchange_usd = quantize_amount(asset_row.exchange_usd / asset_row.exchange_count, 2) \
            if asset_row.exchange_count else 0

        db.session.add(asset_row)
    db.session.commit()


@scheduled(crontab(minute="25,35", hour="2"))
@lock_call()
def update_monthly_payment_report_schedule():
    """收付款全站月报、币种月报"""
    today_ = today()
    cur_month = date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyPaymentSiteReport, DailyPaymentSiteReport)
    if not start_month:
        start_month = cur_month

    if start_month > cur_month:
        start_month = cur_month
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_payment_report(start_month, end_month)
        start_month = end_month
