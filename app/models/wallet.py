# -*- coding: utf-8 -*-

from __future__ import annotations

import json
from collections import defaultdict
from enum import Enum, auto as enum_auto
from datetime import datetime, timedelta, date as datetime_date
from decimal import Decimal
from flask_babel import gettext as _
from werkzeug.utils import cached_property
from typing import List, Dict, Tuple, Optional, Union
from sqlalchemy import func, or_, tuple_
from sqlalchemy.orm import Session

from ..utils import truncate_text, now
from .base import db, ModelBase, new_session
from ..utils.date_ import date_to_datetime, today
from ..common import Language, BalanceBusiness


class WithdrawalAddress(ModelBase):

    MAX_NUM_PER_CHAIN = 300

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)  # asset maybe empty.
    chain = db.Column(db.String(32), nullable=False, index=True)
    address = db.Column(db.String(256), nullable=False)
    memo = db.Column(db.String(256), nullable=False, default='')
    attachment = db.Column(db.String(1024))
    remark = db.Column(db.String(256), nullable=False, default='')
    usage_count = db.Column(db.Integer, nullable=False, default=0)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.Index('user_id_chain_idx', 'user_id', 'chain'),
    )

    @classmethod
    def get_address(cls,
                    user_id: int,
                    asset: str,
                    chain: str,
                    address: str,
                    memo: str) -> Optional['WithdrawalAddress']:
        query = cls.query.filter(cls.user_id == user_id,
                                 cls.asset == asset,
                                 cls.chain == chain,
                                 cls.address == func.binary(address),
                                 cls.memo == func.binary(memo))
        return query.first()

class LocalWithdrawalAddress(ModelBase):

    MAX_NUM = 300

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    account = db.Column(db.String(256), nullable=False, index=True)
    remark = db.Column(db.String(256), nullable=False, default='')
    usage_count = db.Column(db.Integer, nullable=False, default=0)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.UniqueConstraint('user_id', 'account', name='user_account_uniq'),
    )

    @classmethod
    def get_address(cls, user_id: int, account: str) -> Optional['LocalWithdrawalAddress']:
        row = cls.query \
            .filter(cls.user_id == user_id,
                    cls.account == account) \
            .first()
        return row


class ApiWithdrawalAddress(ModelBase):

    class Type(Enum):
        ON_CHAIN = '普通提现'
        LOCAL = '站内转账'

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    type = db.Column(db.Enum(Type), nullable=False)
    asset = db.Column(db.String(32), index=True)    # asset maybe empty
    chain = db.Column(db.String(32), index=True)
    address = db.Column(db.String(256), nullable=False)
    memo = db.Column(db.String(256))
    attachment = db.Column(db.String(1024))
    remark = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.Index('user_id_chain_idx', 'user_id', 'chain'),
    )

    @classmethod
    def get_on_chain_address(cls,
                            user_id: int,
                            asset: str,
                            chain: str,
                            address: str,
                            memo: str) -> Optional['ApiWithdrawalAddress']:
        row = cls.query \
                .filter(cls.user_id == user_id,
                        cls.asset == asset,
                        cls.chain == chain,
                        cls.address == func.binary(address),
                        cls.memo == func.binary(memo)) \
                .first()
        return row

    @classmethod
    def get_local_address(cls, user_id: int, account: str) -> Optional['ApiWithdrawalAddress']:
        row = cls.query.filter(cls.user_id == user_id,
                               cls.address == account).first()
        return row


class ApiWithdrawalAddressWhiteListUser(ModelBase):
    """API提现地址白名单用户
    在此名单的用户，API提现时的提现地址可以不在ApiWithdrawalAddress表里
    """
    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(256), nullable=False, default='')

    @classmethod
    def is_whitelist_user(cls, user_id: int) -> bool:
        r = cls.query.filter(
            cls.user_id == user_id,
            cls.status == cls.Status.VALID,
        ).first()
        return bool(r)


class WithdrawalPrivilegedUser(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False,
                        unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128))

    user = db.relationship('User', foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile
        )

    @classmethod
    def has_user(cls, user_id: int) -> bool:
        return cls.query \
            .filter(cls.user_id == user_id,
                    cls.status == cls.Status.VALID) \
            .first() is not None


class Deposit(ModelBase):

    class Type(Enum):
        ON_CHAIN = '普通充值'
        LOCAL = '站内转账'
        ABNORMAL = '异常充值'   # not used now

    class Status(Enum):
        TOO_SMALL = '数额太小'
        PROCESSING = '处理中'
        CONFIRMING = '确认中'
        CANCELLED = '已取消'
        FINISHED = '已完成'
        TO_HOT = '已转热钱包'
        EXCEPTION = '异常'

    wallet_deposit_id = db.Column(db.Integer, unique=True) # for old records, it's NULL
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    type = db.Column(db.Enum(Type), nullable=False,
                     default=Type.ON_CHAIN, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)
    chain = db.Column(db.String(32), index=True)  # `None` for local deposit
    address = db.Column(db.String(256), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    memo = db.Column(db.String(1024), nullable=False, default='')
    attachment = db.Column(db.String(1024))
    sender_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    tx_id = db.Column(db.String(128), nullable=False, default='', index=True)
    vout = db.Column(db.Integer, nullable=False, default=0)
    block_height = db.Column(db.Integer, nullable=False, default=0)
    confirmations = db.Column(db.Integer, nullable=False, default=0)
    confirmed_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.Enum(Status), nullable=False,
                       default=Status.PROCESSING, index=True)

    user = db.relationship(
        'User',
        foreign_keys=[user_id],
        backref=db.backref('deposits', lazy='dynamic'))
    sender_user = db.relationship(
        'User',
        foreign_keys=[sender_user_id],
        backref=db.backref('sent_deposits', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint(
            'tx_id', 'vout', 'chain', 'asset',
            name='tx_vout_asset_chain_uniq'),
    )

    def __repr__(self):
        return f'{type(self).__name__}(' \
               f'{self.id}, {self.asset}-{self.chain}, ' \
               f'user={self.user_id}, address={self.address!r}, ' \
               f'memo={truncate_text(self.memo, 16)!r}, ' \
               f'amount={self.amount}, ' \
               f'tx_id={self.tx_id!r}, vout={self.vout})'

    @property
    def is_from_deleted_address(self) -> bool:
        """是否是 来自旧地址的充值"""
        attachment = json.loads(self.attachment) if self.attachment else {}
        if attachment.get('abnormal_source') == 'DELETED_ADDRESS':
            return True
        return False


class DepositAudit(ModelBase):

    class Status(Enum):
        AUDIT_REQUIRED = '待审核'
        AUDITED = '已通过'
        CANCELLED = '已取消'

        INFO_REQUIRED = '待提供资料'
        INFO_AUDIT_REQUIRED = '提供资料待审核'
        EXTRA_INFO_REQUIRED = '待提供额外资料'
        FREEZING = '持续冻结'

    class Type(Enum):
        MANUAL_AUDIT = "充值需要人工审核"
        PRIVACY_WITHOUT_KYC = "未实名隐私币充值审核"
        KYT_RC = "KYT高风险充值"
        USER_DEPOSIT_RC = "用户维度单笔充值风控"  # 仅展示保留
        KYT_BLACKLISTED_RC = "充值地址黑名单"

    KYT_TYPES = [
        Type.KYT_RC,
        Type.KYT_BLACKLISTED_RC,
    ]
    KYT_FROZEN_STATUSES = [
        Status.INFO_AUDIT_REQUIRED,
        Status.INFO_REQUIRED,
        Status.EXTRA_INFO_REQUIRED,
        Status.FREEZING,
        Status.AUDIT_REQUIRED,
    ]

    __table_args__ = (db.UniqueConstraint("deposit_id", "type", name="deposit_id_type_unique"),)

    deposit_id = db.Column(db.Integer, db.ForeignKey('deposit.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.AUDIT_REQUIRED)
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    type = db.Column(db.StringEnum(Type), nullable=False)

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)
    edd_id = db.Column(db.Integer, nullable=True, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    remark = db.Column(db.String(256), default='')
    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)
    rejection_reason = db.Column(db.String(256), nullable=True)  # KycVerification.RejectionReason.name
    custom_rejection_reason = db.Column(db.String(512), nullable=True)

    @classmethod
    def link_edd(cls, user_id, deposit_id, edd_id, auto_commit=False):
        rows = cls.query.filter(
            DepositAudit.user_id == user_id,
            DepositAudit.deposit_id == deposit_id,
        ).all()
        for row in rows:
            if row.edd_id:
                continue
            row.edd_id = edd_id
            db.session.flush()
            if auto_commit:
                db.session.commit()

    def get_reject_reason(self, translate=True):
        from app.models import KycVerification

        if self.is_custom_reason:
            return self.custom_rejection_reason
        else:
            rejection_reason = None
            if self.rejection_reason:
                rejection_reason = KycVerification.RejectionReason[self.rejection_reason]
            value = rejection_reason.value if rejection_reason else None
            if translate:
                return _(value) if value else None
            else:
                return value if value else None


class EDDAudit(ModelBase):

    class Source(Enum):
        DEPOSIT_AUDIT = '充值审核'
        MANUAL = '手动发起'

    class Status(Enum):
        CREATED = '已创建'
        INFO_REQUIRED = '待提供资料'
        AUDIT_REQUIRED = '提供资料待审核'
        EXTRA_INFO_REQUIRED = '待提供额外资料'
        FREEZING = '持续冻结'
        AUDITED = '已通过'

    SYNC_STATUSES = {
        Status.INFO_REQUIRED: DepositAudit.Status.INFO_REQUIRED,
        Status.AUDIT_REQUIRED: DepositAudit.Status.INFO_AUDIT_REQUIRED,
        Status.EXTRA_INFO_REQUIRED: DepositAudit.Status.EXTRA_INFO_REQUIRED,
    }
    STATUS_MAP = {
        Status.INFO_REQUIRED: DepositAudit.Status.INFO_REQUIRED,
        Status.AUDIT_REQUIRED: DepositAudit.Status.INFO_AUDIT_REQUIRED,
        Status.EXTRA_INFO_REQUIRED: DepositAudit.Status.EXTRA_INFO_REQUIRED,
        Status.FREEZING: DepositAudit.Status.FREEZING,
        Status.AUDITED: DepositAudit.Status.AUDITED,
    }

    class RiskLevel(Enum):
        HIGH = 'High'
        SEVERE = 'Severe'

    # KYT
    sender_risk_id = db.Column(db.Integer, nullable=True, comment='充值风险评估ID')
    wallet_deposit_id = db.Column(db.Integer, nullable=True, unique=True)
    address_from = db.Column(db.String(256), nullable=True, index=True)  # coinfirm 是发送方地址；chainalysis 是接收地址
    exchange_from = db.Column(db.String(256), default='', index=True)
    # coinfirm 是发送方地址；chainalysis 是交易ID+接收地址
    assess_object = db.Column(db.String(512), default='', index=True, comment='评估对象')
    assessor = db.Column(db.String(32), default='', comment='评估方')
    risk_score = db.Column(db.Integer, nullable=False, default=0)
    risk_level = db.Column(db.StringEnum(RiskLevel), nullable=False, default=RiskLevel.HIGH)
    # Common
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    deposit_id = db.Column(db.Integer, nullable=True, unique=True)
    source = db.Column(db.StringEnum(Source), nullable=False, index=True)
    remark = db.Column(db.String(256), nullable=False, default='')
    info_updated_at = db.Column(db.DateTime, index=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)
    created_by = db.Column(db.Integer, index=True, comment='手动发起类型的发起人')
    audited_by = db.Column(db.Integer, index=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)
    rejection_reason = db.Column(db.String(256), nullable=True)  # KycVerification.RejectionReason.name
    custom_rejection_reason = db.Column(db.String(512), nullable=True)

    @property
    def address_result(self):
        if not self.exchange_from:
            return self.address_from
        return f'{self.address_from}({self.exchange_from})'

    @property
    def risk_result(self):
        return f'risk_score={self.risk_score}, risk_level={self.risk_level.name}'

    def get_reject_reason(self, translate=True):
        from app.models import KycVerification

        if self.is_custom_reason:
            return self.custom_rejection_reason
        else:
            rejection_reason = None
            if self.rejection_reason:
                rejection_reason = KycVerification.RejectionReason[self.rejection_reason]
            value = rejection_reason.value if rejection_reason else None
            if translate:
                return _(value) if value else None
            else:
                return value if value else None


class EDDAuditDetail(ModelBase):

    class IncomeSource(Enum):
        SALARY = '薪资'
        SELF_EMPLOYED = '自雇'
        PENSION = '退休金'
        INHERIT = '继承'
        CRYPTOCURRENCY_MINING = '加密货币挖矿'
        CRYPTOCURRENCY_TRADING = '加密货币交易收入'
        DONATION = '捐赠（如果您是学生并领取津贴，请使用此选项作为证明）'
        BANK_LOAN = '银行贷款'
        COMPANY_PROFIT = '公司获利（股票/股利）'
        INVESTMENT_INCOME = '投资收入（例如股票、债券、投资基金）'
        RENT = '租金/租赁收入'
        ESTATE_OR_OTHERS = '出售不动产或其他财产所得'
        OTHERS = '其他'

    class DepositSource(Enum):
        WITHDRAW_FROM_OTHERS = '资金来自其他交易平台提现'
        FIAT_FROM_OTHERS = '资金来自其他平台法币交易兑换的资金'
        OTHER_WALLETS = '资金来自其他钱包'
        OTC = '与他人私下交易后，对方提币到您的CoinEx帐户'
        OTHERS = '其他'

    edd_id = db.Column(db.Integer, nullable=False, index=True)
    # basic info
    name = db.Column(db.String(128), nullable=False)
    address = db.Column(db.String(512), nullable=False)
    mobile = db.Column(db.String(32), nullable=False)
    occupation = db.Column(db.String(128), nullable=False)
    employer = db.Column(db.String(128), nullable=False)
    employer_address = db.Column(db.String(512), nullable=False)
    # financial info
    annual_income = db.Column(db.String(32), nullable=False, comment='年收入金额（USD）')
    # [x.name for x in IncomeSource]
    income_sources = db.Column(db.String(512), nullable=False)
    income_file_ids = db.Column(db.String(1024), nullable=False)
    income_source_desc = db.Column(db.String(1024), nullable=True, comment='用户选填')
    # 当 edd 关联某笔充值时，需逻辑上保证该字段必填
    # [x.name for x in DepositSource]
    deposit_sources = db.Column(db.String(512), nullable=True)
    deposit_file_ids = db.Column(db.String(1024), nullable=True)
    deposit_source_desc = db.Column(db.String(1024), nullable=True, comment='用户选填')
    has_legal_proceedings = db.Column(db.Boolean, nullable=False, default=False)
    legal_proceedings_desc = db.Column(db.String(1024), nullable=True, comment='过去 5 年内是否受到过任何监管调查、制裁或法律诉讼？')
    has_pep = db.Column(db.Boolean, nullable=False, default=False)
    pep_desc = db.Column(db.String(1024), nullable=True, comment='是否与政治公众人物 (PEP) 有任何关系？')

    id_with_photo_file_ids = db.Column(db.String(1024), nullable=False)
    address_file_ids = db.Column(db.String(1024), nullable=False)
    supplement_file_ids = db.Column(db.String(1024), nullable=True)
    supplement_desc = db.Column(db.String(2048), nullable=True)

    def get_income_sources(self):
        if not self.income_sources:
            return []
        return json.loads(self.income_sources)

    def get_deposit_sources(self):
        if not self.deposit_sources:
            return []
        return json.loads(self.deposit_sources)

    def get_income_file_ids(self):
        if not self.income_file_ids:
            return []
        return json.loads(self.income_file_ids)

    def get_deposit_file_ids(self):
        if not self.deposit_file_ids:
            return []
        return json.loads(self.deposit_file_ids)

    def get_id_with_photo_file_ids(self):
        if not self.id_with_photo_file_ids:
            return []
        return json.loads(self.id_with_photo_file_ids)

    def get_address_file_ids(self):
        if not self.address_file_ids:
            return []
        return json.loads(self.address_file_ids)

    def get_supplement_file_ids(self):
        if not self.supplement_file_ids:
            return []
        return json.loads(self.supplement_file_ids)


class Withdrawal(ModelBase):

    class Type(Enum):
        ON_CHAIN = '普通提现'
        LOCAL = '站内转账'

    class Status(Enum):
        CREATED = '待用户确认'
        AUDIT_REQUIRED = '待审核'
        AUDITED = '已审核'
        PROCESSING = '处理中'
        CONFIRMING = '确认中'
        FINISHED = '已完成'
        CANCELLED = '已取消'
        CANCELLATION_FAILED = '取消失败'
        FAILED = '失败'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    type = db.Column(db.Enum(Type), nullable=False,
                     default=Type.ON_CHAIN, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)
    chain = db.Column(db.String(32), index=True)  # `None` for local withdrawal
    address = db.Column(db.String(256), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_asset = db.Column(db.String(32), nullable=False)
    fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    memo = db.Column(db.String(1024), nullable=False, default='')
    attachment = db.Column(db.String(1024))
    recipient_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    approved_by_user_at = db.Column(db.MYSQL_DATETIME_6)
    sent_at = db.Column(db.MYSQL_DATETIME_6)
    tx_id = db.Column(db.String(128), nullable=False, default='', index=True)
    confirmations = db.Column(db.Integer, nullable=False, default=0)

    remark = db.Column(db.String(256))
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)

    user = db.relationship(
        'User',
        foreign_keys=[user_id],
        backref=db.backref('withdrawals', lazy='dynamic'))
    sender_user = db.relationship(
        'User',
        foreign_keys=[recipient_user_id],
        backref=db.backref('received_withdrawals', lazy='dynamic'))

    def __repr__(self):
        return f'{type(self).__name__}(' \
               f'{self.asset}-{self.chain}, address={self.address!r}, ' \
               f'amount={self.amount}, memo={truncate_text(self.memo, 16)!r})'


class WithdrawalApprover(ModelBase):

    MAX_APPROVER_NUM = 5

    class Status(Enum):
        CREATED = 'created' # 邀请中
        FAILED = 'failed' # 邀请失败
        VALID = 'valid' # 生效中
        DELETING = 'deleting' # 删除中
        DELETED = 'deleted' # 已删除

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    account = db.Column(db.String(64))  # 邮箱，是自己时为空，因为用户可能更换邮箱
    is_self = db.Column(db.Boolean, nullable=False, default=False)  # 是否自己
    applied_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 邀请或删除时间，用于判断24h后自动过期
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    __table_args__ = (
        db.UniqueConstraint('user_id', 'account', name='user_id_account_uniq'),
    )

    @classmethod
    def get_approvers(cls, user_id) -> List['WithdrawalApprover']:
        query = cls.query.filter(cls.user_id == user_id,
                                 cls.status.in_((cls.Status.VALID, cls.Status.DELETING)))
        return query.all()

    @classmethod
    def get_self(cls, user_id: int) -> Optional['WithdrawalApprover']:
        return cls.query.filter(cls.user_id == user_id, cls.is_self.is_(True)).first()

    @cached_property
    def email(self):
        from app.models import User
        if self.is_self:
            return User.query.get(self.user_id).email
        return self.account

    @property
    def name_displayed(self):
        return self.name or self.email or ''


class WithdrawalApprove(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        APPROVED = 'approved'
        REJECTED = 'rejected'

    withdrawal_id = db.Column(db.Integer, db.ForeignKey('withdrawal.id'), nullable=False)
    approver_id = db.Column(db.Integer, db.ForeignKey('withdrawal_approver.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class AdminDisableWithdrawalAddressRecord(ModelBase):
    """客服删除提现审核成员记录"""

    class Status(Enum):
        CREATED = 'created'
        PASSED = 'passed'
        REJECTED = 'rejected'

    user_id = db.Column(db.Integer, nullable=False)
    accounts = db.Column(db.String(512), nullable=False)  # json
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    created_by = db.Column(db.Integer, nullable=False)
    audited_by = db.Column(db.Integer)


class ApiWithdrawalAddressRequest(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        CANCELLED = 'cancelled'
        FINISHED = 'finished'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    data = db.Column(db.Text, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class ApiWithdrawalAddressApprove(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        APPROVED = 'approved'
        REJECTED = 'rejected'

    request_id = db.Column(db.Integer, db.ForeignKey('api_withdrawal_address_request.id'), nullable=False)
    approver_id = db.Column(db.Integer, db.ForeignKey('withdrawal_approver.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class WithdrawalFeeHistory(ModelBase):
    """提现手续费记录表,退回(fee_back)类型amount为负值"""
    class FeeType(Enum):
        FEE = 'fee'
        FEE_BACK = 'fee_back'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    withdrawal_id = db.Column(db.Integer, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_type = db.Column(db.StringEnum(FeeType), nullable=False)


class WithdrawalAddressBlacklist(ModelBase):
    """ 提现地址黑名单 """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    chain = db.Column(db.String(32), nullable=False, index=True)
    address = db.Column(db.String(256), nullable=False, index=True)
    memo = db.Column(db.String(1024), nullable=False, default='')
    remark = db.Column(db.String(1024), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def is_blacklisted(cls, chain: str, address: str, memo: str = '') -> bool:
        r = cls.query.filter(
            cls.chain == chain,
            cls.address == address,
            cls.memo == memo,
            cls.status == cls.Status.VALID,
        ).with_entities(cls.id).first()
        return bool(r)


class DeviceIDBlacklist(ModelBase):
    """ 设备ID黑名单 """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    device_id = db.Column(db.String(64), nullable=False, index=True)
    created_by = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(1024), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def is_blacklisted(cls, device_id: str) -> bool:
        r = cls.query.filter(
            cls.device_id == device_id,
            cls.status == cls.Status.VALID,
        ).with_entities(cls.id).first()
        return bool(r)


class IPBlacklist(ModelBase):
    """ IP地址黑名单 """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    ip = db.Column(db.String(64), nullable=False, index=True)
    location = db.Column(db.String(128), nullable=False, default='')
    created_by = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(1024), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def is_blacklisted(cls, ip: str) -> bool:
        r = cls.query.filter(
            cls.ip == ip,
            cls.status == cls.Status.VALID,
        ).with_entities(cls.id).first()
        return bool(r)


class WithdrawalAudit(ModelBase):
    """ 提现审核记录 """

    class Status(Enum):
        AUDIT_REQUIRED = '待审核'
        AUDITED = '已审核'
        AUDIT_REJECTED = '审核不通过'
        CANCELLED = '已取消'  # 取消提现了

    class Type(Enum):
        WITHDRAWAL_ADDRESS_BLACKLISTED = 'withdrawal_address_blacklisted'  # 提现地址在是黑名单地址中

    __table_args__ = (
        db.UniqueConstraint("withdrawal_id", "type", name="withdrawal_id_type_unique"),
    )

    withdrawal_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.AUDIT_REQUIRED)
    type = db.Column(db.StringEnum(Type), nullable=False)
    audited_by = db.Column(db.Integer, index=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    remark = db.Column(db.String(512))


class ColdWalletHistory(ModelBase):

    class Type(Enum):
        HOT = 'hot'
        POOL = 'pool'
        REVISION = 'revision'

    type = db.Column(db.Enum(Type), nullable=False)
    asset = db.Column(db.String(32), index=True, nullable=False)
    chain = db.Column(db.String(32), index=True, nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')

    __table_args__ = (
        db.Index('asset_chain_idx', 'asset', 'chain'),
    )

    @classmethod
    def get_balance(cls, asset: str, chain: str) -> Decimal:
        row = cls.query \
            .filter(cls.asset == asset,
                    cls.chain == chain) \
            .with_entities(cls.balance) \
            .order_by(cls.id.desc()) \
            .first()
        return row and row.balance or Decimal()

    @classmethod
    def get_balances(cls, assets: List[Tuple[str, str]] = None
                     ) -> Dict[Tuple[str, str], Decimal]:
        query = cls.query
        if assets:
            query = query \
                .filter(tuple_(cls.asset, cls.chain
                               ).in_([tuple_(a, c) for a, c in assets]))

        max_id_q = query \
            .with_entities(func.max(cls.id).label('max_id')) \
            .group_by(cls.asset, cls.chain) \
            .subquery()
        result = query \
            .join(max_id_q,
                  cls.id == max_id_q.c.max_id) \
            .with_entities(cls.asset, cls.chain, cls.balance)

        # preserving order
        mapping = {(asset, chain): balance for asset, chain, balance in result}
        if not assets:
            return mapping
        return {
            (asset, chain): mapping.get((asset, chain)) or Decimal()
            for asset, chain in assets
        }

    @classmethod
    def add_history(cls, asset: str, chain: str, amount: Decimal,
                    remark: str = '', type_: Union[Type, str] = Type.HOT,
                    session: Session = None) -> ColdWalletHistory:
        if session is None:
            with new_session() as session:
                return cls.add_history(
                    asset, chain, amount, remark, type_, session)

        if not isinstance(type_, cls.Type):
            type_ = cls.Type(type_)

        from ..business import CacheLock, LockKeys

        with CacheLock(LockKeys.cold_wallet_history(asset, chain), wait=True):
            db.session.rollback()
            return db.session_add_and_commit(cls(
                type=type_,
                asset=asset,
                chain=chain,
                amount=amount,
                remark=remark,
                balance=max(cls.get_balance(asset, chain) + amount, Decimal())
            ))

    @classmethod
    def revise(cls, asset: str, chain: str, amount: Decimal, remark: str = '',
               session: Session = None) -> ColdWalletHistory:
        return cls.add_history(
            asset, chain, amount, remark, cls.Type.REVISION, session)

    @classmethod
    def delete_history(cls, id_: int, session: Session = None):
        if session is None:
            with new_session() as session:
                return cls.delete_history(id_, session)

        row: cls
        if (row := session.query(cls).get(id_)) is None:
            raise ValueError(f'invalid history id: {id_!r}')

        from ..business import CacheLock, LockKeys

        with CacheLock(LockKeys.cold_wallet_history(
                (asset := row.asset), (chain := row.chain)), wait=True):
            session.rollback()
            session.query(cls).filter(cls.id == id_).delete()
            last: cls = session.query(cls) \
                .filter(cls.asset == asset,
                        cls.chain == chain,
                        cls.id < id_) \
                .order_by(cls.id.desc()) \
                .first()
            if last is None:
                return
            for row in session.query(cls) \
                    .filter(cls.asset == asset,
                            cls.chain == chain,
                            cls.id > id_) \
                    .order_by(cls.id):
                row.balance = last.balance + row.amount
                last = row
            session.commit()


class AssetChainConfig(ModelBase):

    class Status(Enum):
        VALID = enum_auto()
        DELETED = enum_auto()

    asset = db.Column(db.String(32), index=True, nullable=False)
    chain = db.Column(db.String(32), index=True, nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)

    __table_args__ = (
        db.UniqueConstraint('asset', 'chain', 'key',
                            name='asset_chain_key_uniq'),
        db.Index('asset_chain_idx', 'asset', 'chain')
    )


class AssetConfig(ModelBase):

    class Status(Enum):
        VALID = enum_auto()
        DELETED = enum_auto()

    asset = db.Column(db.String(64), index=True, nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)

    __table_args__ = (
        db.UniqueConstraint('asset', 'key', name='asset_key_uniq'),
    )


class AssetChainSignConfig(ModelBase):
    """该表的信息用于充值签名验证及提现多签，只做新增，不做修改"""

    asset = db.Column(db.String(64), nullable=False)
    chain = db.Column(db.String(64), index=True, nullable=False)
    identity = db.Column(db.String(512), nullable=False)
    precision = db.Column(db.Integer, nullable=False)
    group = db.Column(db.Integer, nullable=False)


class WithdrawalSignature(ModelBase):

    class Status(Enum):
        FINISHED = 'finished'
        CANCELLED = 'cancelled'

    withdrawal_id = db.Column(db.Integer, db.ForeignKey('withdrawal.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)


class OnchainSwapSignature(ModelBase):

    class Status(Enum):
        FINISHED = 'finished'
        CANCELLED = 'cancelled'

    order_id = db.Column(db.Integer, index=True, nullable=False)
    nonce = db.Column(db.String(512), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)


class WithdrawalCancel(ModelBase):

    SYSTEM_USER_ID = 0

    class CancelType(Enum):
        SYSTEM = '系统取消'
        USER = '用户取消'
        WALLET = '钱包取消'
        CUSTOMER_SERVICE = '客服取消'

    withdrawal_id = db.Column(db.Integer, nullable=False, index=True)
    cancel_type = db.Column(db.StringEnum(CancelType), nullable=False)
    cancel_user_id = db.Column(db.Integer, nullable=False, index=True)
    is_risk_control = db.Column(db.Boolean, nullable=False, default=False)


class AssetConversion(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        DEDUCTED = 'deducted'
        FAILED = 'failed'
        FINISHED = 'finished'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    available = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    trg_asset = db.Column(db.String(32), nullable=False)
    trg_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    converted = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class AssetConversionHistory(ModelBase):
    """流水表，追溯用户记录"""
    # 取消外键关联
    record_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    source_asset = db.Column(db.String(32), nullable=False)
    source_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    target_asset = db.Column(db.String(32), nullable=False)
    converted_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class LockedAssetBalance(ModelBase):
    """ 不要直接使用该模型，使用 LockAssetHelper 接口进行操作"""

    __table_args__ = (
        db.Index('ix_business_business_id', 'business', 'business_id'),
    )

    class Status(Enum):
        CREATED = '待锁定'
        LOCK_FAIL = '锁定失败'
        LOCKED = '已锁定'
        UNLOCKED = '已解锁'
        UNLOCK_FAIL = '解锁失败'

        # 以下状态废弃，待删除
        DELETED = '已删除'
        FROZEN = "冻结"  # 冻结(不解锁)

    class Business(Enum):
        GIFT = '礼物'
        ADMIN = "admin锁定"
        ONCHAIN = "链上交易"

    class OpType(Enum):
        LOCK = '锁定'
        ADD_AND_LOCK = "增加且锁定"
        UNLOCK = '解锁'
        UNLOCK_AND_SUB = "解锁且扣款"

    class RetryType(Enum):
        NO_RETRY = '不重试'
        RETRY = '重试'

    BUSINESS_MAPPING = {
        OpType.LOCK: {
            Business.ONCHAIN: BalanceBusiness.ONCHAIN,
            Business.ADMIN: BalanceBusiness.SYSTEM,
        },
        OpType.ADD_AND_LOCK: {
            Business.GIFT: BalanceBusiness.GIFT,
        },
        OpType.UNLOCK: {
            Business.ONCHAIN: BalanceBusiness.ONCHAIN,
            Business.ADMIN: BalanceBusiness.SYSTEM,
            Business.GIFT: BalanceBusiness.GIFT,
        },
        OpType.UNLOCK_AND_SUB: {
            Business.GIFT: BalanceBusiness.GIFT_REVOKE,
            Business.ONCHAIN: BalanceBusiness.ONCHAIN,
        },
    }

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    sub_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)    # 【解锁且扣款】时的扣款数量
    locked_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)       # 未锁定时，作为预期锁定时间；已解锁时，作为实际锁定时间
    unlocked_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)     # 未解锁时，作为预期解锁时间；已解锁时，作为实际解锁时间
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    remark = db.Column(db.String(128))

    retry_type = db.Column(db.StringEnum(RetryType), nullable=False, default=RetryType.RETRY, comment="重试类型")
    lock_type = db.Column(db.StringEnum(OpType), nullable=False, default=OpType.LOCK, comment="锁定类型")
    lock_business = db.Column(db.StringEnum(BalanceBusiness), nullable=False, comment="server 锁定业务")
    unlock_type = db.Column(db.StringEnum(OpType), comment="解锁类型")
    unlock_business = db.Column(db.StringEnum(BalanceBusiness), comment="server 解锁业务")

    business = db.Column(db.StringEnum(Business), nullable=False, comment="业务")
    business_id = db.Column(db.Integer, nullable=False, comment="业务ID")

    @classmethod
    def get_to_lock_rows(
            cls,
            start_time: datetime = None,
            end_time: datetime = None,
            businesses: List[Business] = None,
    ):
        now_ = now()
        query = cls.query.filter(
            cls.status == cls.Status.CREATED,
        )
        if businesses:
            query = query.filter(cls.business.in_(businesses))
        if start_time:
            if start_time > now_:
                start_time = now_
            query = query.filter(cls.locked_at >= start_time)
        if end_time:
            if end_time > now_:
                end_time = now_
            query = query.filter(cls.locked_at <= end_time)
        if start_time is None and end_time is None:
            query = query.filter(
                cls.locked_at <= now_,
            )
        return query.order_by(cls.locked_at, cls.id).all()

    @classmethod
    def get_to_unlock_rows(
            cls,
            start_time: datetime = None,
            end_time: datetime = None,
    ):
        now_ = now()
        query = cls.query.filter(
            cls.status == cls.Status.LOCKED,
        )
        if start_time:
            if start_time > now_:
                start_time = now_
            query = query.filter(cls.unlocked_at >= start_time)
        if end_time:
            if end_time > now_:
                end_time = now_
            query = query.filter(cls.unlocked_at <= end_time)
        if start_time is None and end_time is None:
            query = query.filter(
                cls.unlocked_at <= now_,
            )
        return query.order_by(cls.unlocked_at, cls.id).all()


class LockedAssetBalanceBackup(ModelBase):
    """ LockedAssetBalance Business为HISTORY历史数据 的数据备份"""

    __table_args__ = (
        db.Index('ix_business_business_id', 'business', 'business_id'),
    )

    user_id = db.Column(db.Integer, nullable=False)
    created_by = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    sub_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)    # 【解锁且扣款】时的扣款数量
    locked_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    unlocked_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    status = db.Column(db.StringEnum(LockedAssetBalance.Status), nullable=False,
                       default=LockedAssetBalance.Status.CREATED)
    remark = db.Column(db.String(128))

    retry_type = db.Column(db.StringEnum(LockedAssetBalance.RetryType), nullable=False,
                           default=LockedAssetBalance.RetryType.RETRY, comment="重试类型")
    lock_type = db.Column(db.StringEnum(LockedAssetBalance.OpType), nullable=False,
                          default=LockedAssetBalance.OpType.LOCK, comment="锁定类型")
    lock_business = db.Column(db.StringEnum(BalanceBusiness), nullable=False, comment="server 锁定业务")
    unlock_type = db.Column(db.StringEnum(LockedAssetBalance.OpType), comment="解锁类型")
    unlock_business = db.Column(db.StringEnum(BalanceBusiness), comment="server 解锁业务")

    business = db.Column(db.StringEnum(LockedAssetBalance.Business), nullable=False, comment="业务")
    business_id = db.Column(db.Integer, nullable=False, comment="业务ID")


class UpdateAssetBalance(ModelBase):

    class Status(Enum):
        CREATED = '待初审'
        AUDITED = '待复审'
        FINISHED = '已完成'
        FAILED = '失败'
        DELETED = '已删除'

    class Type(Enum):
        MARKET_EXPENSE = '市场支出'
        OPERATION_EXPENSE = '运营支出'
        ACCIDENT_EXPENSE = '事故支出'
        ASSET_RECOVER = '恢复资产'
        ASSET_UPDATE = '资产变更'
        ASSET_RETRIEVAL = '资产找回'
        ASSET_RETRIEVAL_PROFIT = '资产找回收入'
        OTHER = '其他'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    audit_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    check_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    type = db.Column(db.StringEnum(Type))
    remark = db.Column(db.String(128))
    activity = db.Column(db.String(128))


class CleanedAssetResume(ModelBase):
    """ 被清理资产恢复 """

    class Type(Enum):
        TINY_SPOT_ASSET = '极小额资产'
        INACTIVE_ACCOUNT = '不活跃账户资产'

    class Status(Enum):
        CREATED = '待审核'
        FINISHED = '已完成'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, index=True, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    created_by = db.Column(db.Integer, nullable=False)
    audit_by = db.Column(db.Integer, nullable=True)
    business_id = db.Column(db.Integer, nullable=True) # 资产变更的business_id


class SalaryPay(ModelBase):

    class Status(Enum):
        CREATED = '待初审'
        AUDITED = '待复审'
        FINISHED = '已完成'
        FAILED = '失败'
        DELETED = '已删除'

    class Type(Enum):
        AMBASSADOR = '大使发薪'
        PARTNER = '合伙人发薪'
        KOL = 'KOL发薪'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    audit_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    check_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)
    type = db.Column(db.StringEnum(Type))
    remark = db.Column(db.String(128))


class CommissionPay(ModelBase):
    """ 提成发放 """

    class Status(Enum):
        CREATED = '待初审'
        AUDITED = '待复审'
        FINISHED = '已生效'
        FAILED = '失败'
        DELETED = '已删除'

    class Type(Enum):
        BUS_USER = '商务提成'

    user_id = db.Column(db.Integer, index=True, nullable=False)
    created_by = db.Column(db.Integer, nullable=False)
    audit_id = db.Column(db.Integer, nullable=True)
    check_id = db.Column(db.Integer, nullable=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, index=True, default=Status.CREATED)
    type = db.Column(db.StringEnum(Type))
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)  # 完成时间
    remark = db.Column(db.String(128))


class AssetPrice(db.Model):

    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    avg_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)

    @classmethod
    def get_close_price_range_map(cls, start_date: date, end_date: date) -> Dict[date, Dict[str, Decimal]]:
        daily_price_map = dict()
        curr_date = start_date
        while curr_date <= end_date:
            daily_price_map[curr_date] = cls.get_close_price_map(curr_date)
            curr_date += timedelta(days=1)
        return daily_price_map

    @classmethod
    def get_close_price_map(cls, date_: datetime_date):
        """
        取得各币种当天收盘价
        """
        from app.business import PriceManager
        if isinstance(date_, datetime):
            date_ = date_.date()
        search_date = date_ + timedelta(days=1)
        price_map = PriceManager.assets_to_usd()
        if not cls.query.filter(cls.date == search_date).first():
            start = date_to_datetime(search_date) - timedelta(hours=1)
            end = date_to_datetime(search_date)
            nearest = cls.query.filter(
                cls.date >= start,
                cls.date < end
            ).order_by(cls.date.desc()).first()
            if nearest:
                search_date = nearest.date
            else:
                latest = cls.query.order_by(cls.date.desc()).first()
                search_date = latest.date
        query = cls.query.with_entities(AssetPrice.asset, AssetPrice.price)
        for item in query.filter(AssetPrice.date == search_date).all():
            price_map[item.asset] = item.price
        return price_map

    @classmethod
    def get_yesterday_close_price_map(cls):
        """快捷函数"""
        _yesterday = today() - timedelta(days=1)
        return cls.get_close_price_map(_yesterday)

    @classmethod
    def get_price_map(cls, date_: Union[datetime, datetime_date] = None,
                      use_price: bool = True, use_latest: bool = True):
        """
        不传入date_时，汇率取上一个小时的快照
        对于每日生成的日报，应取当天汇率的收盘价，使用AssetPrice.get_close_price_map, 并传入当天日期
        """
        from app.business import PriceManager
        price_map = PriceManager.assets_to_usd(PriceManager.list_asset())
        if isinstance(date_, datetime_date):
            date_ = date_to_datetime(date_)
        price_str = 'price' if use_price else 'avg_price'
        search_date = None
        if date_ and cls.query.filter(cls.date == date_).first():
            search_date = date_
        if not search_date and use_latest:
            if latest_price := cls.query.order_by(cls.date.desc()).first():
                search_date = latest_price.date
        if search_date:
            query = cls.query.with_entities(AssetPrice.asset, getattr(cls, price_str))
            for item in query.filter(AssetPrice.date == search_date).all():
                price_map[item.asset] = getattr(item, price_str)
        return price_map

    @classmethod
    def get_nearest_price_map(cls, search_time: datetime, day_range: int = 1):
        # 只用于内部钱包日报统计的内部接口，取指定时间范围内最近的汇率
        start_time = search_time - timedelta(days=day_range)
        end_time = search_time + timedelta(days=day_range)
        asset_price_list = cls.query.filter(cls.date >= start_time, cls.date <= end_time).all()
        asset_datetime_price_map = defaultdict(lambda: defaultdict(Decimal))
        for item in asset_price_list:
            d = item.date  # AssetPrice最小到microsecond，这里要格式化
            _date_time = datetime(d.year, d.month, d.day, d.hour, d.minute, d.second)
            asset_datetime_price_map[item.asset][_date_time] = item.price
        price_map = {}
        for asset, datetime_price_map in asset_datetime_price_map.items():
            datetime_list = list(datetime_price_map.keys())
            nearest_datetime = min(datetime_list, key=lambda x: abs(x - search_time))
            price_map[asset] = datetime_price_map[nearest_datetime]
        return price_map


class DepositWithdrawalPopupWindow(ModelBase):
    """ (币种)充提弹窗配置 """

    class Platform(Enum):
        ALL = "all"
        WEB = "web"
        APP = "app"

    class Frequency(Enum):
        ONCE = "once"
        EVERY_DAY = "every_day"
        EVERY_TIME = "every_time"

    class TriggerPage(Enum):
        DEPOSIT = "deposit"
        WITHDRAWAL = "withdrawal"

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    class FilterType(Enum):
        FILTERS = 'filters'
        NONE = 'none'

    class OfflineType(Enum):
        DEPOSIT = "关闭充值"
        WITHDRAWAL = "关闭提现"
        DEFLATION = "token 通缩"  # 手动配置仅针对老版本 app
        OTHER = "其他"

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    name = db.Column(db.String(128), nullable=False)
    chain = db.Column(db.String(32), nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)  # 空字符串代表链上的所有币种
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    platform = db.Column(db.Enum(Platform), nullable=False)
    trigger_page = db.Column(db.Enum(TriggerPage), nullable=False)
    frequency = db.Column(db.Enum(Frequency), nullable=False)
    sort_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    filter_type = db.Column(db.StringEnum(FilterType), nullable=False, default=FilterType.NONE)
    # 格式：[用户分群 id]
    groups = db.Column(db.TEXT, nullable=False, default='')
    # 只存目标客群用户
    users = db.Column(db.MYSQL_MEDIUM_TEXT)
    target_user_number = db.Column(db.Integer, nullable=False, default=0)
    offline_type = db.Column(db.StringEnum(OfflineType), nullable=False, default=OfflineType.OTHER)

    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    jump_type = db.Column(db.StringEnum(JumpType))
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    @classmethod
    def get_enabled_windows(cls, platform: Platform) -> list:
        _now = now()
        rows = cls.query.filter(
            cls.status == cls.Status.VALID,
            or_(cls.platform == platform, cls.platform == cls.Platform.ALL),
            cls.started_at < _now,
            cls.ended_at > _now,
        ).all()
        rows.sort(key=lambda x: x.sort_id, reverse=True)
        return rows

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class DepositWithdrawalPopupWindowContent(ModelBase):
    """ (币种)充提弹窗配置-内容 """

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.FA_IR,
        Language.KO_KP,
        Language.ID_ID,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.DE_DE,
        Language.PT_PT,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    popup_window_id = db.Column(db.Integer, db.ForeignKey("deposit_withdrawal_popup_window.id"), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    url = db.Column(db.String(512), nullable=False)
    title = db.Column(db.String(512), nullable=False)
    content = db.Column(db.Text, nullable=False)


class DexUserAssetProcessHistory(ModelBase):
    class Status(Enum):
        CREATED = "created"
        PROCESSING = "processing"
        FINISHED = "finished"

    __table_args__ = (
        db.UniqueConstraint("address", "asset", name="address_asset_uniq"),
    )

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    address = db.Column(db.String(128), nullable=False)
    asset = db.Column(db.String(64), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 发放时间
    send_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class AbnormalDepositApplication(ModelBase):
    """ 异常充值申请记录（资产自助找回） """

    """
    status 表示申请的主流程状态
    additional_info_status 表示申请的补充资料状态

    目前存在一个问题，即当发起额外资料补充流程时，会将 status 置为 CREATED。
    这样，相当于丢失了主流程当前状态。

    而目前，前端逻辑中，只有当 status == CREATED 且  
    additional_info_status 为补充资料相关状态时，用户才能提交资料。

    正确的做法应该是，additional_info_status 应该和 status 互不影响。

    现在，为了解决这个历史问题，新设计了两个状态 REQUIRE_MORE 和 REQUIRE_MORE_CR，
    用于表示需要额外补充资料(待初审)和需要额外补充资料(待复审)， 相当于通过这种方式在
    additional_info_status 中保存了主流程的状态。
    """

    # 历史记录的找回手续费
    SERVICE_FEE_AMOUNT = Decimal("50")
    SERVICE_FEE_ASSET = "USDT"

    class Type(Enum):
        UNKNOWN = "待定"
        PENDING_DEPOSIT = "正常充值"  # 正常充值或错误的充值方式
        WRONG_MEMO = "未带或带错标签"
        RECIPIENT_IS_HOT_WALLET = "充值或退回到热钱包"
        WRONG_ASSET = "充错币"
        WRONG_CHAIN_WITH_RECORDABLE = "充错链，可入账"
        WRONG_CHAIN_WITHOUT_RECORDABLE = "充错链，不可入账"
        WRONG_CHAIN_OF_ADDRESS = "充错地址"

    class AssetRateSource(Enum):
        UNKNOWN = "未知"
        COINEX = "CoinEx 现货"
        CMC = "CMC"
        MANUAL = "手动输入"
    class Status(Enum):
        CREATED = "初始状态-待定"
        AUDIT_REQUIRED = "待初审"
        CHECK_REQUIRED = "待复审"  # 已初审待复审
        DOUBLE_CHECK_REQUIRED = "待三审"
        FEE_ASSET_CHANGE_DEDUCTED = "费用资产变更-用户账户已扣减"  # 费用资产变更：从用户账户扣手续费，给财务账户加手续费
        CANCELLED = "已取消"
        REJECTED = "审核不通过"
        CHECKED = "已复审"
        #
        PROCESSING = "资金处理中"
        # 下面2个状态，只有【充错链，可入账】、【充错地址】、【充值或退回到热钱包】才有
        ASSET_CHANGE_PROCESSING = "资产变更中"  # 资产变更：扣减CoinEx财务账户，给用户账户加上资产。
        ASSET_CHANGE_DEDUCTED = "资产变更-财务账户已扣减"
        FINISHED = "找回成功"
        FAILED = "钱包处理失败"

    class AdditionalInfoStatus(Enum):
        UNKNOWN = "待定"
        NOT_REQUIRED = "不需要补充资料"
        REQUIRED = "需要补充资料"
        REQUIRE_MORE = "待额外补充资料"
        REQUIRE_MORE_CR = "待额外补充资料(CHECK_REQUIRED)"
        PROVIDED = "已提供补充资料"
        PROVIDED_CR = "已提供补充资料(CHECK_REQUIRED)"

    class WalletType(Enum):
        # 对应钱包端记录的类型
        UNKNOWN = "待定"
        ABNORMAL_DEPOSIT = "异常充值"
        DEPOSIT_RECOVERY = "充值找回"

    class RejectionReason(Enum):
        # 第三方截图相关
        NOT_UPLOAD_BLOCK_BROWSER_QUERY_SCREENSHOT = _("请勿上传区块浏览器截图")
        UPLOAD_COMPLETED_THIRD_PARTY_SNAPSHOT = _('请上传提现已完成的第三方截图，如无法上传，请通过工单/电子邮件与我们联系')
        THIRD_PARTY_TRANSFER_OUT_SCREENSHOT_MISMATCHED = _(
            '第三方平台的提现记录截图不符，截图需包含名称、金额、日期、TXID、地址、标签（如有）等信息')
        THIRD_PARTY_WITHDRAWAL_SNAPSHOT_FROM_EMAIL = _('第三方平台的历史提现记录不可截取自邮箱')
        THIRD_PARTY_WITHDRAWAL_SNAPSHOT_FROM_COINEX = _('第三方提现记录不可来自本人CoinEx账号')
        THIRD_PARTY_WITHDRAWAL_SNAPSHOT_UNRELATED = _('第三方提现记录与该笔申请不相关，请提供与CoinEx关联的提现记录')
        THIRD_PARTY_WITHDRAWAL_SNAPSHOT_BLURRY = _('第三方提现记录截图模糊')
        THIRD_PARTY_WITHDRAWAL_SNAPSHOT_INCOMPLETE = _('请提供完整的提现页面截图')
        # 充值验证相关
        DEPOSIT_TRANSFER_OUT_SCREENSHOT_MISMATCHED = _(
            '充值验证的提现记录截图不符，截图需包含名称、金额、日期、TXID、地址、标签（如有）等信息')
        NEED_PROVIDE_CANNOT_FILL_MEMO_CERTIFICATE = _('请通过工单/电子邮件提供无法填写标签Memo/Tag的凭证')
        NEED_PROVIDE_SCREENSHOT_CONTAINING_TWO_WITHDRAWALS = _(
            "请提供同时包含未到账充值和充值验证在同一页面的截图")
        NOT_COMPLETED_DEPOSIT_SPECIFIED_AMOUNT_VERIFICATION = _("未完成指定数量的充值验证")
        DEPOSIT_VERIFICATION_INVALID_BEFORE_APPLY = _('充值验证在申请之前，为无效充值验证')
        DEPOSIT_VERIFICATION_FROM_SAME_PLATFORM = _(
            '充值验证需从同一平台打出；如已完成此步骤，请额外补充未到账充值和充值验证在同一页面的录屏')
        DEPOSIT_VERIFICATION_FROM_USER_COINEX_ACCOUNT = _('充值验证截图不可来自本人CoinEx账号')
        DEPOSIT_VERIFICATION_FROM_USER_COINEX_WITHDRAWAL = _('充值验证的提现记录与该笔申请不相关，请提供与CoinEx关联的提现记录')
        # 退回地址相关
        REFUND_ADDRESS_FORMAT_MISMATCHED = _("退回地址格式不符")
        NEED_PROVIDE_NON_COINEX_RECEIVING_ADDRESS = _("请提供非CoinEx的收币地址/截图")
        REFUND_ADDRESS_SCREENSHOT_MISMATCHED = _("退回地址的验证截图不符")
        NEED_PROVIDE_SAME_CHAIN_RECEIVING_ADDRESS = _('请提供相同公链的退款地址/截图')
        NEED_PROVIDE_RECEIVING_PLATFORM_ADDRESS = _('请上传您收款平台存款页面的截图，以证明您拥有退款地址。')
        # 提现退回热钱包相关
        THIRD_PARTY_REFUND_SCREENSHOT_MISMATCHED = _(
            '第三方平台退回凭证不符，录屏需包含名称、金额、日期、充值和退回TXID、地址、标签（如有）等信息')
        THIRD_PARTY_CUSTOMER_COMMUNICATE_SCREENSHOT_MISMATCHED = _('第三方平台客服沟通凭证不符，录屏需包含“充值”和“退回”的TXID')
        SCREEN_WITHOUT_FACE_BY_ANOTHER_DEVICE = _('录屏未使用另一部设备露脸拍摄')
        SCREEN_BLURRY = _('您提交的录屏画质模糊，无法核验交易信息，请重新提供清晰完整的操作录屏')
        SCREEN_MISSING_TIME = _('录屏未展现设备时间或浏览器显示的时间')
        SCREEN_MISSING_TX_ON_CHAIN_INFO = _('录屏未刷新页面或未点击TXID展示链上信息')
        SCREEN_MISSING_REFUND_PROOF = _('录屏未展示进入充值地址 & memo 页面，并查看退回凭证')
        SCREEN_MISSING_CUSTOMER_COMMUNICATE = _('录屏未完整展示与对方客服的聊天记录；录屏需展示“充值”和“退回”的TXID')
        # 其他充值相关
        ALREADY_RECORDED = _("该笔充值已成功入账")
        NEED_WAIT = _("该笔充值将在钱包维护完成后自动入账，请耐心等待")
        DEPOSIT_MADE_BEFORE_ADDRESS = _('未到账充值在充值地址生成之前；请用正确账号提交申请')
        RETRIEVE_ADDRESS_NOT_GENERATE = _('未生成需找回充值的地址，请用正确账号提交申请')
        TOTAL_USD_UNKNOWN = _('您的充值价值未知，我们不建议您进行资产找回。如仍需找回请通过工单/邮件联系我们')
        TOTAL_USD_TOO_SMALL = _("您的未到账充值价值少于找回手续费50U，我们不建议您进行资产找回")
        DEPOSIT_TO_INCORRECT_ADDRESS = _('该笔充值由于充值至错误的地址，无法找回')
        # 其他
        IMAGE_HAS_BEEN_EDITED = _('图片或录屏信息被篡改或处理过')
        ADDITIONAL_INFO_NOT_PROVIDED_FOR_A_LONG_TIME = _("长时间未提交验证资料")
        ADDITIONAL_INFO_UNQUALIFIED = _('多次额外补充资料不合格；请确认资料合格后重新提交申请')
        NEED_PROVIDE_PROOF = _('我们已通过工单联系您，请查看工单并提供所需资料')

        # 已废弃，兼容保留
        SERVICE_FEE_NOT_ENOUGH = _("手续费不足，请保证你的现货账户有%(amount)s %(asset)s的资产")
        TOKEN_THIRD_PARTY_TRANSFER_OUT_SCREENSHOT_MISMATCHED = _(
            "第三方平台的提现记录截图不符，截图需包含名称、金额、日期、TXID、地址、标签（如有）等信息")
        REFUND_ADDRESS_IS_HOT_WALLET_ADDRESS_OF_A_PLATFORM = _("退回地址为某平台的热钱包地址，请重新提交")

        @classmethod
        def reason_to_str(cls, reason: Enum) -> str:
            if reason == cls.SERVICE_FEE_NOT_ENOUGH:
                fee_reason = _(
                    cls.SERVICE_FEE_NOT_ENOUGH.value,
                    amount=AbnormalDepositApplication.SERVICE_FEE_AMOUNT,
                    asset=AbnormalDepositApplication.SERVICE_FEE_ASSET,
                )
                return fee_reason
            else:
                # caller with force_locale
                return _(reason.value)

    # 对应于wallet异常充值记录的找回类型
    WALLET_ABNORMAL_DEPOSIT_TYPES = [
        Type.PENDING_DEPOSIT,
        Type.WRONG_MEMO,
        Type.RECIPIENT_IS_HOT_WALLET,
        Type.WRONG_CHAIN_OF_ADDRESS,
    ]
    # 对应于wallet充值找回记录的找回类型
    WALLET_DEPOSIT_RECOVERY_TYPES = [
        Type.WRONG_ASSET,
        Type.WRONG_CHAIN_WITH_RECORDABLE,
        Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
    ]

    # 需要补充资料的找回类型
    NEED_ADDITIONAL_INFO_TYPES = [
        Type.WRONG_MEMO,
        Type.RECIPIENT_IS_HOT_WALLET,
        Type.WRONG_ASSET,
        Type.WRONG_CHAIN_WITH_RECORDABLE,
        Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
        Type.WRONG_CHAIN_OF_ADDRESS,
    ]
    # 需要手续费的找回类型
    NEED_SERVICE_FEE_TYPES = [
        Type.WRONG_ASSET,
        Type.WRONG_CHAIN_WITH_RECORDABLE,
        Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
    ]
    NEW_NEED_SERVICE_FEE_TYPES = [
        Type.RECIPIENT_IS_HOT_WALLET,
        Type.WRONG_ASSET,
        Type.WRONG_CHAIN_WITH_RECORDABLE,
        Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
        Type.WRONG_CHAIN_OF_ADDRESS,
    ]
    # 打回到退款地址的找回类型
    REFUND_TYPES = [
        Type.WRONG_ASSET,
        Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
    ]

    __table_args__ = (db.UniqueConstraint("wallet_type", "wallet_id", name="wallet_type_id_unique"),)

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))

    # 初审、复审、三审
    auditor_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    checker_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    checked_at = db.Column(db.MYSQL_DATETIME_6)
    double_checker_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    double_checked_at = db.Column(db.MYSQL_DATETIME_6)

    # 钱包记录类型 和 钱包记录id
    wallet_type = db.Column(db.StringEnum(WalletType), nullable=False, default=WalletType.UNKNOWN)
    wallet_id = db.Column(db.Integer)

    # 基本信息
    asset = db.Column(db.String(32), nullable=False, index=True)
    chain = db.Column(db.String(32), nullable=False, index=True)
    address = db.Column(db.String(512), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 用户填写的amount
    tx_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 链上tx的amount
    memo = db.Column(db.String(1024), nullable=False, default='')
    tx_id = db.Column(db.String(512), nullable=False, index=True)

    additional_info_status = db.Column(db.StringEnum(AdditionalInfoStatus), nullable=False, default=AdditionalInfoStatus.UNKNOWN)
    # 补充信息, 存json（图片s3的key）。 不需要时为null
    additional_info = db.Column(db.String(4096))
    additional_info_at = db.Column(db.MYSQL_DATETIME_6)
    # timestamp list: []
    additional_info_at_info = db.Column(db.String(1024))

    # 退回信息
    refund_address = db.Column(db.String(512), nullable=False, default='')
    refund_memo = db.Column(db.String(1024), nullable=False, default='')
    refund_tx_id = db.Column(db.String(512), nullable=False, default='')

    # 有效状态更新的时间
    processed_at = db.Column(db.MYSQL_DATETIME_6)

    # 实际手续费
    fee_asset = db.Column(db.String(32), default='')
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.UNKNOWN)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    remark = db.Column(db.String(512), nullable=False, default='')
    jump_url = db.Column(db.String(512), nullable=False, default='')
    rejection_reason = db.Column(db.String(4096), nullable=False, default='')
    # 可能是自定义的原因 + 枚举原因（枚举则存 .value），admin 展示用。 ['枚举原因的值 1;枚举原因的值 2', '自定义原因 1']
    history_rejection_reason = db.Column(db.Text, nullable=False, default='')
    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)
    total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 找回币种的usd价值
    is_new = db.Column(db.Boolean, default=True)  # 是否是新记录；旧数据都是False；上线后新增的记录，都是True
    asset_rate = db.Column(db.MYSQL_DECIMAL_PRICE, default=0)  # 找回币种汇率
    asset_rate_source = db.Column(db.StringEnum(AssetRateSource), nullable=False, default=AssetRateSource.UNKNOWN)
    expect_fee_is_manual = db.Column(db.Boolean)  # 是否手动设置手续费
    expect_fee_asset = db.Column(db.String(32))  # 预计手续费币种
    expect_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8)  # 预计手续费数目，提前算好
    # 部分钱包的充值记录（type=ABNORMAL, reason=RECIPIENT_IS_HOT_WALLET），同步到web时 是给财务帐号生成充值记录
    # 后续再通过资产变更，扣除财务账号的资产（扣除理应给用户入账的部分），给用户账号增加资产。
    # 关联下充值记录ID，避免重复
    deposit_id = db.Column(db.Integer, unique=True)  # 充值记录ID

    @property
    def is_need_additional_info(self) -> bool:
        return self.type in self.NEED_ADDITIONAL_INFO_TYPES

    @property
    def is_need_fee(self) -> bool:
        if self.is_new:
            return self.type in self.NEW_NEED_SERVICE_FEE_TYPES
        return self.type in self.NEED_SERVICE_FEE_TYPES
    
    @property
    def retrieve_amount(self):
        # 找回数量：如果需要手续费，且手续费币种和找回币种相同，则原始数量 - 手续费；否则等于原始数量
        if self.is_need_fee:
            if self.fee_asset and self.fee_asset == self.asset:
                return self.tx_amount - (self.fee_amount or 0)

            if self.expect_fee_asset and self.expect_fee_asset == self.asset:
                return self.tx_amount - (self.expect_fee_amount or 0)
        return self.tx_amount

    @property
    def rejection_reason_str(self) -> str:
        if self.is_custom_reason:
            return self.rejection_reason
        else:
            if self.rejection_reason:
                li = self.rejection_reason.split(',')
                enum_li = [self.RejectionReason[i] for i in li]
                return ';'.join([self.RejectionReason.reason_to_str(i) for i in enum_li])
            else:
                return ''

    @property
    def rejection_reason_list(self) -> list:
        if self.is_custom_reason:
            return [self.rejection_reason] if self.rejection_reason else []
        else:
            return self.rejection_reason.split(',') if self.rejection_reason else []

    def get_history_rejection_reason(self) -> list[str]:
        if self.history_rejection_reason:
            return json.loads(self.history_rejection_reason)
        return []

    def get_additional_info_at_info(self) -> list[int]:
        if self.additional_info_at_info:
            return json.loads(self.additional_info_at_info)
        return []


class AbnormalDepositApplicationChangelog(ModelBase):
    """ 异常充值申请-变更记录 """

    class ChangeType(Enum):
        STATUS = "状态"
        TYPE = "类型"
        DETAIL = "内容"

    application_id = db.Column(db.Integer, db.ForeignKey("abnormal_deposit_application.id"))
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    admin_user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    change_type = db.Column(db.String(32), nullable=False)
    detail = db.Column(db.String(1024), nullable=False)

    @classmethod
    def add(
        cls,
        application_id: int,
        user_id: int,
        admin_user_id: Optional[int],
        change_type: str,
        old_value: str,
        new_value: str,
        is_commit: bool = False,
    ) -> AbnormalDepositApplicationChangelog:
        # change_type, old_value, new_value: 枚举.name
        detail = {
            "old": old_value,
            "new": new_value,
        }
        log = cls(
            application_id=application_id,
            user_id=user_id,
            admin_user_id=admin_user_id,
            change_type=change_type,
            detail=json.dumps(detail),
        )
        db.session.add(log)
        if is_commit:
            db.session.commit()
        return log


class AbnormalDepositApplicationTransferHistory(ModelBase):
    """ 异常充值申请记录-资产变更记录 """

    __table_args__ = (db.UniqueConstraint("application_id", "type", name="application_id_type_unique"),)

    class Status(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FINISHED = "finished"

    class Type(Enum):
        FEE = "手续费"
        WRONG_CHAIN_WITH_RECORDABLE_CHANGE = "充错链可入账-变更"
        RECIPIENT_IS_HOT_WALLET_CHANGE = "充到热钱包-变更"
        WRONG_CHAIN_OF_ADDRESS_CHANGE = "充错地址-变更"

    application_id = db.Column(db.Integer, db.ForeignKey("abnormal_deposit_application.id"))
    type = db.Column(db.StringEnum(Type), nullable=False)
    from_user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    to_user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    asset = db.Column(db.String(32), index=True, nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deducted_at = db.Column(db.MYSQL_DATETIME_6)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    def get_business_id(self):
        # +10000：兼容之前的老数据
        return self.id + 10000


class KYTDepositAudit(ModelBase):
    # TODO: 上线后仅展示保留

    class Status(Enum):
        CREATED = '已创建'
        INFO_REQUIRED = '待提供资料'
        EXTRA_INFO_REQUIRED = '待提供额外资料'
        AUDIT_REQUIRED = '待审核'
        FREEZING = '持续冻结'
        # 以下为终止态
        AUDITED = '已入账'
        WITHDRAWAL_ONLY = '仅提现'

    class RiskLevel(Enum):
        HIGH = 'High'
        SEVERE = 'Severe'

    deposit_id = db.Column(db.Integer, nullable=False, unique=True)
    sender_risk_id = db.Column(db.Integer, nullable=False, comment='充值风险评估ID')
    wallet_deposit_id = db.Column(db.Integer, nullable=False, unique=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    address_from = db.Column(db.String(256), nullable=True, index=True)
    exchange_from = db.Column(db.String(256), default='', index=True)
    assess_object = db.Column(db.String(512), nullable=False, default='', index=True, comment='评估对象')
    risk_score = db.Column(db.Integer, nullable=False, default=0)
    risk_level = db.Column(db.StringEnum(RiskLevel), nullable=False, default=RiskLevel.HIGH)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    operator = db.Column(db.Integer, nullable=True)
    operated_at = db.Column(db.DateTime)
    remark = db.Column(db.String(256), nullable=False, default='')
    info_updated_at = db.Column(db.DateTime, index=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)

    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)
    rejection_reason = db.Column(db.String(256), nullable=True)  # KycVerification.RejectionReason.name
    custom_rejection_reason = db.Column(db.String(512), nullable=True)

    @property
    def address_result(self):
        if not self.exchange_from:
            return self.address_from
        return f'{self.address_from}({self.exchange_from})'

    @property
    def risk_result(self):
        return f'risk_score={self.risk_score}, risk_level={self.risk_level.name}'

    def get_reject_reason(self, translate=True):
        from app.models import KycVerification

        if self.is_custom_reason:
            return self.custom_rejection_reason
        else:
            rejection_reason = None
            if self.rejection_reason:
                rejection_reason = KycVerification.RejectionReason[self.rejection_reason]
            value = rejection_reason.value if rejection_reason else None
            if translate:
                return _(value) if value else None
            else:
                return value if value else None


class KYTDepositAuditDetail(ModelBase):
    kyt_id = db.Column(db.Integer, nullable=False, index=True)
    source_proof_file_ids = db.Column(db.String(512), nullable=False)
    address_file_ids = db.Column(db.String(512), nullable=False)
    edd_file_ids = db.Column(db.String(512), nullable=False)

    supplement_file_ids = db.Column(db.String(512), nullable=True)
    supplement_desc = db.Column(db.String(2048), nullable=True)

    def get_source_proof_file_ids(self):
        if not self.source_proof_file_ids:
            return []
        return json.loads(self.source_proof_file_ids)

    def get_address_file_ids(self):
        if not self.address_file_ids:
            return []
        return json.loads(self.address_file_ids)

    def get_edd_file_ids(self):
        if not self.edd_file_ids:
            return []
        return json.loads(self.edd_file_ids)

    def get_supplement_file_ids(self):
        if not self.supplement_file_ids:
            return []
        return json.loads(self.supplement_file_ids)


class WithdrawalRestrictedFund(ModelBase):
    """提现受限资金"""

    class BizType(Enum):
        P2P = 'p2p'  # biz_id:  p2p_order.id

    class StatusType(Enum):
        NORMAL = "normal"
        MANUAL_RELEASE = "manual_release"

    biz_type = db.Column(db.StringEnum(BizType), nullable=False)  # 业务类型
    biz_id = db.Column(db.Integer, nullable=False)  # 业务 ID
    user_id = db.Column(db.Integer, nullable=False, index=True)  # 用户ID
    asset = db.Column(db.String(32), nullable=False)  # 币种
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 限制数量
    amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)   # 限制金额
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.NORMAL)
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 被限制截止时间

    releaser_id = db.Column(db.Integer, nullable=True)  # 解除限制用户id
    release_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 解除限制时间


class WithdrawalRestrictedFundChangeLog(ModelBase):
    """被限制资金修改记录"""
    SYSTEM_USER_ID = 0

    record_id = db.Column(db.Integer, nullable=False, index=True, comment="WithdrawalRestrictedFund.id")
    old_data = db.Column(db.JSON, nullable=False)
    new_data = db.Column(db.JSON, nullable=False)
    change_user_id = db.Column(db.Integer)

