from decimal import Decimal
from enum import Enum
from uuid import uuid4

from flask_babel import gettext as _

from .base import db, ModelBase
from ..common.fiat import DirectFiatPaymentMethod
from ..utils import AmountType, now, today


class DirectFiatOrder(ModelBase):
    class Status(Enum):
        CREATED = 'created'
        PENDING = 'pending'     # 处理中
        BUY_SENT = 'buy_sent'   # 已打币 - 服务商已完成
        SELL_SENT = 'sell_sent' # 已打币 - 进行中
        FINISHED = 'finished'   # 已完成
        CANCELED = 'canceled'   # 已取消

    class ThirdStatus(Enum):
        CREATED = 'created'
        PENDING = 'pending'     # 处理中
        FINISHED = 'finished'   # 已完成
        FAILED = 'failed'       # 已失败

    class Reason(Enum):
        MANUAL_CANCELED = _('用户手动取消')
        MANUAL_WITHDRAWAL_CANCELED = _('用户手动取消提现')
        THIRD_PARTY_CANCELED = _('服务商取消')
        WITHDRAWAL_SYSTEM_TIMEOUT = _('提现超时取消')
        SYSTEM_TIMEOUT = _('超时取消')

    class OrderType(Enum):
        BUY = 'buy'
        SELL = 'sell'

    DISPLAY_STATUSES = {
        Status.CREATED: Status.PENDING,
        Status.PENDING: Status.PENDING,
        Status.SELL_SENT: Status.PENDING,
        Status.BUY_SENT: Status.PENDING,
        Status.FINISHED: Status.FINISHED,
        Status.CANCELED: Status.CANCELED,
    }
    PENDING_STATUS_TUPLE = (Status.CREATED, Status.PENDING, Status.SELL_SENT, Status.BUY_SENT)

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)

    asset = db.Column(db.String(32), nullable=False, index=True)
    chain = db.Column(db.String(32), nullable=False, index=True)
    coin_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    fiat_currency = db.Column(db.String(32), nullable=False, index=True)
    fiat_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    payment_id = db.Column(db.String(64), nullable=False, unique=True)
    payment_account = db.Column(db.String(64), nullable=False, default='')
    payment_method = db.Column(db.String(32), nullable=False, default='')
    payment_url = db.Column(db.Text, default='', nullable=False)
    payment_expired_at = db.Column(db.MYSQL_DATETIME_6)
    order_id = db.Column(db.String(64), nullable=False)
    third_party = db.Column(db.String(64), nullable=False, index=True)
    event = db.Column(db.Text, default='', nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    third_status = db.Column(db.StringEnum(ThirdStatus), nullable=False, default=ThirdStatus.CREATED)
    order_type = db.Column(db.StringEnum(OrderType), nullable=False, index=True)
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)
    canceled_at = db.Column(db.MYSQL_DATETIME_6, index=True)
    sent_at = db.Column(db.MYSQL_DATETIME_6)  # 打币时间
    # deposit address if order_type is buy else withdrawal address
    address = db.Column(db.String(256))
    tx_id = db.Column(db.String(128), nullable=True)
    # deposit id if order_type is buy else withdrawal id
    biz_id = db.Column(db.Integer, nullable=True, index=True)
    reason = db.Column(db.String(1024))

    def get_reason(self, translate=True):
        if not self.reason:
            return
        reason = self.Reason[self.reason]
        if translate:
            return _(reason.value)
        return reason.value

    @classmethod
    def new_record(cls,
                   user_id: int,
                   asset: str,
                   chain: str,
                   coin_amount: AmountType,
                   fiat_currency: str,
                   fiat_total_amount: AmountType,
                   order_id: str,
                   address: str,
                   third_party: str,
                   order_type: OrderType,
                   payment_method: DirectFiatPaymentMethod,
                   status=Status.CREATED,
                   payment_url: str = '',
                   payment_account: str = '',
                   payment_expired_at=None,
                   auto_commit=True,
                   ):
        record = cls(user_id=user_id,
                     asset=asset,
                     chain=chain,
                     coin_amount=Decimal(coin_amount),
                     fiat_currency=fiat_currency,
                     fiat_total_amount=Decimal(fiat_total_amount),
                     payment_id=cls.gen_payment_id(),
                     order_id=order_id,
                     address=address,
                     third_party=third_party,
                     status=status,
                     third_status=cls.ThirdStatus.CREATED,
                     order_type=order_type,
                     payment_url=payment_url,
                     payment_account=payment_account,
                     payment_method=payment_method.name,
                     payment_expired_at=payment_expired_at,
                     )
        db.session.add(record)
        if auto_commit:
            db.session.commit()
        return record

    @classmethod
    def gen_payment_id(cls):
        while True:
            new_id = today().strftime('%Y%m%d') + str(uuid4().int)[-8:]
            if not cls.query.filter(cls.payment_id == new_id).first():
                return new_id
            cls.gen_payment_id()
