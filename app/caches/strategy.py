# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from datetime import date
from typing import List, Tuple, Dict

from app.models.strategy import StrategyTriggerPrice
from app.caches.base import StringCache, HashCache, SetCache, ListCache
from app.utils import quantize_amount, amount_to_str, today_timestamp_utc, current_timestamp
from app.utils.parser import JsonEncoder


class MarketMinuteClosePriceCache(HashCache):
    """ 存市场最近60分钟的k线，用于计算网格的 最低价区间、最高价区间 """
    MINUTES = 60

    def __init__(self, market: str):
        super().__init__(market)

    @classmethod
    def reload(cls, markets: List[str]):
        for m in markets:
            cls.reload_by_market(m)

    @classmethod
    def reload_by_market(cls, market: str):
        from app.business import ServerClient

        interval = 60
        cur_ts = current_timestamp(to_int=True)
        end_ts = cur_ts - cur_ts % interval

        cache = cls(market)
        old_close_price_map = cache.read()
        if old_close_price_map:
            old_max_ts = max([int(i) for i in old_close_price_map.keys()])
            start_ts = old_max_ts + interval
        else:
            start_ts = end_ts - cls.MINUTES * interval

        if start_ts > end_ts:
            return
        kls = ServerClient().market_kline(market=market, start_time=start_ts, end_time=end_ts, interval=interval)
        new_data = dict(old_close_price_map)
        new_data.update({str(kl[0]): amount_to_str(kl[2]) for kl in kls})  # time: close_price

        # 取时间最新的前N条
        res = dict(sorted(new_data.items(), key=lambda x: int(x[0]), reverse=True)[:cls.MINUTES])
        cache.save(res)

    def get_close_price_map(self) -> Dict[int, Decimal]:
        raw_data = self.read()
        close_price_map = {int(k): Decimal(v) for k, v in raw_data.items()}
        return close_price_map

    def get_avg_price(self) -> Decimal:
        close_price_map = self.get_close_price_map()
        if not close_price_map:
            return Decimal()

        # 近1小时收盘均价，精度由调用方处理
        return sum(close_price_map.values()) / len(close_price_map)


class MarketDayKlineCache(StringCache):
    """ 存市场最近90天的日k线，用于计算网格的 推荐的最低价、最高价 """
    DAYS = 90

    def __init__(self, market: str):
        super().__init__(market)

    @classmethod
    def reload(cls, markets: List[str]):
        for m in markets:
            cls.reload_by_market(m)

    @classmethod
    def reload_by_market(cls, market: str):
        from app.business import ServerClient

        interval = 86400
        end_ts = today_timestamp_utc()

        cache = cls(market)
        old_kls = cache.get_klines()
        if old_kls:
            old_max_ts = max([i[0] for i in old_kls])
            start_ts = old_max_ts + interval
        else:
            start_ts = end_ts - cls.DAYS * interval

        if start_ts > end_ts:
            return
        kls = ServerClient().market_kline(market=market, start_time=start_ts, end_time=end_ts, interval=interval)

        res = list(old_kls)
        for kl in kls:
            # time, open, close, highest, lowest
            res.append([kl[0], kl[1], kl[2], kl[3], kl[4]])
        res.sort(key=lambda x: x[0])
        res = res[-cls.DAYS:]  # 取时间最新的前N条

        cache.set(json.dumps(res, cls=JsonEncoder))

    def get_klines(self) -> List:
        raw_data = self.read()
        raw_data = json.loads(raw_data) if raw_data else []
        res = [[int(i[0]), Decimal(i[1]), Decimal(i[2]), Decimal(i[3]), Decimal(i[4])] for i in raw_data]
        res.sort(key=lambda x: x[0])
        return res

    @classmethod
    def cal_volatility(cls, open_price: Decimal, highest_price: Decimal, lowest_price: Decimal) -> Decimal:
        """ 波动率: (日K线的最高点-最低点)/开盘价 """
        if open_price == Decimal():
            return Decimal()
        v = (highest_price - lowest_price) / open_price
        v = quantize_amount(v, 8)
        return v

    def calc_price_range(self, data: List) -> Tuple[Decimal, Decimal]:
        """ 计算最低价、最高价 """
        if not data:
            return Decimal(), Decimal()

        total_vol = Decimal()
        for i in data:
            total_vol += self.cal_volatility(i[1], i[3], i[4])
        avg_vol = quantize_amount(total_vol / len(data), 8)  # 近N天-波动率均值
        lowest_price = min(Decimal(i[4]) for i in data)  # 近N天-最低价
        highest_price = max(Decimal(i[3]) for i in data)
        lowest_dot = lowest_price * (Decimal("1") - 2 * avg_vol)  # 低点
        highest_dot = highest_price * (Decimal("1") + 2 * avg_vol)  # 高点
        return lowest_dot, highest_dot

    def get_days_price_range(self, days_list: List[int]) -> List:
        price_ranges = []
        klines = self.get_klines()
        for days in days_list:
            data = klines[-days:]  # 取最近N天数据来计算
            lowest, highest = self.calc_price_range(data)
            price_ranges.append([lowest, highest])
        return price_ranges


class SpotGridStageConfigCache(StringCache):
    """ 现货网格档位配置 """

    DEFAULT_CONFIGS = [
        # 最小深度市值USD, 最大挂单USD
        (Decimal("500000"), Decimal("500000")),
        (Decimal("200000"), Decimal("200000")),
        (Decimal("100000"), Decimal("100000")),
        (Decimal("50000"), Decimal("50000")),
        (Decimal("20000"), Decimal("20000")),
        (Decimal("10000"), Decimal("10000")),
        (Decimal("5000"), Decimal("5000")),
        (Decimal("2000"), Decimal("2000")),
        (Decimal("1000"), Decimal("1000")),
        (Decimal("500"), Decimal("500")),
        (Decimal("200"), Decimal("200")),
    ]

    def __init__(self):
        super().__init__(None)

    def get_sorted_configs(self) -> List[Tuple[Decimal, Decimal]]:
        data = self.read()
        if not data:
            return self.DEFAULT_CONFIGS

        data = json.loads(data)
        results = []
        for d in data:
            results.append((Decimal(d["min_depth_usd"]), Decimal(d["max_usd"])))

        results.sort(key=lambda item: item[0], reverse=True)  # 按照最小深度市值USD降序排列
        return results


class SpotGridMarketDepthCache(HashCache):
    """ 现货网格市场深度缓存（admin展示） """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        cache = cls()
        cache_data = {str(k): amount_to_str(v) for k, v in market_depth_usd_map.items()}
        cache.save(cache_data)


class SpotGridMarketCache(HashCache):
    """ 现货网格-市场深度信息 """
    def __init__(self):
        super().__init__(None)

    @classmethod
    def calc_max_usd(cls, configs: List[Tuple[Decimal, Decimal]], depth_usd: Decimal) -> Decimal:
        # 计算 最大价值范围
        for _depth_usd, _max_usd in configs:
            if depth_usd >= _depth_usd:
                return _max_usd
        return Decimal()

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        configs = SpotGridStageConfigCache().get_sorted_configs()
        min_depth_usd = configs[-1][0]

        market_max_usd_map = {}
        valid_market_depth_usd_map = {m: u for m, u in market_depth_usd_map.items() if u >= min_depth_usd}
        for market, depth_usd in valid_market_depth_usd_map.items():
            max_usd = cls.calc_max_usd(configs, depth_usd)
            if not max_usd:
                continue
            if not MarketMinuteClosePriceCache(market).exists():
                # 无网格价格区间数据
                continue
            market_max_usd_map[market] = max_usd

        cache = cls()
        cache_data = {str(k): amount_to_str(v) for k, v in market_max_usd_map.items()}
        cache.save(cache_data)

        SpotGridMarketDepthCache().save_to_cache(market_depth_usd_map)
        SpotGridMarketsViewCache().save_to_cache(list(market_max_usd_map))

    @classmethod
    def get_market_max_usd(cls, market: str) -> Decimal:
        val = cls().hget(market)
        return Decimal(val) if val else Decimal()


class SpotGridMarketsViewCache(ListCache):
    """支持的网格市场列表(排序)"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def save_to_cache(cls, markets: list[str]) -> list[str]:
        from app.caches import MarketCache

        market_items = []
        market_info_map = MarketCache.online_markets_detail()
        for m in markets:
            m_info = market_info_map.get(m)
            if m_info:
                market_items.append([m, m_info['base_asset'], m_info['quote_asset']])
            else:
                market_items.append([m, '', ''])

        market_items.sort(key=lambda x: MarketCache.market_sort_func(x[1], x[2]))
        sorted_market_names = [v[0] for v in market_items]
        cls().save(sorted_market_names)
        return sorted_market_names


class StrategyTriggerMarketsCaChe(SetCache):
    """ 网格-触发价的 市场列表 """
    def __init__(self):
        super().__init__(None)


class StrategyPriceTriggerCache(HashCache):
    """ 策略的触发价 """
    # data: {strategy_id: rise_or_fall:price}

    def __init__(self, market: str, price_type: StrategyTriggerPrice.Type):
        key_ = f"{market}:{price_type.name}"
        super().__init__(key_)

    def set_trigger_price(self, strategy_id: int, price: Decimal, direct: StrategyTriggerPrice.Direction):
        self.hset(str(strategy_id), f"{direct.name}:{amount_to_str(price)}")

    def del_trigger_price(self, strategy_id: int):
        self.hdel(str(strategy_id))

    def get_all_trigger_prices(self) -> Dict[int, Tuple[str, Decimal]]:
        data = self.hgetall()
        result = {}
        for k, v in data.items():
            direct, price = v.split(":")
            result[int(k)] = (direct, Decimal(price))
        return result


class StrategyRecommendDaysNoticeCache(SetCache):
    """ 策略推荐运行天数的通知 """
    def __init__(self, strategy_type: str, date_: date):
        key_ = f'{strategy_type}:{date_.strftime("%Y%m%d")}'
        super().__init__(key_)


class StrategyExceedPriceNoticeCountCache(HashCache):
    """ 策略 超出网格价格区间 的通知次数 """

    MAX_COUNT = 2
    TTL = 86400 * 3

    def __init__(self, market: str):
        super().__init__(market)

    def hincrby_if_fewer_than(self, sty_id: int) -> bool:
        key_ = str(sty_id)
        cur_count = int(self.hget(key_) or 0)
        if cur_count >= self.MAX_COUNT:
            return False

        self.hincrby(key_, 1)
        self.expire(self.TTL)
        return True

    def remove_un_exceed(self, new_exceed_sty_ids: List[int]):
        """ 删除市场里本次未超出价格区间的策略id """
        if not new_exceed_sty_ids:
            self.delete()
        old_sty_keys = set(self.hkeys())
        new_sty_keys = {str(i) for i in new_exceed_sty_ids}
        if del_keys := old_sty_keys - new_sty_keys:
            self.hdel(*del_keys)


class StrategySubBalanceCache(StringCache):
    """ 策略子账号余额 """

    TTL = 300

    def __init__(self, main_user_id: int):
        super().__init__(str(main_user_id))

    def get_sub_balances(self) -> dict:
        if not (value := self.read()):
            return {}
        return {int(k): v for k, v in json.loads(value).items()}

    def save_sub_balances(self, sub_balances: dict):
        value = json.dumps(sub_balances, cls=JsonEncoder)
        self.set(value, ex=self.TTL)
