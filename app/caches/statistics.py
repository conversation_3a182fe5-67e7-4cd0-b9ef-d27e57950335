# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
import json
from enum import Enum
from sqlalchemy import func
from app.models import User, UserTradeSummary, Vip<PERSON>ser, MarketMaker

from app.utils.amount import amount_to_str
from app.utils.date_ import current_timestamp, today
from app.utils.format import safe_div

from .base import Hash<PERSON>ache, StringCache
from ..utils import timestamp_to_date


class DepositWithdrawRetentionRateCache(HashCache):
    expired_seconds = 3600 * 24 * 2

    def __init__(self):
        super().__init__(None)


class WithdrawalFuseStatisticsCache(HashCache):

    def __init__(self):
        super().__init__(None)


class DepositFuseStatisticsCache(StringCache):

    def __init__(self):
        super().__init__(None)


class NewDepositFuseStatisticsCache(HashCache):

    def __init__(self):
        super().__init__(None)


class RiskControlHappendCache(StringCache):
    
    def __init__(self, key: str):
        super().__init__(key)


class BigUserCustomerStatisticCache(HashCache):

    def __init__(self):
        super().__init__(None)


class AlertUserTimeCache(StringCache):

    def __init__(self, msg_id: str):
        """
        use string to format kwargs, mix different type may has same keys.
        """
        super().__init__(msg_id)

    def set_cache(self, ex: int):
        self.set('1', ex=ex)


class AssetUsersStatisticCache(HashCache):

    def __init__(self, account: str):
        super().__init__(account)


class AssetUsersStatisticForLanguageCache(HashCache):

    def __init__(self, account: str):
        super().__init__(account)


class AntiFraudDataCursorCache(HashCache):
    
    def __init__(self,):
        super().__init__(None)


class AntiFraudReportCursorCache(HashCache):

    def __init__(self, ):
        super().__init__(None)


class NationUserStatisticsCache(StringCache):
    
    """
    国籍分布统计
    """
    class Type:
        REGISTER = '注册用户'
        ACTIVE = '活跃用户'

    class DataType(Enum):
        COUNTRY = 'country'
        AREA = 'area'
    
    def __init__(self, type_: str, data_type: DataType = DataType.COUNTRY):
        super().__init__(f"{type_}:{data_type}")


class LanguageUserStatisticsCache(StringCache):
    """
    语言分布统计
    """
    class Type:
        REGISTER = '注册用户'
        ACTIVE = '活跃用户'

    def __init__(self, type_: str):
        super().__init__(type_)


class AverageAssetDepositStatisticsCache(HashCache):

    def __init__(self):
        super().__init__(None)


class AverageAssetWithdrawalStatisticsCache(HashCache):

    def __init__(self):
        super().__init__(None)


class LanguageTradeFeeStatisticsCache(StringCache):
    """
    语言分布-收入统计
    """
    def __init__(self):
        super().__init__(None)


class NationTradeFeeStatisticsCache(StringCache):
    """
    国籍分布-收入统计
    """

    def __init__(self):
        super().__init__(None)


class PerpetualProfitLossStatisticsCache(StringCache):
    """
    合约用户盈亏统计
    """

    class Range(Enum):
        LAST_7D = '最近 7 天'
        LAST_30D = '最近 30 天'
        LAST_90D = '最近 90 天'
        LAST_180D = '最近半年'
        LAST_365D = '最近一年'

    def __init__(self, range_: Range):
        super().__init__(range_.name)
        

class TradeRatioStatisticsCache(StringCache):
    """
    交易比例统计
    """
    class Range(Enum):
        LAST_7D = '7d'
        LAST_30D = '30d'
        LAST_90D = '90d'

    def __init__(self, range_: Range):
        super().__init__(range_.value)
        
    @classmethod
    def reload(cls):
        """重新加载所有时间范围的数据"""
        end_time = today()
        for range_ in cls.Range:
            if range_ == cls.Range.LAST_7D:
                start_time = end_time - timedelta(days=7)
            elif range_ == cls.Range.LAST_30D:
                start_time = end_time - timedelta(days=30)
            elif range_ == cls.Range.LAST_90D:
                start_time = end_time - timedelta(days=90)
            else:
                continue
            data = cls.aggregate_trade_ratio_data(start_time, end_time)
            
            # 更新cache
            cache = cls(range_)
            cache.set(json.dumps(data, ensure_ascii=False))
    
    def get_data(self):
        """获取当前时间范围的数据"""
        data = self.read()
        if not data:
            return {}
        return json.loads(data)
    
    @classmethod
    def get_user_format_data(cls, records, vip_level_map, user_type: str):
        """
        格式化用户交易数据
        """
        
        vip_level_trade_map = defaultdict(Decimal)

        for user_id, item in records.items():
            if user_id in vip_level_map:
                level = vip_level_map[user_id]
            else:
                # vip表没有的用户
                level = 0
            if user_type == 'maker' and level == 0:
                level = 5   # 做市商level 0 合并进level 5
            vip_level_trade_map[level] += item.trade_amount
        
        all_vip_user_deal_amount = sum(vip_level_trade_map.values())
        all_taker_amount = sum([item.taker_amount for item in records.values()])
        all_maker_amount = sum([item.maker_amount for item in records.values()])
        ret = dict(
            taker_ratio=amount_to_str(safe_div(all_taker_amount, all_taker_amount + all_maker_amount) * 100, 2) + "%",
            maker_ratio=amount_to_str(safe_div(all_maker_amount, all_taker_amount + all_maker_amount) * 100, 2) + "%",
            all_taker_amount=amount_to_str((all_taker_amount), 2),
            all_maker_amount=amount_to_str((all_maker_amount), 2),
            level1_ratio=amount_to_str(safe_div(vip_level_trade_map[1], all_vip_user_deal_amount) * 100, 2) + "%",
            level2_ratio=amount_to_str(safe_div(vip_level_trade_map[2], all_vip_user_deal_amount) * 100, 2) + "%",
            level3_ratio=amount_to_str(safe_div(vip_level_trade_map[3], all_vip_user_deal_amount) * 100, 2) + "%",
            level4_ratio=amount_to_str(safe_div(vip_level_trade_map[4], all_vip_user_deal_amount) * 100, 2) + "%",
            level5_ratio=amount_to_str(safe_div(vip_level_trade_map[5], all_vip_user_deal_amount) * 100, 2) + "%",
        )
        if user_type == 'normal':
            ret['level0_ratio'] = amount_to_str(safe_div(vip_level_trade_map[0], all_vip_user_deal_amount) * 100, 2) + "%"
        return ret


    @classmethod
    def get_trade_data_by_type(cls, start_time, end_time, trade_type: str):
        """
        获取指定时间范围和交易类型的数据
        """
        
        # 获取做市商用户ID列表
        user_query = User.query.filter(User.user_type.in_([
            User.UserType.INTERNAL_MAKER,
            User.UserType.EXTERNAL_MAKER,
            User.UserType.EXTERNAL_SPOT_MAKER,
            User.UserType.EXTERNAL_CONTRACT_MAKER,
            User.UserType.EXTERNAL,
        ])).all()
        maker_user_ids = [item.id for item in user_query]

        # 获取交易数据
        records = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_time,
            UserTradeSummary.report_date <= end_time,
            UserTradeSummary.system == trade_type,
        ).group_by(
            UserTradeSummary.user_id,
        ).with_entities(
            UserTradeSummary.user_id,
            func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
            func.sum(UserTradeSummary.taker_amount).label('taker_amount'),
            func.sum(UserTradeSummary.maker_amount).label('maker_amount'),
        ).all()

        # 获取VIP用户映射
        vip_user_query = VipUser.query.with_entities(VipUser.user_id, VipUser.level).all()
        vip_level_map = {item.user_id: item.level for item in vip_user_query}
        
        # 获取做市商用户映射
        maker_user_query = MarketMaker.query.filter(MarketMaker.maker_type == trade_type).all()
        maker_level_map = {item.user_id: item.level for item in maker_user_query}
        
        # 分离普通用户和做市商用户数据
        normal_user_records = {item.user_id: item for item in records if item.user_id not in maker_user_ids}
        maker_user_records = {item.user_id: item for item in records if item.user_id in maker_user_ids}
        
        # 格式化数据
        normal_user_data = cls.get_user_format_data(normal_user_records, vip_level_map, 'normal')
        maker_user_data = cls.get_user_format_data(maker_user_records, maker_level_map, 'maker')
        
        return {
            "normal_user_data": [normal_user_data],
            "maker_user_data": [maker_user_data],
        }

    @classmethod
    def aggregate_trade_ratio_data(cls, start_time, end_time):
        """
        聚合现货和合约的交易比例数据
        """
        # 获取现货数据
        spot_data = cls.get_trade_data_by_type(start_time, end_time, 'spot')
        
        # 获取合约数据
        perpetual_data = cls.get_trade_data_by_type(start_time, end_time, 'perpetual')
        
        updated_at = current_timestamp(to_int=True)
        result = {
            "normal_user_data": {
                "spot": spot_data["normal_user_data"],
                "perpetual": perpetual_data["normal_user_data"]
            },
            "maker_user_data": {
                "spot": spot_data["maker_user_data"],
                "perpetual": perpetual_data["maker_user_data"]
            },
            "updated_at": updated_at,
        }
        
        return result


class StakingStatisticsCache(StringCache):
    
    """
    链上质押理财统计
    """
    def __init__(self, asset: str):
        super().__init__(asset)

class PledgeStatisticsCache(StringCache):
    """
    质押借贷统计
    """
    def __init__(self, loan_asset: str):
        super().__init__(loan_asset)

class PledgeStatisticsSumamryCache(StringCache):
    """
    质押借贷统计(汇总数据)
    """
    def __init__(self, loan_asset: str, pledge_asset: str):
        super().__init__(f'{loan_asset}:{pledge_asset}')


class AmbassadorStatisticsCache(StringCache):

    class AmbassadorType(Enum):
        ALL = "全部大使"
        NORMAL = "平台大使"
        BUS = "商务大使"

    class TimeRange(Enum):
        ALL = -1
        DAYS_7 = 7
        DAYS_30 = 30
        DAYS_90 = 90
        DAYS_180 = 180
        DAYS_365 = 365

    def __init__(self):
        super().__init__(None)

    def get_last_update_date(self):
        data = self.read()
        if not data:
            return None
        data = json.loads(data)
        if not data:
            return None
        return timestamp_to_date(data['ts'])

    def get_data(self, ambassador_type: AmbassadorType):
        data = self.read()
        if not data:
            return {}
        data = json.loads(data)
        if not data:
            return {}
        res = {'ts': data['ts']}
        for time_rage, time_range_data in data['data'].items():
            type_data = time_range_data[ambassador_type.name]
            res[time_rage] = {
                **time_range_data,
                **type_data,
            }
            for t in self.AmbassadorType:
                res[time_rage].pop(t.name)
        return res

