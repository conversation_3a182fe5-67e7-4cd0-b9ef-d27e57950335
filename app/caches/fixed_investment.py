# -*- coding: utf-8 -*-
import json
from collections import defaultdict

from app.models.fixed_investment import FixedInvestmentProduct
from app.caches.base import HashCache, ListCache
from app.utils.parser import JsonEncoder


class FixedInvestAssetRankCache(ListCache):
    """定期理财-币种排序缓存（币种列表）"""

    sort_types = (
        "",  # 默认顺序
        "apr_desc",  # 年化收益率降序
    )

    def __init__(self, sort_type: str):
        key = f'{sort_type}'
        self.sort_type = sort_type
        super().__init__(key)

    @classmethod
    def reload_by_asset_products_map(cls, asset_products_map: dict[str, list[dict]]):
        from app.business.utils import AssetComparator

        asset_products_list = list(asset_products_map.items())
        apr_desc_asset_products_list = list(sorted(asset_products_list, key=lambda x: max([i['base_apr'] for i in x[1]]), reverse=True))
        apr_desc_assets = [x[0] for x in apr_desc_asset_products_list]

        default_sorted_assets = list(sorted(apr_desc_assets, key=lambda x: AssetComparator(x)))
        cls("").save(default_sorted_assets)


class FixedInvestAssetProductsCache(HashCache):
    """定期理财-币种维度的产品 缓存"""
    MAX_SIZE = 10000

    def __init__(self):
        super().__init__(None)

    @classmethod
    def query_rows(cls) -> list[FixedInvestmentProduct]:
        rows: list[FixedInvestmentProduct] = FixedInvestmentProduct.query.filter(
            FixedInvestmentProduct.status == FixedInvestmentProduct.Status.ONLINE,
        ).limit(cls.MAX_SIZE).all()
        return rows

    @classmethod
    def reload(cls):
        """按币种维度展示product"""
        rows = cls.query_rows()
        asset_product_rows_map = defaultdict(list)
        for r in rows:
            asset_product_rows_map[r.asset].append(r)

        asset_products_map = {}
        for asset, asset_product_rows in asset_product_rows_map.items():
            products = []
            for product_row in asset_product_rows:
                product_row: FixedInvestmentProduct
                product_info = {
                    "product_id": product_row.id,
                    "lock_days": product_row.lock_days,
                    "base_apr": product_row.base_apr,
                    "min_subscription_amount": product_row.min_subscription_amount,
                }
                products.append(product_info)
            asset_products_map[asset] = products

        dump_data = {asset: json.dumps(ps, cls=JsonEncoder) for asset, ps in asset_products_map.items()}
        cls().save(dump_data)

        FixedInvestAssetRankCache.reload_by_asset_products_map(asset_products_map)

        return asset_products_map
