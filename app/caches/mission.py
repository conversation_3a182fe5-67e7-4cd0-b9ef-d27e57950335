import json
from collections import defaultdict
from typing import Any

from flask_babel import gettext as _, force_locale

from app.models.equity_center import EquityType
from app.models.user import LoginRelationHistory
from app.utils.date_ import current_timestamp
from app.utils.iterable import batch_iter
from .base import Hash<PERSON>ache, SetCache, StringCache
from .. import Language

from ..models.mission_center import MissionPlan, Mission, MonitorType, UserMissionMonitor
from ..utils.parser import JsonEncoder

USER_MISSION_BATCH_SIZE = 5000


class MissionCache(HashCache):
    """任务缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.mission_center.mission import MissionBiz
        from app.business.equity_center.helper import EquityCenterService
        from app.business.equity_center.inv_increase import IncreaseEquityHelper
        mission_query = Mission.query.join(
            MissionPlan, MissionPlan.id == Mission.plan_id
        ).filter(
            MissionPlan.status.in_([
                MissionPlan.Status.EFFECTIVE,
                MissionPlan.Status.STOPPED,
                MissionPlan.Status.FINISHED
            ])
        ).with_entities(
            Mission.id,
            Mission.mission_condition,
            Mission.logic_params,
            Mission.equity_id,
            Mission.sequence,
            Mission.deadline_days,
            MissionPlan.scene_type
        ).all()
        equity_ids = {i.equity_id for i in mission_query}
        equity_basic_info_mapper = EquityCenterService.batch_query_equity_basic_info(equity_ids)
        cache = cls()
        for mission in mission_query:
            equity_data = equity_basic_info_mapper.get(mission.equity_id, {})
            reward = dict(
                reward_type=equity_data["type"].name,
                value=equity_data["cost_amount"],
                value_type=equity_data["cost_asset"],
                cashback_asset=equity_data["cashback_asset"],
                increase_rate=equity_data["extra_data"].get("increase_rate", 0),
            )
            increase_rate = equity_data["extra_data"].get("increase_rate", 0)
            if equity_data["type"] == EquityType.INVEST_INCREASE:
                reward["value"], reward["value_type"] = IncreaseEquityHelper.format_value_and_type(increase_rate)
            reward["increase_rate"] = increase_rate

            mission_data = dict(
                mission_id=mission.id,
                mission_condition=mission.mission_condition.name,
                logic_params=mission.logic_params,
                sequence=mission.sequence,
                deadline_days=mission.deadline_days,
                scene_type=mission.scene_type.name,
                equity_id=mission.equity_id,
                reward=reward,
                mission_extra=MissionBiz.get_mission_extra_by_condition(mission.mission_condition),
            )
            cache.hset(mission.id, json.dumps(mission_data, cls=JsonEncoder))

    @classmethod
    def get_cache_data_by_ids(cls, mission_ids: list):
        if not mission_ids:
            return {}
        return {k: json.loads(v) for k, v in cls().hmget_with_keys(mission_ids)}

    @classmethod
    def get_all_cache_data(cls):
        return {k: json.loads(v) for k, v in cls().hgetall().items()}

    @classmethod
    def get_data_by_id(cls, mission_id: int):
        cache = cls()
        value = cache.hget(str(mission_id))
        if not value:
            return {}
        return json.loads(value)

    @classmethod
    def get_mission_by_reward_type(cls, reward_type: str):
        cache = cls()
        value = cache.hgetall()
        if not value:
            return []
        result = []
        for k, v in value.items():
            data = json.loads(v)
            if data['reward']['reward_type'] != reward_type:
                continue
            result.append(k)
        return result
    
    @classmethod
    def get_mission_by_condition(cls, mission_condition: str):
        cache = cls()
        value = cache.hgetall()
        if not value:
            return []
        result = []
        for k, v in value.items():
            data = json.loads(v)
            if data['mission_condition'] != mission_condition:
                continue
            result.append(k)
        return result


class MissionContentCache(HashCache):
    """任务投放内容缓存"""

    REFER_FINISHED_TEXT = _("活动参与人数已满")

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from ..business.mission_center.plan import MissionPlanBiz
        # normal
        plan_mapper = {
            plan_id: lang_content for plan_id, lang_content in MissionPlan.query.filter(
                MissionPlan.status == MissionPlan.Status.EFFECTIVE,
            ).with_entities(
                MissionPlan.id,
                MissionPlan.deliver_content
            ).all()
        }
        # special
        finished_display_plans = MissionPlanBiz.query_finished_display_plans()
        finished_content_mapper = defaultdict(dict)
        for plan_id in finished_display_plans:
            for lang in Language:
                with force_locale(lang.value):
                    finished_content_mapper[plan_id][lang.name] = _(cls.REFER_FINISHED_TEXT)

        cache = cls()
        update_plan_keys = plan_mapper.keys()
        current_keys = cache.hkeys()
        for k, data in plan_mapper.items():
            cache.hset(k, json.dumps(data, cls=JsonEncoder))
        for k, data in finished_content_mapper.items():
            cache.hset(k, json.dumps(data, cls=JsonEncoder))
        update_keys = list(update_plan_keys) + list(finished_display_plans)
        delete_keys = set(current_keys) - set(map(str, update_keys))
        if delete_keys:
            cache.hdel(*delete_keys)

    @classmethod
    def get_lang_data(cls, plan_id: int, lang: Language) -> str:
        cache = cls()
        lang_data = cache.hget(str(plan_id))
        if not lang_data:
            return ""
        return json.loads(lang_data).get(lang.name, "")

    @classmethod
    def delete_plan_data(cls, plan_id: int) -> None:
        cache = cls()
        cache.hdel(str(plan_id))

    @classmethod
    def update_one(cls, plan_id: int, deliver_content: dict):
        cls().hset(str(plan_id), json.dumps(deliver_content, cls=JsonEncoder))


class SendNoticeCache(SetCache):
    """任务中心已发放消息缓存"""

    def __init__(self, notice_type: str):
        super().__init__(notice_type)

    def get_ids(self) -> set[int]:
        return {int(s) for s in self.smembers()}

    def has_id(self, user_mission_id: int) -> bool:
        return self.sismember(str(user_mission_id))

    def add_id(self, user_mission_id: int):
        self.sadd(str(user_mission_id))

    def add_ids(self, user_mission_ids: list):
        if user_mission_ids:
            self.sadd(*[str(i) for i in user_mission_ids])

    def delete_ids(self, user_mission_ids: list):
        self.srem(*[str(i) for i in user_mission_ids])


class MissionUserNoticeCache(StringCache):
    """ 用户任务触达通知缓存, 24小时内只通知一次 """

    TTL = 3600 * 24

    def __init__(self, user_id: int, notice_type: str):
        super().__init__(f'{user_id}:{notice_type}')

    def gen(self):
        self.value = '1'
        self.expire(self.TTL)


class MonitorUserMissionCache(HashCache):
    """任务中心监控用户任务缓存"""

    def __init__(self):
        """
        1. 任务中心监控用户任务缓存
        2. 缓存数据结构: {user_mission_monitor_id: {
            "user_id": user_id, # 用户id 
            "used_at": used_at, # 监控生效时间
            "created_at": created_at, # 监控创建时间
            "monitor_type": monitor_type, # 监控条件
            "expired_at": expired_at, # 监控过期时间
        }}
        3. 缓存数据更新时机:
            1. 新增的用户任务监控数据
            2. 监控完成或者过期的时候
        """
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.mission_center.monitor import UserMissionMonitorBiz
        from app.business.mission_center.plan import MissionPlanBiz
        # 获取所有处理中的计划ID
        plan_ids = MissionPlanBiz.query_processing_plan_ids()

        cache_instance = cls()
        update_keys = set()
        for plan_id in plan_ids:
            config_data = UserMissionMonitorBiz.get_monitor_config(plan_id)
            if not config_data:
                continue
            for chunk_ids in batch_iter(config_data.keys(), USER_MISSION_BATCH_SIZE):
                chunk_data = {k: config_data[k] for k in chunk_ids}
                cache_instance.hmset({str(k): json.dumps(v, cls=JsonEncoder) for k, v in chunk_data.items()})
                update_keys.update(chunk_data.keys())

        # 现有的keys
        current_keys = set(map(int, cache_instance.hkeys()))
        delete_keys = set(current_keys) - update_keys
        if not delete_keys:
            return
        for chunk_ids in batch_iter(delete_keys, USER_MISSION_BATCH_SIZE):
            cache_instance.hdel(*chunk_ids)

    @classmethod
    def update_user_mission_monitors(cls, user_mission_monitors: list[UserMissionMonitor]):
        """
        更新用户任务数据(更新时机)
        1. 新增的用户任务数据
        2. 被kafka监听到的用户任务数据
        不存在并发更新的问题，暂时不用加锁。
        """
        from app.business.mission_center.monitor import UserMissionMonitorBiz
        if not user_mission_monitors:
            return
        mission_user_mappers = UserMissionMonitorBiz.format_monitor_config(user_mission_monitors)
        cache_instance = cls()
        for chunk_ids in batch_iter(mission_user_mappers.keys(), USER_MISSION_BATCH_SIZE):
            chunk_data = {k: mission_user_mappers[k] for k in chunk_ids}
            cache_instance.hmset({str(k): json.dumps(v, cls=JsonEncoder) for k, v in chunk_data.items()})

    @classmethod
    def remove_user_mission_monitors(cls, user_mission_monitor_ids: list[int]):
        """移除用户任务数据 任务完成或者过期的时候"""
        cache_instance = cls()
        for chunk_ids in batch_iter(user_mission_monitor_ids, USER_MISSION_BATCH_SIZE):
            cache_instance.hdel(*chunk_ids)

    @classmethod
    def get_config_data(cls) -> dict[MonitorType, dict[int, dict[str, Any]]]:
        result = defaultdict(lambda: defaultdict(dict))
        cache_instance = cls()
        for user_mission_monitor_id, user_mission_monitor_data in cache_instance.hgetall().items():
            load_data = json.loads(user_mission_monitor_data)
            user_id = load_data.pop("user_id")
            monitor_type = MonitorType[load_data.pop("monitor_type")]
            result[monitor_type][user_id].update({
                int(user_mission_monitor_id): load_data
            })
        return result

    @classmethod
    def get_middle_expiring_missions(cls) -> list[int]:
        """查询即将到期的任务"""
        now_timestamp = current_timestamp(to_int=True)
        for user_mission_monitor_id, user_mission_data in cls().hgetall().items():
            load_data = json.loads(user_mission_data)
            used_at = load_data.get('used_at')
            expired_at = load_data.get('expired_at')
            if not used_at or not expired_at:
                continue
            day_timestamp = (expired_at - used_at) / 2
            if now_timestamp >= used_at + day_timestamp:
                yield int(user_mission_monitor_id)


class MissionDeviceSnapshotCache(HashCache):
    """任务监控用户设备快照缓存"""

    def __init__(self, plan_id: int, risk_type: str = None):
        if risk_type:
            key = f"{plan_id}:{risk_type}"
        else:
            key = str(plan_id)
        super().__init__(key)

    def update_snapshot(self, user_ids: list[int]):
        device_risk_users_mapper = LoginRelationHistory.query_latest_duplicate_device_users(set(user_ids))
        if device_risk_users_mapper:
            self.hmset(device_risk_users_mapper)
        self.hset('is_finished', '1')

    def update_new_selected_users(self, user_ids: list[int]):
        update_user_ids = set(user_ids) - {int(k) for k in self.hkeys() if k != 'is_finished'}
        device_risk_users_mapper = LoginRelationHistory.query_latest_duplicate_device_users(update_user_ids)
        if device_risk_users_mapper:
            self.hmset(device_risk_users_mapper)

    @classmethod
    def get_snapshot(cls, plan_id: int):
        cache_instance = cls(plan_id)
        assert cache_instance.check_update_finished(), '任务监控用户设备快照缓存未更新完成'
        return {int(k): v for k, v in cache_instance.hgetall().items() if k != 'is_finished'}

    def check_update_finished(self):
        is_finished = self.hget('is_finished')
        if is_finished:
            return True
        return False
    
    def get_risked_device_user_id_mapper(self):
        """发奖一个设备只会给一个用户发"""
        return {v: int(k) for k, v in self.hgetall().items() if k != 'is_finished'}

    @classmethod
    def clear_caches(cls, plan_id, risk_type):
        cls(plan_id).delete()
        cls(plan_id, risk_type).delete()


class MissionRiskedUsersCache(HashCache):
    """任务中心被风控用户缓存"""

    def __init__(self, plan_id: int, risk_key: str):
        key = f"{plan_id}:{risk_key}"
        super().__init__(str(key))

    def update_risked_users(self, user_value_mapper: dict[int, str]):
        self.hmset({str(i): user_value for i, user_value in user_value_mapper.items()})

    def get_risked_users(self) -> dict[int, str]:
        return {int(k): v for k, v in self.hgetall().items()}