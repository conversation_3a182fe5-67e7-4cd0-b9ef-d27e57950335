#!/usr/bin/python
# -*- coding: utf-8 -*-
from pprint import pprint

import pytest
from flask import g

from app.models.user import User
from app.models.investment import InvStatisticTimeRange
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestInvStatisticReload:

    def test_inv_reload(self, tcontext):
        with tcontext:
            from app.caches.investment import InvestmentStatisticCache
            result = InvestmentStatisticCache.reload_all()
            pprint(result)

    def test_fix_reload(self, tcontext):
        with tcontext:
            from app.caches.investment import FixedInvStatisticCache
            FixedInvStatisticCache.reload_all()

            item = FixedInvStatisticCache(InvStatisticTimeRange.DAYS_7).get_data()
            pprint(item)

    def test_reload_all(self, tcontext):
        with tcontext:
            from app.caches.investment import FixedInvStatisticCache
            from app.caches.investment import InvestmentStatisticCache
            result = InvestmentStatisticCache.reload_all()
            pprint(result)
            result = FixedInvStatisticCache.reload_all()
            pprint(result)


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestInvestmentStatistic:

    def test_get_investment_statistic_success(self, tcontext):
        """测试获取理财统计成功"""

        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get('/admin/statistic/investment')
            pprint(resp.json)
            assert resp.json['code'] == 0
            data = resp.json['data']
            
            # 检查记录数量
            assert len(data['records']) > 0
            # 检查第一条记录（总计行）
            total_record = data['records'][0]
            assert total_record['asset'] == 'ALL'

    def test_get_investment_statistic_with_asset_filter(self, tcontext):
        """测试币种筛选"""
        
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get('/admin/statistic/investment', query_string={'asset': 'USDT'})
            pprint(resp.json)
            assert resp.json['code'] == 0
            data = resp.json['data']
            records = data['records']
            assert len(records) == 2  # 总计行 + BTC行
            assert records[1]['asset'] == 'USDT'

    def test_get_investment_statistic_with_investment_type_filter(self, tcontext):
        """测试理财类型筛选"""
        
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get('/admin/statistic/investment', query_string={'investment_type': 'CURRENT'})
            pprint(resp.json)

            assert resp.json['code'] == 0
            data = resp.json['data']
            
            assert isinstance(data, dict)
            records = data['records']
            # 活期理财应该包含所有开启的币种
            assert len(records) >= 2

    def test_get_investment_statistic_with_time_range(self, tcontext):
        """测试时间段筛选"""
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get('/admin/statistic/investment', query_string={'time_range': 'DAYS_30'})
            pprint(resp.json)

            assert resp.json['code'] == 0
            data = resp.json['data']
            
            assert isinstance(data, dict)
            for field in ['records', 'update_time']:
                assert field in data
        
    def test_get_fixed_investment_statistic_success(self, tcontext):
        """测试获取定期理财统计成功"""
        
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get('/admin/statistic/investment', query_string={'investment_type': 'FIXED'})
            
            assert resp.json['code'] == 0
            data = resp.json['data']
            
            assert isinstance(data, dict)
            for field in ['records', 'update_time']:
                assert field in data
            
            # 检查记录
            records = data['records']
            assert len(records) > 0
            
            # 检查第一条记录（总计行）
            total_record = records[0]
            assert total_record['asset'] == 'ALL'
            assert total_record['ladder_usd'] == '0'
