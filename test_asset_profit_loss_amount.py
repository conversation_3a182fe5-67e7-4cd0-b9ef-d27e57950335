#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AssetProfitLossHistory表的amount字段超长情况
使用直接的MySQL连接，不依赖Flask应用
"""

import pymysql
from datetime import date
import os

def get_db_connection():
    """获取数据库连接"""
    # 从环境变量或配置中获取数据库连接信息
    # 这里使用测试环境的配置
    return pymysql.connect(
        host='vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
        user='uat_coinex_backend_db_user',
        password='lVLG96Yvstxb0nez',
        database=f'coinex_backend_{os.getenv("ENV_NUM", "1")}',
        charset='utf8mb4',
        autocommit=False
    )

def test_amount_field_limits():
    """测试amount字段的长度限制"""

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        print("开始测试AssetProfitLossHistory的amount字段超长情况...")

        # 测试用例数据
        test_cases = [
            {
                "name": "正常精度 - 12位小数",
                "amount": "123456789012345.123456789012",  # 26位总长度，12位小数
                "should_succeed": True
            },
            {
                "name": "最大精度 - 12位小数",
                "amount": "99999999999999.999999999999",  # 26位总长度，12位小数
                "should_succeed": True
            },
            {
                "name": "超长整数部分",
                "amount": "999999999999999.123456789012",  # 27位总长度，12位小数
                "should_succeed": False
            },
            {
                "name": "超长小数部分",
                "amount": "123456789012345.1234567890123",  # 26位总长度，13位小数
                "should_succeed": False
            },
            {
                "name": "极大数值",
                "amount": "99999999999999999999999999.999999999999",  # 超长
                "should_succeed": False
            },
            {
                "name": "零值",
                "amount": "0.000000000000",
                "should_succeed": True
            },
            {
                "name": "小数值",
                "amount": "0.000000000001",
                "should_succeed": True
            }
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {test_case['name']}")
            print(f"测试数值: {test_case['amount']}")

            try:
                # 插入SQL语句
                insert_sql = """
                INSERT INTO asset_profit_loss_history
                (asset, user_id, report_date, profit, price, amount, type, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """

                cursor.execute(insert_sql, (
                    "BTC",
                    1,
                    date.today(),
                    "100.12345678",
                    "50000.123456789012",
                    test_case['amount'],
                    "system"
                ))

                # 提交事务
                conn.commit()

                # 获取插入的记录ID
                record_id = cursor.lastrowid

                if test_case['should_succeed']:
                    print(f"✅ 成功插入记录，ID: {record_id}")
                else:
                    print(f"⚠️  预期失败但成功插入了记录，ID: {record_id}")

            except Exception as e:
                # 回滚事务
                conn.rollback()

                if test_case['should_succeed']:
                    print(f"❌ 预期成功但插入失败: {str(e)}")
                else:
                    print(f"✅ 预期失败，插入确实失败: {str(e)}")

            print("-" * 60)

        print("\n测试完成！")

        # 清理测试数据
        print("\n清理测试数据...")
        try:
            delete_sql = """
            DELETE FROM asset_profit_loss_history
            WHERE asset = 'BTC' AND user_id = 1 AND report_date = %s
            """
            cursor.execute(delete_sql, (date.today(),))
            conn.commit()
            print("✅ 测试数据清理完成")
        except Exception as e:
            conn.rollback()
            print(f"⚠️  清理测试数据时出错: {str(e)}")

    finally:
        cursor.close()
        conn.close()

def show_field_info():
    """显示字段信息"""
    print("AssetProfitLossHistory.amount字段信息:")
    print("- 类型: MYSQL_DECIMAL_PRICE")
    print("- 精度: DECIMAL(precision=26, scale=12)")
    print("- 说明: 总共26位数字，其中12位小数")
    print("- 最大值: 99999999999999.999999999999")
    print("- 最小值: -99999999999999.999999999999")
    print()

if __name__ == "__main__":
    show_field_info()
    test_amount_field_limits()
