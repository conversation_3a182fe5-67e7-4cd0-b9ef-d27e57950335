#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AssetProfitLossHistory表amount字段的极端情况
包括可能导致错误的边界情况
"""

import pymysql
from datetime import date
import os

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
        user='uat_coinex_backend_db_user',
        password='lVLG96Yvstxb0nez',
        database=f'coinex_backend_{os.getenv("ENV_NUM", "1")}',
        charset='utf8mb4',
        autocommit=False
    )

def test_extreme_decimal_cases():
    """测试极端的DECIMAL情况"""
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("测试AssetProfitLossHistory amount字段的极端情况...")
        print("字段定义: DECIMAL(26, 12)")
        print("=" * 80)
        
        # 极端测试用例
        extreme_cases = [
            {
                "name": "NULL值",
                "amount": None,
                "description": "测试NULL值是否被接受"
            },
            {
                "name": "空字符串",
                "amount": "",
                "description": "测试空字符串"
            },
            {
                "name": "非数字字符串",
                "amount": "abc123",
                "description": "测试非数字字符串"
            },
            {
                "name": "包含特殊字符",
                "amount": "123.45$",
                "description": "测试包含货币符号"
            },
            {
                "name": "多个小数点",
                "amount": "123.45.67",
                "description": "测试格式错误的数字"
            },
            {
                "name": "负数最大值",
                "amount": "-99999999999999.999999999999",
                "description": "测试负数的最大绝对值"
            },
            {
                "name": "超出负数范围",
                "amount": "-999999999999999.999999999999",
                "description": "测试超出负数范围"
            },
            {
                "name": "正无穷",
                "amount": "inf",
                "description": "测试无穷大"
            },
            {
                "name": "负无穷",
                "amount": "-inf",
                "description": "测试负无穷大"
            },
            {
                "name": "NaN",
                "amount": "nan",
                "description": "测试非数字值"
            },
            {
                "name": "超长字符串",
                "amount": "1" * 100 + "." + "2" * 100,
                "description": "测试超长数字字符串"
            },
            {
                "name": "前导零",
                "amount": "000000000000001.123456789012",
                "description": "测试前导零"
            },
            {
                "name": "科学计数法负指数",
                "amount": "1.23456789012E-20",
                "description": "测试极小的科学计数法"
            },
            {
                "name": "科学计数法大指数",
                "amount": "1.23456789012E+50",
                "description": "测试极大的科学计数法"
            }
        ]
        
        successful_inserts = []
        failed_inserts = []
        
        for i, test_case in enumerate(extreme_cases, 1):
            print(f"\n测试用例 {i}: {test_case['name']}")
            print(f"测试值: {test_case['amount']}")
            print(f"描述: {test_case['description']}")
            
            try:
                # 插入SQL语句
                insert_sql = """
                INSERT INTO asset_profit_loss_history 
                (asset, user_id, report_date, profit, price, amount, type, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                cursor.execute(insert_sql, (
                    "EXTREME",
                    888888,  # 使用特殊用户ID便于清理
                    date.today(),
                    "100.12345678",
                    "50000.123456789012",
                    test_case['amount'],
                    "system"
                ))
                
                # 提交事务
                conn.commit()
                
                # 获取插入的记录ID
                record_id = cursor.lastrowid
                successful_inserts.append(record_id)
                
                # 查询实际存储的数值
                select_sql = "SELECT amount FROM asset_profit_loss_history WHERE id = %s"
                cursor.execute(select_sql, (record_id,))
                stored_value = cursor.fetchone()[0]
                
                print(f"✅ 成功插入记录，ID: {record_id}")
                print(f"实际存储值: {stored_value}")
                
            except Exception as e:
                # 回滚事务
                conn.rollback()
                failed_inserts.append({
                    'case': test_case['name'],
                    'value': test_case['amount'],
                    'error': str(e)
                })
                print(f"❌ 插入失败: {str(e)}")
            
            print("-" * 60)
        
        # 总结结果
        print(f"\n测试总结:")
        print(f"成功插入: {len(successful_inserts)} 条")
        print(f"插入失败: {len(failed_inserts)} 条")
        
        if failed_inserts:
            print(f"\n失败的测试用例:")
            for failed in failed_inserts:
                print(f"- {failed['case']}: {failed['value']} -> {failed['error']}")
        
        if successful_inserts:
            print(f"\n成功插入的记录详情:")
            print("ID\t\t存储值")
            print("-" * 40)
            for record_id in successful_inserts:
                cursor.execute("SELECT amount FROM asset_profit_loss_history WHERE id = %s", (record_id,))
                stored_value = cursor.fetchone()[0]
                print(f"{record_id}\t\t{stored_value}")
        
        # 清理测试数据
        print(f"\n清理测试数据...")
        try:
            delete_sql = """
            DELETE FROM asset_profit_loss_history 
            WHERE asset = 'EXTREME' AND user_id = 888888 AND report_date = %s
            """
            cursor.execute(delete_sql, (date.today(),))
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"✅ 清理完成，删除了 {deleted_count} 条测试记录")
        except Exception as e:
            conn.rollback()
            print(f"⚠️  清理测试数据时出错: {str(e)}")
    
    finally:
        cursor.close()
        conn.close()

def show_test_summary():
    """显示测试总结"""
    print("AssetProfitLossHistory amount字段极端情况测试")
    print("目标: 找出可能导致插入失败的边界情况")
    print("字段类型: DECIMAL(26, 12)")
    print("预期行为:")
    print("- 正常数值: 成功插入")
    print("- 超出精度: MySQL自动调整")
    print("- 无效格式: 插入失败并抛出异常")
    print("- NULL值: 取决于字段是否允许NULL")
    print()

if __name__ == "__main__":
    show_test_summary()
    test_extreme_decimal_cases()
