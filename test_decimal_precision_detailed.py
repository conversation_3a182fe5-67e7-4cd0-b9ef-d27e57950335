#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试AssetProfitLossHistory表的amount字段精度处理
查看MySQL如何处理超出精度的DECIMAL数值
"""

import pymysql
from datetime import date
import os

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
        user='uat_coinex_backend_db_user',
        password='lVLG96Yvstxb0nez',
        database=f'coinex_backend_{os.getenv("ENV_NUM", "1")}',
        charset='utf8mb4',
        autocommit=False
    )

def test_decimal_precision_detailed():
    """详细测试DECIMAL字段的精度处理"""
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("详细测试AssetProfitLossHistory的amount字段精度处理...")
        print("字段定义: DECIMAL(26, 12) - 总共26位数字，其中12位小数")
        print("=" * 80)
        
        # 测试用例数据
        test_cases = [
            {
                "name": "标准精度内",
                "amount": "12345678901234.123456789012",  # 14位整数 + 12位小数 = 26位
                "expected_behavior": "完全保存"
            },
            {
                "name": "最大整数位数",
                "amount": "99999999999999.123456789012",  # 14位整数 + 12位小数 = 26位
                "expected_behavior": "完全保存"
            },
            {
                "name": "超出整数位数",
                "amount": "999999999999999.123456789012",  # 15位整数 + 12位小数 = 27位
                "expected_behavior": "可能截断或报错"
            },
            {
                "name": "超出小数位数",
                "amount": "12345678901234.1234567890123",  # 14位整数 + 13位小数
                "expected_behavior": "小数部分截断到12位"
            },
            {
                "name": "大幅超出精度",
                "amount": "123456789012345678901234567890.123456789012345678901234567890",
                "expected_behavior": "可能截断或报错"
            },
            {
                "name": "科学计数法",
                "amount": "1.23456789012E+13",  # 科学计数法
                "expected_behavior": "转换为普通数值"
            },
            {
                "name": "极小数值",
                "amount": "0.000000000001",  # 12位小数
                "expected_behavior": "完全保存"
            },
            {
                "name": "超小数值",
                "amount": "0.0000000000001",  # 13位小数
                "expected_behavior": "截断到12位小数"
            }
        ]
        
        inserted_ids = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {test_case['name']}")
            print(f"输入数值: {test_case['amount']}")
            print(f"预期行为: {test_case['expected_behavior']}")
            
            try:
                # 插入SQL语句
                insert_sql = """
                INSERT INTO asset_profit_loss_history 
                (asset, user_id, report_date, profit, price, amount, type, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                cursor.execute(insert_sql, (
                    "TEST",
                    999999,  # 使用特殊用户ID便于清理
                    date.today(),
                    "100.12345678",
                    "50000.123456789012",
                    test_case['amount'],
                    "system"
                ))
                
                # 提交事务
                conn.commit()
                
                # 获取插入的记录ID
                record_id = cursor.lastrowid
                inserted_ids.append(record_id)
                
                # 查询实际存储的数值
                select_sql = "SELECT amount FROM asset_profit_loss_history WHERE id = %s"
                cursor.execute(select_sql, (record_id,))
                stored_value = cursor.fetchone()[0]
                
                print(f"✅ 成功插入记录，ID: {record_id}")
                print(f"实际存储值: {stored_value}")
                
                # 比较输入和存储的值
                if str(stored_value) == test_case['amount']:
                    print("🎯 存储值与输入值完全一致")
                else:
                    print("⚠️  存储值与输入值不同 - MySQL进行了精度调整")
                    
            except Exception as e:
                # 回滚事务
                conn.rollback()
                print(f"❌ 插入失败: {str(e)}")
            
            print("-" * 60)
        
        print(f"\n测试完成！共插入 {len(inserted_ids)} 条记录")
        
        # 显示所有插入的记录
        if inserted_ids:
            print("\n所有测试记录的存储情况:")
            print("ID\t\t存储的amount值")
            print("-" * 40)
            
            for record_id in inserted_ids:
                cursor.execute("SELECT amount FROM asset_profit_loss_history WHERE id = %s", (record_id,))
                stored_value = cursor.fetchone()[0]
                print(f"{record_id}\t\t{stored_value}")
        
        # 清理测试数据
        print(f"\n清理测试数据...")
        try:
            delete_sql = """
            DELETE FROM asset_profit_loss_history 
            WHERE asset = 'TEST' AND user_id = 999999 AND report_date = %s
            """
            cursor.execute(delete_sql, (date.today(),))
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"✅ 清理完成，删除了 {deleted_count} 条测试记录")
        except Exception as e:
            conn.rollback()
            print(f"⚠️  清理测试数据时出错: {str(e)}")
    
    finally:
        cursor.close()
        conn.close()

def show_mysql_decimal_info():
    """显示MySQL DECIMAL类型的相关信息"""
    print("MySQL DECIMAL(26, 12) 字段信息:")
    print("- precision=26: 总共可以存储26位数字")
    print("- scale=12: 其中12位是小数部分")
    print("- 因此整数部分最多14位 (26-12=14)")
    print("- 最大正值: 99999999999999.999999999999")
    print("- 最小负值: -99999999999999.999999999999")
    print("- 当输入超出精度时，MySQL会:")
    print("  * 小数部分超出：四舍五入到指定小数位数")
    print("  * 整数部分超出：可能报错或截断（取决于SQL模式）")
    print()

if __name__ == "__main__":
    show_mysql_decimal_info()
    test_decimal_precision_detailed()
